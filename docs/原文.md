Automating Zero-Shot Patch Porting for Hard Forks
Shengyi Pan
The State Key Laboratory of
Blockchain and Data Security,
Zhejiang University
Hangzhou, China
<EMAIL>
<PERSON> University
Hangzhou, China
<EMAIL>
<PERSON><PERSON><PERSON>
The State Key Laboratory of
Blockchain and Data Security,
Zhejiang University
Hangzhou, China
<EMAIL>
Xing Hu
The State Key Laboratory of
Blockchain and Data Security,
Zhejiang University
Ningbo, China
<EMAIL>
Xin Xia
Huawei
Hangzhou, China
<EMAIL>
Shanping Li
The State Key Laboratory of
Blockchain and Data Security,
Zhejiang University
Hangzhou, China
<EMAIL>
ABSTRACT
Forking is a typical way of code reuse, which provides a simple
way for developers to create a variant software (denoted as hard
fork) by copying and modifying an existing codebase. Despite of
the benefits, forking also leads to duplicate efforts in software maintenance.
Developers need to port patches across the hard forks
to address similar bugs or implement similar features. Due to the
divergence between the source project and the hard fork, patch
porting is complicated, which requires an adaption regarding different
implementations of the same functionality. In this work, we
take the first step to automate patch porting for hard forks under a
zero-shot setting.We first conduct an empirical study of the patches
ported from Vim to Neovim over the last ten years to investigate
the necessities of patch porting and the potential flaws in the current
practice.We then propose a large language model (LLM) based
approach (namely PPatHF) to automatically port patches for hard
forks on a function-wise basis. Specifically, PPatHF is composed
of a reduction module and a porting module. Given the pre- and
post-patch versions of a function from the reference project and
the corresponding function from the target project, the reduction
module first slims the input functions by removing code snippets
less relevant to the patch. Then, the porting module leverages a
LLM to apply the patch to the function from the target project. To
better elicit the power of the LLM on patch porting, we design a
prompt template to enable efficient in-context learning. We further
propose an instruction-tuning based training task to better guide
the LLM to port the patch and inject task-specific knowledge. We
evaluate PPatHF on 310 Neovim patches ported from Vim. The
experimental results show that PPatHF outperforms the baselines
∗Corresponding Author
Permission to make digital or hard copies of all or part of this work for personal or
classroom use is granted without fee provided that copies are not made or distributed
for profit or commercial advantage and that copies bear this notice and the full citation
on the first page. Copyrights for components of this work owned by others than the
author(s) must be honored. Abstracting with credit is permitted. To copy otherwise, or
republish, to post on servers or to redistribute to lists, requires prior specific permission
and/or a fee. Request <NAME_EMAIL>.
ISSTA ’24, September 16–20, 2024, Vienna, Austria
© 2024 Copyright held by the owner/author(s). Publication rights licensed to ACM.
ACM ISBN 979-8-4007-0612-7/24/09
https://doi.org/10.1145/3650212.3652134
significantly. Specifically, PPatHF can correctly port 131 (42.3%)
patches and automate 57% of the manual edits required for the
developer to port the patch.
CCS CONCEPTS
• Software and its engineering→Automatic programming;
Software maintenance tools.
KEYWORDS
Patch Porting, Hard Fork, Large Language Model
ACM Reference Format:
Shengyi Pan, You Wang, Zhongxin Liu, Xing Hu, Xin Xia, and Shanping Li.
2024. Automating Zero-Shot Patch Porting for Hard Forks. In Proceedings
of the 33rd ACM SIGSOFT International Symposium on Software Testing and
Analysis (ISSTA ’24), September 16–20, 2024, Vienna, Austria. ACM, New
York, NY, USA, 13 pages. https://doi.org/10.1145/3650212.3652134
1 INTRODUCTION
Forking [30, 64] (a.k.a. clone-and-own) is widely used in both opensource
[28, 34] and industry [25] development, which provides a
straightforward and low-cost way for developers to reuse an existing
project and tailor it to their own requirements. In recent years,
the rise of social-coding platforms (e.g., GitHub) actively enhances
and promotes the ability to reuse existing software via fork-based
development paradigm [20, 21, 66]. Specifically, we follow Zhou et
al. [64–66] to refer to the traditional notion of reusing an existing
codebase and splitting off an independent variant for new development
directions as hard forks. This stands in contrast to a new form
of forks (namely social forks) that are for isolated development with
the intention of contributing back to the mainline [31, 32].
Despite the perceived benefits, such as the simplicity and flexibility
in creating customized variants of existing softwares, forking
also inevitably leads to duplicate efforts in software development
and maintenance [20, 44, 48]. Specifically, since the design logic and
possibly a large portion of code is reused due to the initial copying,
similar bug fixes and feature implications need to be performed
redundantly across a family of hard forks. Moreover, propagating
changes across the hard forks is usually not straightforward and an
error or delayed propagation can possibly compromise the software
arXiv:2404.17964v1 [cs.SE] 27 Apr 2024
ISSTA ’24, September 16–20, 2024, Vienna, Austria Shengyi Pan, You Wang, Zhongxin Liu, Xing Hu, Xin Xia, and Shanping Li
quality. Although the source project and the hard forks shares a
common codebase initially, their implementations typically diverge
over time as the hard forks are intended to be an independent
project with a different development direction [21, 66]. Thus, when
a patch is committed to the source project (or the hard fork), it
is not directly applicable (i.e., through a copy-and-paste process)
to the hard fork (or the source project). Instead, porting patches
across the family of hard forks requires an adaption regarding different
implementations of the same protocol or functionality, which
typically involves identifying the correct location and adjusting
the patch according to differences in implementations. Currently,
patches are ported across hard forks manually by the developer
on a case-by-case basis, which is time-consuming and often errorprone
[44, 45]. Several recent empirical works [20, 66] point out
the necessities for developing tools to facilitate coordination of
changes across hard forks, as the existing tools (e.g., git apply)
fail to handle the significant divergence of hard forks. Moreover,
the manual porting process typically faces a long delay [44], which
can be serious when the patch is related to security. Specifically,
once a software vulnerability (SV) is patched and publicly disclosed
for one of the projects among the hard fork family, malicious actors
can easily spot and turn the SV into zero-day attacks against other
un-patched projects within the family. Reid et al. [46] recently first
investigate the security risks caused by the code reuse through direct
file-level duplication. They report that orphan SVs (i.e., known
SVs that have been fixed, but copies still remain vulnerable) are
widespread in the OSS ecosystem and often take a long time to
be fixed. Our preliminary study (see Section 2.2) demonstrates the
widespread existence of such security risks within hard forks.
In this work, we aim to fill the gap by automating the patch
porting for hard forks. In practice, it is non-trivial and usually timeconsuming
to collect a significant number of historically ported
patches between the source project and the hard fork. The ported
patches may lack clear documentations in commit messages or
being buried in a large commit. Thus, to ensure the generalizability
and practicality of the proposed approach, we focus on a zero-shot
setting, i.e., without knowing any historically ported patches.
To understand the necessities of patch porting and the potential
flaws in the existing practice, we first conduct an empirical study
on Vim [5] and Neovim [4]. Vim is a widely-used text editor and
Neovim is one of Vim’s most popular hard forks with 71.5K stars
on GitHub. We find that ❶ Neovim has been porting patches from
Vim for nearly 10 years since its creation and the porting rate
does not decrease over time. Over 45% and 75% of the total and
security patches committed in Vim have been ported into Neovim,
respectively. SVs are only disclosed and recorded for Vim in public
SV databases (e.g., NVD), which forces users of Neovim to actively
and promptly port the security patches. ❷ In current practice, the
porting is manually done by developers periodically. Large porting
delays are common, even for high-severity SVs. For example, over
50% of the ported patches are delayed for over 180 days. The porting
delay of SV fixes brings serious security risks to Neovim.
It is challenging to automate the patch porting in hard forks,
especially under a zero-shot setting. Specifically, we observe two
key challenges: ❶ The implementations of hard forks can diverge
significantly from the source project after a long time evolution
(see Section 2.1). Without a correct understanding of the patch
semantics, it is impossible to port the patch with both implementation
differences considered and the modification logic maintained.
❷ Under the zero-shot setting, only the information of the patch
from the source project is available and no extra information, such
as historically ported patch pairs and relevant test cases, is provided.
The superiority of recent rising large language models (LLM) in
understanding the code semantics [22, 61], as well as its strong generalizability
under the zero-shot setting, makes it an ideal solution
to tackle the aforementioned two challenges.
Thus, we propose a LLM-based approach (namely PPatHF) to
automatically port patches for hard forks. Given a patch in the
reference project (typically the source project), we port the patch
to the target project (typically the hard fork) on a function-wise
basis. PPatHF contains two modules, i.e., the reduction module
and the porting module. The reduction module first slims the input
functions by removing contexts that are less relevant to the patch,
enabling PPatHF to port more patches under the length limit of
the LLM. Then, the porting module leverages a LLM to adapt the
patch to the hard fork. The LLM can extract common patch logic
beyond implementation differences and automate porting at the
semantic level. To effectively instruct the LLM to port patches,
we design a prompt template to elicit the power of the LLM on
the patch porting task by enabling efficient in-context learning
(ICL) [63]. Moreover, we design an instruction-tuning [57] based
training task to further guide the LLM to port the patch and inject
project-specific knowledge. To evaluate our approach, we extract
310 pairs of ported patches from Vim to Neovim after June, 2022 (i.e.,
the date when the corpus used to pretrain the LLM is collected).
We adopt metrics including accuracy and two metrics from the
relevant work [40] that measures the edits required to manually
port the patch, i.e., Average Edit Distance (AED) and Relative Edit
Distance (RED). PPatHF outperforms the best performing baselines
by a large margin in all metrics. Specifically, 42.3% patches ported
by PPatHF are syntactically equivalent to the the developer ported
one, and PPatHF can reduces 57% edits on average required by
the developer to manually port the patch. We also conduct a case
study to investigate the effectiveness of PPatHF in porting security
patches. Moreover, we evaluate the porting performance of PPatHF
on another two hard fork pairs to verify its generalizability.
• We propose to automate the patch porting in hard forks. We
conduct an empirical study of patches ported from Vim to Neovim
over the last ten years, unveiling the necessities and challenges
in automating the patch porting across hard forks.
• We propose, for the first time, a LLM based approach (namely
PPatHF) to automate patch porting across hard forks under the
zero-shot setting.
• The experiment results show that PPatHF outperforms baselines
significantly, and can largely reduce the manual efforts required
from the developer to port the patch.
• We open source our replication package [15] for follow-up works.
2 MOTIVATION AND PRELIMINARIES
In this section, we first provide motivating examples to show the
usage scenario and challenges of automating patch porting for hard
forks. Then, we conduct a preliminary study to comprehensively
investigate the necessities of automating such process.
Automating Zero-Shot Patch Porting for Hard Forks ISSTA ’24, September 16–20, 2024, Vienna, Austria
Project: Vim
Commit Message: patch 8.2.2285: Vim9: cannot set an option to a false. Problem: Vim9:
cannot set an option to a false. Solution: For VAR_BOOL use string "0". (closes #7603)
Commit Date: Jan 3, 2021
evalvars.c, static void set_option_from_tv(char_u *varname, typval_T *varp)
Project: Neovim
Commit Message: vim-patch:8.2.2285: …
Commit Date: Jul 25, 2022
eval/vars.c, static void set_option_from_tv(const char *varname, typval_T *varp)
…
char_u *strval;
char_u nbuf[NUMBUFLEN];
int error = FALSE;
if (varp->v_type == VAR_BOOL)
+ {
numval = (long)varp->vval.v_number;
- else if (!in_vim9script() || varp->v_type != VAR_STRING)
- numval = (long)tv_get_number_chk(varp, &error);
- strval = tv_get_string_buf_chk(varp, nbuf);
+ strval = (char_u *)"0"; // avoid using "false"
+ }
+ else
+ {
+ if (!in_vim9script() || varp->v_type != VAR_STRING)
+ numval = (long)tv_get_number_chk(varp, &error);
+ strval = tv_get_string_buf_chk(varp, nbuf);
+ }
if (!error && strval != NULL)
set_option_value(varname, numval, strval, OPT_LOCAL);
…
…
const char *strval;
bool error = false;
char nbuf[NUMBUFLEN];
if (varp->v_type == VAR_BOOL) {
numval = (long)varp->vval.v_number;
+ strval = "0"; // avoid using "false"
} else {
numval = (long)tv_get_number_chk(varp, &error);
+ strval = tv_get_string_buf_chk(varp, nbuf);
}
- strval = tv_get_string_buf_chk(varp, nbuf);
if (!error && strval != NULL) {
set_option_value(varname, numval, strval, OPT_LOCAL);
…
Figure 1: An example of patch porting in Vim-Neovim.
2.1 Motivating Example
Figure 1(a) presents a motivating example collected from Vim (i.e.,
source project) and Neovim (i.e., hard fork) to demonstrate the
patch porting across hard forks. This patch fixes a bug [14] in
function set_option_from_tv(), which is used to set the option
varname to the value of varp for the current buffer/window. In the
pre-patch version, when varp stores the bool value False, numval
will be set to 0 and strval will be set to "false" after calling the
function tv_get_string_buf_chk(). However, when numval is 0
and strval contains non-zero characters, set_option_value()
will raise an error. Thus, the patch modifies the logic to set strval
to "0" when varp is a bool type variable. This bug also exists in
Neovim due to the initial copying of the hard fork. Therefore, it is
necessary to port this patch to Neovim in time.
However, porting this patch is complicated and cannot be
achieved by existing tools like git-apply [3]. Because both the
changed lines and the context of the Neovim patch are different
from that of the Vim patch. For example, the function in Neovim
does not have the additional if restrictions for getting numval
of non-bool-type variables. In addition, the type of strval is
const char* in Neovim instead of char_u* in Vim, making the
explicit type casting, i.e., (char_u *), in the Vim patch unnecessary.
In the current practice, Neovim developers manually port the
patches on a case-by-case basis, which is time-consuming and errorprone,
and typically faces a long delay. In this example, this patch
was first committed in Vim on Jan. 3, 2021 to solve the bug, and was
not ported to Neovim until Jul. 25, 2022, delaying for 567.8 days.
Therefore, we argue that it is important and valuable to automate
the patch porting for hard forks.
In this work, due to the concern of practicability, we aim to tackle
this problem under a zero-shot setting. This specific task faces the
following two key challenges:
Understand the patch semantics to manage the convergence
of logic similarities and implementation differences. Though
hard forks share similar design logic with the source project, the
detailed implementations diverge over time [66]. This means an
automated solution for this task needs to correctly understand the
patch semantics, in order to: ❶ Match similar logic between the
given code snippets from the source project and the hard fork to
locate the code entities to be changed despite of the implementation
differences. For the example in Figure 1, to locate the code entities
to be changed in Neovim, an approach needs to tolerate the differences
between the if-else if block and the if-else block, and
successfully match them. ❷ Adapt the patch according to the implementation
differences in the context while maintaining the patch
semantics. Apart from the relatively straightforward name-space
changes (e.g., variable name, variable type, function name), there
are more complex modifications involving code structure and logic,
e.g., removing code irrelevant to or adding code specific to the context
of the hard fork. For the example in Figure 1, the explicit type
cast in the added statement (i.e., strval = (char_u *)"0") should
be removed when being ported to Neovim. The rational for such
change should traceback to the declaration of strval, where the
variable type is const char * instead of char_u *. FixMorph [52],
the state-of-the-art approach of patch backporting (which is the
most relevant task to ours), abstracts the patch into transformation
rules at the syntax level. It matches the patch and the context based
on the same syntactic structure and only allows certain generalizations
of the identifier names. It can not handle the patch porting
for hard forks where the syntactic structure changes greatly.
Automate the patch porting given only the information of
the patch from the source project, without additional prerequisite
of knowing any extra information, such as historically
ported patches, available test cases. As mentioned in the introduction,
the extra information is not always available or expensive
to collect in practice. This renders most existing approaches in the
relevant works invalid. For example, automated program transformation
methods including GetaFix [16] and Phoenix [17] require to
learn the transformation rules and determine the abstraction level
from multiple similar existing patches. PatchWeave [53] transforms
the patch by concolic executions and requires available test cases.
We observe unique opportunities in leveraging a LLM to solve the
aforementioned challenges: ❶ Endowed with the ability of capturing
the semantic information of the given code snippet [22, 38, 62],
the LLM is able to capture the implementation differences and automate
the patch porting based on similar high-level code semantics.
❷ The off-the-shelf LLM has already been proven to significantly
outperform the existing approaches on various code generation
tasks [22, 29, 61]. The generalizability of the LLM, especially the
strong performance under a zero-shot setting [37], makes the LLM
ISSTA ’24, September 16–20, 2024, Vienna, Austria Shengyi Pan, You Wang, Zhongxin Liu, Xing Hu, Xin Xia, and Shanping Li
2014 2015 2016 2017 2018 2019 2020 2021 2022 2023
year
0
200
400
600
800
1000
1200
1400
#ported_patch
repo
Vim
Neovim
Figure 2: Distribution of ported patches over the years
an ideal solution to automate patch porting for hard forks.We leverage
the LLM to build our approach, namely PPatHF, an effective
and generalizable solution to automate patch porting. Note that
PPatHF correctly ports the patch demonstrated in the example.
2.2 Preliminary Study
We conduct a preliminary study on the patches ported from Vim to
Neovim to understand the current practice of patch porting.
Porting patches from source project is necessary for hard
forks. We identify 6,487 Neovim patches ported from Vim, spanning
from April. 2, 2014 to Aug. 24, 2023. Figure 2 presents the
distribution of ported patches over the years. The Neovim repository
was created on GitHub on Jan. 31, 2014 [4]. Two month after
its creation, Neovim began porting patches from Vim (i.e., its source
project), and keeps porting until the time we collect our dataset (i.e.,
Sept. 1, 2023), and the number of ported patches does not decrease
over time. Besides, in each year before 2022, the number of patches
ported to Neovim has generally been lower than the required one,
indicating a considerable delay in patch porting and there may
still exist historical Vim patches that need to be ported. The ported
Neovim patches correspond to nearly 46% of the patches committed
to Vim. We find that most patches ported to the hard fork relate
to the bug fixing caused by the reuse of the code from the source
project. Among the bugs discovered in the source project, one critical
case are software vulnerabilities (SVs). We collect 175 Common
Vulnerabilities and Exposures (CVE) records related to Vim after
year 2014 (i.e., when the Neovim project is initiated) according
to the Ubuntu security advisory [12]. By cross-referencing CVEs
with ported patches, we find that over 76% of security patches have
been ported to Neovim. Moreover, we find that these SVs are only
disclosed for Vim (i.e., the source project) but not Neovim. This
aligns with the observations made byWang et al. [56] that although
similar SVs may exist in software with similar functionalities (e.g.,
SSL libraries) due to similar design logic or code clone, typically
only one CVE is disclosed for one of the particular software. Such
practices make the threats to hard forks unlikely to be alerted timely
or even disregarded [56], e.g., the Software Component Analysis
(SCA) tools scan the codebase for known vulnerable components by
referring SV databases (e.g., NVD) [23, 60]. To reduce the exposure
to zero-day attacks [18], it requires the stakeholders of hard forks
to actively monitor the SVs disclosed for upstream projects and
promptly port the corresponding patches.
It takes a large delay for hard forks to port the patches in
current practices. We further investigate the delay between the
time a patch is committed in Vim (i.e., the source project) and
it being ported in Neovim (i.e., the hard fork). Figure 3 presents
the cumulative distribution function (ECDF) of delta days before
30.5%
50.7%
74.5%
100%
30 180 365 3622
Delta Days (in log−scale)
Cumulative
Distribution
Figure 3: ECDF plot of delta days of patch porting
porting patches. Nearly 50% of patches took more than 180 days to
be ported into Neovim, and over 25% even took more than a year.
This indicates that a large portion of issues discovered and fixed in
Vim will still persist in Neovim for an extended long period of time.
When it comes to SVs, such delay in patch porting could be fatal.
The delay leaves an exploit window wide open for malicious actors
to launch 0-day attacks against Neovim by simply referring to disclosed
Vim SVs. We also examine the security patches that have
been ported to Neovim (134 in total). The median porting delay
is 37 days and over 25% of security patches take more than three
months to be ported. Although Neovim ports security patches in
a more urgent manner compared to ordinary defects, we contend
that such delay is still fatal, considering that information about the
SV in Vim has already been publicly disclosed (possibly including
a functional exploit). Taking CVE-2022-0413 [2] as an example, it
is a Use After Free vulnerability in Vim with a HIGH severity. The
vulnerability was publicly disclosed on NVD on Jan. 30, 2022. However,
it was left in the Neovim codebase for another 64 days before
the patch porting [8]. Once publicly disclosed, this SV becomes a
hot spot for malicious actors [27], especially given its high severity.
Moreover, the SV was disclosued with links of the Vim patch [9]
and a verified proof-of-concept (PoC) [10] listed as external references.
Such information further ease the difficulties for malicious
actors to turn the SV into a 0-day attack against Neovim. Over 70%
percent of the security patches are ported into Neovim after the
disclosure of the Vim SV, and nearly 85% of SVs are rated with HIGH
or CRITICAL severity. By automating the patch porting for hard
forks, we can significantly reduce the manual effort required in the
porting process, thus alleviating the delay in current practices.
3 PROBLEM FORMULATION
This work aims to automatically port the patch across the family
of hard forks (typically from the source project to the hard fork).
Specifically, we conduct patch porting at the function granularity,
as functions represent self-contained, fine-grained blocks of
code. Specifically, given the pre-patch and post-patch versions of
a modified function from the source project, namely ( 𝑓𝑠 , 𝑓
′
𝑠 ), and
the pre-patch version of the corresponding function in the hard
fork, namely 𝑓𝑓 , the LLM is utilized to automatically port the patch
derived from differentiating 𝑓𝑠 and 𝑓
′
𝑠 and apply it to 𝑓𝑓 :
𝑝𝑜𝑟𝑡

𝑓𝑠 , 𝑓
′
𝑠 , 𝑓𝑓

= 𝑓
′
𝑓 (1)
where 𝑓
′
𝑓 is the expected post-patch version of the function in the
hard fork. Since we aim to port the patch from the source project to
facilitate a similar code change (e.g., bug fixing, feature implication)
in the hard fork, it is essential for 𝑝𝑜𝑟𝑡 to maintain the functionality
of the patch while correctly adapting it based on the context of the
hard fork. Besides, we focus on a zero-shot scenario, i.e., automating
Automating Zero-Shot Patch Porting for Hard Forks ISSTA ’24, September 16–20, 2024, Vienna, Austria
𝒇𝒔 𝒇𝒔 " 𝒇𝒇 𝒇𝒔 𝒇𝒔 " 𝒇𝒇
Code Segments
Involve No Diff Lines
Removable Code
Segments
Intersect Mapping
Reduce
𝒇𝒇 " 𝒇𝒇 "
Porting
Module
Recover
Reduction
Module
Figure 4: Overview of PPatHF
the patch porting without knowing any historically ported patch
of the given pair of source project and hard fork.
4 APPROACH
In this section, we introduce our proposed approach, namely
PPatHF. Figure 4 presents the overview of PPatHF, which is composed
of a reduction module and a porting module. Specifically,
the reduction module first slims the input functions by removing
code snippets that are less relevant to the patch. Then, the porting
module adapt the patch derived from the pair of pre and post patch
functions from the source project to the hard fork by generating
the modified function. Finally, the generated post-patch function
is recovered by reintegrating the code snippets that are previously
removed by the reduction module.
4.1 Reduction Module
Our reduction module discards code segments in the functions that
are less relevant to the patch, allowing the LLM to port more patches
under its length limit. The modified functions are generally very
lengthy. We show in our experiments that under the length limit
of 2k, the LLM is only able to port 26.8% of the patches. Moreover,
the reduction module can largely reduce the computational costs
and allowing the LLM to focus on the key code related to the
modification.
The upper left part of Figure 4 illustrates the framework of our
reduction module. Briefly speaking, we first extract code segments
in the function from the source project that do not overlap with
code lines modified by the patch. We refer to these code segments
as removable segments. Subsequently, we map these removable
segments from the source project to the hard fork. Using the acquired
the removable segments for functions in both the source
project and the hard fork, we slim the input functions ( 𝑓𝑠 , 𝑓
′
𝑠 , 𝑓𝑓 ) by
removing these segments and later integrate them back to recover
the 𝑓
′
𝑓 generated by the porting module.
Extracting Removable Segments. We start by extracting removable
segments for the function from the source project, represented
in the form of a list of Abstract Syntax Tree (AST) nodes. Arbitrarily
reducing nodes in an AST may result in evident syntax errors (e.g.,
reducing an identifier in an expression statement) or distortion of
the original function (e.g., reducing an if condition while preserving
the if body), potentially confusing the LLM and leading to wrong
porting results. Thus, during the extraction of removable segments,
we only focus on sub-trees whose root nodes are of compound
statement types (e.g., if, for, and while statement). These sub-trees
within the AST constitute structurally complete units, ensuring
that their reduction does not distort the original function. Moreover,
code segments corresponding to the sub-trees of compound
statement are generally longer than those of simple statement, alleviating
the potential errors when mapping these segments from
the source project to the hard fork.
Specifically, we first conduct a diff operation between 𝑓𝑠 and 𝑓
′
𝑠
to obtain the statements deleted and added by the patch, denoted as
𝑠𝑡𝑚𝑑𝑒𝑙 and 𝑠𝑡𝑚𝑎𝑑𝑑 , respectively. Next, we traverse the AST of 𝑓𝑠 to
create a list of mutually exclusive sub-trees that are not overlapped
with 𝑠𝑡𝑚𝑑𝑒𝑙 . For 𝑓
′
𝑠 and 𝑠𝑡𝑚𝑎𝑑𝑑 , we follow the same procedure to
obtain another list. Finally,we calculate the intersection of these two
lists to obtain a list of sub-trees within the AST, which represents
all code segments that can be removed in both 𝑓𝑠 and 𝑓
′
𝑠 while
retaining the modified statements. Such matching between the two
lists is straightforward because the pre- and post-patch function
are identical after excluding the modified statements.
Mapping Removable Segments. In this part, we map the removable
segments of 𝑓𝑠 to 𝑓𝑓 . These two pre-patch functions are semantically
similar, but usually with different implementation details.
Thus, we perform the mapping based on text similarity. Specifically,
to determine the similarity between two code segments from 𝑓𝑠 and
𝑓𝑓 (denoted as 𝑠𝑠 and 𝑠𝑓 ), we first normalize them (e.g., removing
empty characters, lowering the case of alphabetic characters) and
then calculate the similarity score using the following formula.
𝑠𝑖𝑚𝑖𝑙𝑎𝑟𝑖𝑡𝑦(𝑠𝑠 , 𝑠𝑓 ) =
𝑚𝑎𝑥𝑙𝑒𝑛(𝑠𝑠 , 𝑠𝑓 ) − 𝑒𝑑𝑖𝑡_𝑑𝑖𝑠𝑡𝑎𝑛𝑐𝑒 (𝑠𝑠 , 𝑠𝑓 )
𝑚𝑎𝑥𝑙𝑒𝑛(𝑠𝑠 , 𝑠𝑓 )
(2)
where𝑚𝑎𝑥𝑙𝑒𝑛 is length of the longer code segment, 𝑒𝑑𝑖𝑡_𝑑𝑖𝑠𝑡𝑎𝑛𝑐𝑒
computes the Levenshtein distance between the two code segments.
Given a removable segment in 𝑓𝑠 as the target, to find its matching
code segment among the candidate segments in 𝑓𝑓 , we initially
calculate the similarity score between the target and each candidate,
and eliminate candidates with a similarity score lower than a
specified threshold 𝑡ℎ𝑟𝑒𝑠𝑠𝑒𝑙 𝑓 . Instead of solely relying on the local
information (i.e., the code segment itself) to conduct matching, we
further include the context information to eliminate the possible
matching errors, i.e., considering the similarity between code snippets
corresponding to the parent AST node of the segments to be
matched (denoted as the parent code snippet). Specifically, since
similar code segments may appear in multiple different locations
within the function, we also take into considerations of the relative
position of the code segment within the its parent code snippet.
We first divide the parent code snippets into two halves (i.e., the
preceding context and the following context) based on the position
of the code segments to be matched. Then, we calculate the
similarity scores between the two preceding halves and the two
following halves, respectively. Finally, we average the two scores
weighted by the length of the corresponding half and use it as the
final similarity score between the parent code snippets. We further
eliminate candidates whose parent similarity is under the specified
threshold 𝑡ℎ𝑟𝑒𝑠𝑝𝑎𝑟𝑒𝑛𝑡 . If there are still candidates left, we consider
the one with the highest parent similarity as the match.
For each removable segment of 𝑓𝑠 acquired in the extraction
phase, we attempt to retrieve the corresponding segment in 𝑓𝑓 ,
using the aforementioned matching process. If a match is retrieved,
ISSTA ’24, September 16–20, 2024, Vienna, Austria Shengyi Pan, You Wang, Zhongxin Liu, Xing Hu, Xin Xia, and Shanping Li
Pretrained LLM
(Frozen)
Pretrained
Weights
𝑾 ∈ ℝ𝒎×𝒏
𝑟
⨁
𝑥!
𝑨 ∈ ℝ𝒓×𝒏
𝑩 ∈ ℝ𝒎×𝒓
LoRA Adapters
From Finetuning Pretrained
Weights
𝑾 ∈ ℝ𝒎×𝒏
𝑟
⨁
𝑥!"#
𝑨 ∈ ℝ𝒓×𝒏
𝑩 ∈ ℝ𝒎×𝒓
…
…
LoRA
Adapter
Prompt
Template
𝑥"
LoRA
Adapter
𝟎
LoRA
Adapter
𝒌
…
Post
Process
…
𝒇
𝒇!
𝒎
Finetuning Patch Porting
!! !! " !#
!! "
Figure 5: Framework of the Porting Module
Below is a commit message, paired with a corresponding function
before from ! .
Apply the patch according to the commit message by generating
the function after based on the given function before.\n\n
### Commit Message:\n \n\n
### Function Before ( ! ):\n \n\n
### Function After ( ! ):\n
Instruction
(")
# $ !
# %, ? "
"!
(a) Prompt template for patch porting
(b) Prompt template for finetuning
Below is a patch (including function before and function after) from
!! , paired with a corresponding function before from !" .
Adapt the patch from !! to !" by generating the function after
based on the given function before.\n\n
### Function Before ( !! ):\n \n\n
### Function After ( !! ):\n \n\n
### Function Before ( !" ):\n \n\n
### Function After ( !" ):\n
Instruction
(#)
$ %#, %# $ !!
!! "
$ %%, ? !#
!# "
Figure 6: Designed prompt template for the patch porting
and the finetuning task. ⟨·⟩ stands for a placeholder.
we pair it with the removable segments of the function from the
source project to create a tuple. This results in a list of tuples of
removable segments, which is used to reduce the input functions.
Reducing & Recovering. We first index the removable tuples
acquired in the mapping process, and then simultaneously replace
these segments with placeholder comments in the form of
/* Placeholder_{index} */ for each of the three input functions
( 𝑓𝑠 , 𝑓
′
𝑠 , 𝑓𝑓 ). After the portingmodule generates the patched function
𝑓
′
𝑓 , we reintegrate these previously removed code segments back
into their due positions to recover the generation. Specifically, for
each placeholder comment found in the generated 𝑓
′
𝑓 , we utilize the
parsed index to retrieve the corresponding removed code segment,
and use it to replace the comment.
4.2 Porting Module
After pretraining on large-scale corpora, LLMs are endowed with
abilities as general and capable task solvers [63]. To better elicit
LLM’s ability on our patch porting task, we design the prompt
template following [54] (see Figure 6(a)).We give a natural language
instruction of the task first and followed by the task input, i.e., the
pre- and post-patch versions of the function in the source project
( 𝑓𝑠 , 𝑓
′
𝑠 ) and the pre-patch version of the corresponding function in
the hard fork 𝑓𝑓 . Specifically, we instruct the LLM to generate the
post-patch version of the function in the hard fork 𝑓
′
𝑓 by extracting
the patch logic from ( 𝑓𝑠 , 𝑓
′
𝑠 ) and apply it to 𝑓𝑓 . Moreover, the format
of our designed prompt template aligns with the guidance for incontext
learning (ICL) [19, 63]:
LLM(𝐼,𝑇 ( 𝑓𝑠 , 𝑓
′
𝑠 )
|    {z    }
𝑑𝑒𝑚𝑜
,𝑇 ( 𝑓𝑓 , ?)
|  {z  }
𝑞𝑢𝑒𝑟𝑦
) → 𝑓
′
𝑓 (3)
where 𝐼 is the task instruction and 𝑇 (·) is the template used to
format the input into the prompt. In some point, the pre- and postpatch
versions of function from the source project ( 𝑓𝑠 , 𝑓
′
𝑠 ) can be
regarded as a demonstration, which shows the LLM how to patch
the 𝑓𝑓 . The output is left as a blank to be completed by the LLM
through imitating the modifications presented by the demonstrations
from the source project. Thus, the designed prompt template
enables efficient ICL, which helps to better adapt the general ability
of the LLM acquired from pretraining to patch porting.
Although with proper prompt engineering, the LLM already
demonstrates powerful capabilities in various tasks [19, 50] without
gradient update, the prompt engineering faces the following
limitations [63]: 1) limited window size for context; 2) there lacks
a clear and deterministic practice regarding prompt design for a
new task like patch porting. Even subtle changes in prompt can
strongly impact LLM performance [51]. Thus, we further propose
an instruction-tuning [57] based training task to 1) better adapt the
LLM to the patch porting task; 2) inject project specific knowledge
into the LLM. In our designed finetuning task, we finetune the LLM
to patch a function (i.e., generate the post-patch version of the given
function) based on the given commit message. The prompt template
used to format the instances is shown in Figure 6(b). The LLM is
trained to optimize the sequence-to-sequence loss (see Section 5.3
for more training details). To collect the training instances, we crawl
the commit history of both the source project and the hard fork,
and further select commits suitable (e.g., with high quality commit
message) for the instruction tuning (refer to Section 5.2 for details of
the data preparation). Note that the design of our finetuning task is
different from the typical one that trains the model using the exact
same input and output with the evaluation. Our design conforms
to the principle of zero-shot setting to avoid compromising the
generalizability. Specifically, we do not require the information of
any known ported patch pairs (which are not readily available), but
the commit history which can be easily obtained for most modern
softwares (e.g., using version control systems like git). Besides, our
proposed finetuning task is closely related to the patch porting,
i.e., applying the patch (either derived from the commit message
or a pair of pre- and post-patch functions) to a given function to
generate its post-patch version. The prompt template (both the task
instruction and the format) for these two tasks also have a lot in
common (see Figure 6). Thus, by tuning with the proposed training
task, we encourage the LLM to better follow the instructions to
perform patch porting, alleviating the complexity of prompt engineering
[58]. Tuning can also help to align the output of the LLM
with our expectations, alleviating the known weaknesses including
completing the input without addressing task or generating
repetitively [24, 42]. More importantly, apart from better adapting
Automating Zero-Shot Patch Porting for Hard Forks ISSTA ’24, September 16–20, 2024, Vienna, Austria
the general ability of the LLM (acquired from pretraining) towards
the patch porting task, instruction-tuning is proven to be an effective
approach to inject domain-specific information into general
LLMs [39, 59]. The context information at the function scope may
not be sufficient to ensure a successful porting (e.g., a changed
function call). A general understanding of both the source project
and the hard fork (e.g., implementations, coding styles) is a prerequisite
of a correct porting. Our proposed finetuning task effectively
helps the LLM familiar with the projects, thus turning it into a
domain-specific expert.
Considering the huge cost of full parameter tuning, we adopt
Low-Rank Adaption (LoRA) [33] as a lightweight solution to facilitate
finetuning. Specifically, considering a general form of the
updating parameter matrix W ← W+ △W, LoRA freezes the original
matrix W ∈ R𝑚×𝑛 while approximating the update matrix
△W ∈ R𝑚×𝑛 by low-rank decomposition, i.e., △W = BA, where
B ∈ R𝑚×𝑟 , A ∈ R𝑟×𝑛, and the rank 𝑟 ≪ min (𝑚, 𝑛). Instead of tuning
the pretrained model weights (W), LoRA injects and tunes the
low-rank decomposition matrices (A, B) instead, thus significantly
reducing the number of trainable parameters (11.4% compared with
full tuning in our implementation) when adapting the LLM to patch
porting task (as shown in Figure 5). The reduction in trainable
parameters, in turn, significantly lowers the computational cost
required for tuning the LLM, making our proposed finetuning task
more cost-effective. Most importantly, the separation between the
frozen LLM and LoRA adapters (i.e., the injected low-rank decomposition
matrices) ensures efficient deployment against various
pairs of porting projects. Specifically, the pretrained LLM can be
shared as a base, allowing for the easy switch between small LoRA
adapters to accommodate different projects.
5 EXPERIMENT SETUP
In this section, we first introduce our data collection procedure.
Then, we describe our experiment settings.
5.1 Data Preparation for Patch Poring
We use Vim-Neovim as a specific example of to investigate the patch
porting from the source project to the hard fork. We select Vim-
Neovim based on the following reasons: ❶ Vim-Neovim is a typical
case of hard fork, i.e., successful branching with differentiation [48,
66]. Both Vim (the source project) and Neovim (the hard fork)
succeed as well-known and widely used text editors (with 33.5k
and 71.1k GitHub stars respectively). Both projects remain active
for a long time (nearly ten years). ❷ Neovim clearly records the
patches that are ported from Vim and follows a one-to-one porting
style (i.e., one Vim patch is ported into Neovim as an individual
patch). Since it has been nearly ten years since the initial fork
of Neovim, porting patches from Vim has become a routine of
the Neovim development and there are abundant historical ported
patches. These characteristics ease the difficulties in collecting and
analyzing the ported patches, as well as conducting experiments to
evaluate the porting performances.
We build dataset in the form of a quadruples ( 𝑓𝑠 , 𝑓
′
𝑠 , 𝑓𝑓 , 𝑓
′
𝑓
). The
first three functions are the task input, and the last one is the
expected output (see Section 3 for a detailed task formulation). We
build our patch porting dataset based on the following steps:
STEP 1: Collect project history commits. We first extract the
entire commit history of Vim and Neovim util Sept. 1, 2023. We
use Git to traverse the historical commits and collect the relevant
information (e.g., commit hash, message, and the committed date).
STEP 2: Pair the ported Neovim patches with the source one
from Vim. Based on the manual check of Neovim commits, we
observe that the commit message of the patches ported from Vim
share a fixed format, where the message title starts with “vimpatch:"
followed by the same patch id used in Vim and the message
body posts the link to the source patch in Vim. Thus, we filter the
collected Neovim commits by matching commit messages with the
above two patterns to get the patches that are ported from Vim. As a
result, we obtain 6,487 ported patches in Neovim, and out of which,
we are able to retrieve the corresponding valid Vim commit hash
for 5,998 patches. Some commit messages of the ported Neovim
patch may post the Vim issue id instead of the commit hash or
simply forget to post any reference link.
STEP 3: Filter ported patch pairs to build the test set. Since
we focus on automating the patch porting with a zero-shot setting,
there are no known ported patch pairs available for model training
or validation. We build our test set using the patches ported
into Neovim after July 1, 2022. Becuase, the corpus (namely The
Stack [36]) used to pretrain StarCoder [38] (the LLM used in our
implementation) is collected from extensive GitHub repositories
(downloaded before July 2022). Thus, we only include the patches
ported after the collection date of the pretrain corpus to avoid possible
training-to-test data leakage, which has been shown to compromise
the validity of the evaluation [26, 62]. We further restrict
our test set into the patches that only modifies a single function
in both Vim and Neovim. Specifically, for each patch, we only focus
on the modifications to the source code, excluding changes to
comments in *.c files (excluding changes to documentations and
header files). Note that the single-function restriction is mainly to
simplify the evaluation as PPatHF automates the patch porting
at the function granularity (see Section 3). We further conduct a
case study in Section 7.1 to verify the validity of PPatHF when
generalizing to patches involving multiple function changes.
STEP 4: Preprocess the ported patch pairs. For each filtered
patch pair, we further extract the functions before and after the
patch in Vim and Neovim, respectively.We preprocess each function
by removing the comments and empty lines. Finally, our test set
includes 310 pairs of ported patches.
5.2 Data Preparation for Finetuning
In the proposed finetuning task (see Section 4.2), we tune the LLM
to patch the given function 𝑓 to generate its post-patch version 𝑓
′
according to the commit message 𝑚. We build the dataset in the
form of a triplet ( 𝑓 , 𝑓
′
,𝑚) by collecting project history commits
with single-function change. Note that we only keep the commits
before July 1, 2022 for finetuning to make sure there is no leakage
of the test data. Since the commit message is included in the prompt
to instruct the LLM to patch the function (see Figure 6(b)), we clean
and format the commit message to only keep the natural language
descriptions while removing code snippets, commit hashes, etc.We
further exclude commits with low-quality messages (e.g., cleaned
messages containing less than five words) from finetuning. These
messages are unable to provide valid instructions to guide the LLM
ISSTA ’24, September 16–20, 2024, Vienna, Austria Shengyi Pan, You Wang, Zhongxin Liu, Xing Hu, Xin Xia, and Shanping Li
patch the function, thus harming the effectiveness of finetuning.
Finally, our finetuning dataset contains 2,747 and 2,082 commits
from Vim and Neovim, respectively.
5.3 Experiment Setting
The experimental environment is a server with NVIDIA A800 GPUs,
Intel Xeon 8358P CPUs, running Ubuntu OS.
Implementation Details. We use StarCoder [38], a recent and
powerful foundation model of code, to implement the porting
module of PPatHF. We download the public accessible pretrained
weights (with 15.5B parameters) from the Hugging Face Transformer
library [11] to initialize our model. In the finetuning, we
tune the StarCoder to optimize the sequence-to-sequence loss in
a supervised way. Specifically, the LLM is trained with the causal
language modelling (CLM) task [43], but with the CLM loss only
applied to the output sequence (i.e., the completion). We mask the
CLM loss of the input sequence (i.e., the prompt) to ensure the
model is only optimized to generate the expected output. We adopt
LoRA to tune the LLM with the rank set to 8 following [1]. we
use AdamW [41] as the optimizer. The learning rate is set to 2𝑒−5
following [54]. During the training, we adopt a cosine learning rate
scheduler with the warmup ratio set to 0.03 following [54].We tune
the LLM for 10 epochs during the finetuning.
Baselines. We include the following four baselines: ❶ Origin is a
special baseline for patch porting, which directly outputs the given
pre-patch version of the function in the hard fork without applying
any modifications. It measures the default manual efforts to port the
patch. By comparing PPatHF with Origin, we can know whether
the generated post-patch functions are closer to the ground truth
than the pre-patch version. ❷ git-apply [3] is the tool from the Git
suit. ❸ patch [6] is the tool from GUN Diffutils. These two tools are
used to apply a given patch to the target file. With these baselines,
we examine whether porting patches across hard forks can be done
with a simple copy-and-paste process. ❹ FixMorph [52] is the stateof-
the-art approach in patch backporting. It is the most relevant
work to our proposed zero-shot hard fork patch porting task, which
backports a patch from the Linux mainline version to older versions
at the syntax level. ❺ StarCoder [38] is one of the most advanced
open-source code LLMs. To the best of our knowledge, no existing
neural approach can be directly applied to our patch porting task,
especially under a zero-shot setting. Since we are first to introduce
the task of patch porting in hard forks, we also investigate the use
of the off-the-shelf LLM for this task by including StarCoder as
one baseline. Moreover, we implement our porting module using
the StarCoder as the underlying LLM. Thus, by comparing PPatHF
with the StarCoder, we can better understand the shortcomings
of directly applying the off-the-shelf LLMs in solving the patch
porting task and the effectiveness of our design. We use the same
prompt (see Figure 6(a)) for StarCoder and PPatHF.
Evaluation Metrics. To automate the patch porting in hard forks,
PPatHF aims to generate the post-patch version of the given function
in the hard fork based on the patch from the source project.We
evaluate the performance by adopting the metrics, i.e., Accuracy,
AED, and RED, used in the relevant works regarding 1) patch backporting
for Linux kernel [52], 2) comment updating [40], which
shares a similar task setting with ours, i.e., updating a given code
comment based on the patch. Accuracy is used to present to what
extent an approach can port the patch correctly. We refer a correct
porting to the generated post-patch function 𝑓ˆ
′
𝑓 that is syntactically
equivalent to the developer patched one 𝑓
′
𝑓 . Apart from the strict
identical match, Average Edit Distance (AED) and Relative Edit Distance
(RED) measures the edits developer still need to perform to
correctly patch the function after using a patch porting tool, which
provides a more detailed evaluation of the porting performance.
Specifically, AED and RED are calculated as follows:
𝐴𝐸𝐷 =
1
𝑁
Σ︁𝑁
𝑖=1
𝑒𝑑𝑖𝑡_𝑑𝑖𝑠𝑡𝑎𝑛𝑐𝑒

𝑓ˆ
′ (𝑖 )
𝑓 , 𝑓
′ (𝑖 )
𝑓

𝑅𝐸𝐷 =
1
𝑁
Σ︁𝑁
𝑖=1
𝑒𝑑𝑖𝑡_𝑑𝑖𝑠𝑡𝑎𝑛𝑐𝑒

𝑓ˆ
′ (𝑖 )
𝑓 , 𝑓
′ (𝑖 )
𝑓

𝑒𝑑𝑖𝑡_𝑑𝑖𝑠𝑡𝑎𝑛𝑐𝑒

𝑓 (𝑖 )
𝑓 , 𝑓
′ (𝑖 )
𝑓

(4)
where 𝑒𝑑𝑖𝑡_𝑑𝑖𝑠𝑡𝑎𝑛𝑐𝑒 is the token-level (first parse the code sequence
into tokens) Levenshtein distance and 𝑓 (𝑖 )
𝑓 refers to the given prepatch
function in the hard fork of the 𝑖𝑡ℎ sample. Different from
AED that measures the absolute edit distances between the generated
post-patch function and the ground truth, RED reports the edit
distances relative to the one between the pre-patch version and the
ground truth. Thus, if the RED of one approach is less than 1, then
the approach is expected to help developers understand where and
how to port the patch and reduce the required manual edits.
6 EXPERIMENT RESULTS
In the experiment, we aim to answer the following two RQs:
• RQ1: How effective is PPatHF compared to baselines for
zero-shot patch porting for hard forks?
• RQ2: How effective are the key designs of PPatHF?
6.1 RQ1. The Effectiveness of PPatHF
Method. To verify the effectiveness of PPatHF in porting patches,
we compare its performance with baselines (see Section 5.3) using
the test set collected in Section 5.1. Note that the test set only
includes patches ported after 2022-07-01, i.e., the date when the
corpus used to pretrain [36] and tune (see Section 5.2) StarCoder are
collected before. For StarCoder and PPatHF (i.e., neural models), we
investigate with three different settings of length limits (i.e., 2k, 4k,
and 8k) to provide a comprehensive evaluation of the performance.
Every LLM has a length limit in its implementation, which restricts
the maximum number of tokens (shared by the prompt and the
completion) being able to process. Typical length limits for modern
LLMs including 2k (e.g., GPT-3 [19], LLaMA [55]), 4k (e.g., GPT-
3.5 [7]), and 8k for more recent models (e.g., StarCoder [38], GPT-
4 [7]). Also, the increase of the input length leads to significant (up to
quadratic based on the implementation of the transformer attention
mechanism) increase of memory footprint and computational cost.
Results. Table 1 presents the performance comparisons between
PPatHF and the baselines for zero-shot patch porting for hard forks.
None of the patches in the test set can be directly applied using
git-apply or patch due to the implementation differences between
the source project and the hard fork. The number of patches that the
neural models are able to complete the generation instead of being
forced to stop due to the length limit (i.e., the column Complete in
the table) increases significantly as we enlarging the length limit
Automating Zero-Shot Patch Porting for Hard Forks ISSTA ’24, September 16–20, 2024, Vienna, Austria
Table 1: Performance comparisons between PPatHF and baselines
for zero-shot patch porting for hard forks
Length Approach Complete Accuracy AED RED
/
Origin
/
0 (0.0%) 26.60 1.00
git-apply 0 (0.0%) / /
patch 0 (0.0%) 26.57 1.00
FixMorph 13 (4.2%) 31.56 1.52
2k
StarCoder 83 (26.8%) 29 (9.4%) 23.05 0.94
PPatHF 150 (48.4%) 72 (23.2%) 19.80 0.70
4k
StarCoder 185 (59.7%) 67 (21.6%) 18.76 0.83
PPatHF 242 (78.1%) 111 (35.8%) 14.45 0.51
8k
StarCoder 255 (82.3%) 95 (30.6%) 14.30 0.67
PPatHF 290 (93.5%) 131 (42.3%) 11.98 0.43
from 2k to 8k. However, even with its maximum limit (i.e., 8k), the
StarCoder is still unable to complete the generation of the entire
310 patches in the test set, indicating that some functions could be
very lengthy in practice. This observation highlights the necessities
of our proposed reduction module (Section 4.1), which simplifies
the function by removing the context less-relevant to the patch.
Compared to StarCoder, PPatHF is able to complete the generation
for more patches, especially under a resource-limit scenario (i.e.,
0.8 times more patches with a 2k limit). For patches that neural
models unable to complete the generation, we use the pre-patch
version of the function 𝑓𝑓 as the default output.
PPatHF @8k (at a length limit of 8k) correctly ports 131 (42.3%)
patches, 9 times more than the one achieved by FixMorph, i.e.,
13 (4.2%) patches. Regarding edit-based metrics, PPatHF @8k can
significantly reduce the efforts for developers to port the patch
by automating 57% of the manual edits on average (i.e., a RED of
0.43), reducing the average edits to patch a function from 26.60
to 11.98. FixMorph, on the contrary, introduces 52% of extra edits
(i.e., a RED of 1.52), increasing the average edits to 31.56. The significant
improvement of PPatHF over FixMorph is mainly due to
its ability in understanding the patch semantics, thus being able
to handle the significant implementation divergences between the
source project and the hard fork. For the motivating example of
patch porting presented in Figure 1, which involves locating and
adapting patches with largely changed syntactical structures, (see
Section 2.1). PPatHF correctly automates the porting of this patch
while FixMorph, as a syntax-based approach, fails to handle such
implementation differences.
Compared with the StarCoder, PPatHF significantly outperforms
the StarCoder in the number of correctly ported patches by 148.3%,
65.7%, and 37.9% under the length limit of 2k, 4k, and 8k, respectively.
Large improvements are also observed on the AED and RED
metrics. Moreover, PPatHF even achieves better performances than
the StarCoder with a larger length limit, i.e., PPatHF @2k vs. Star-
Coder@4k and PPatHF @4k vs. StarCoder@8k. These results show
that it is non-trivial to leverage the LLM to effectively automate the
patch porting, and verify the validity of our designs in better utilizing
LLM’s ability for this task (see RQ2 for a detailed discussion).
RQ-1: PPatHF significantly outperforms the baselines for zeroshot
patch porting in hard forks. PPatHF can effectively help
developers understand where and how to port the patches and
significantly reduce the required manual edits.
6.2 RQ2. The Key Designs of PPatHF
Method. Our findings in RQ1 have verified that PPatHF outperforms
the baselines by a large margin. With RQ2, we aim to further
verify the effectiveness of the key designs of PPatHF. Specifically,
we compare the performances of PPatHF with two variants, each
lacking one of the following key designs: 1) the design of incorporating
the reduction module to slim the function by removing
the code snippets less-relevant to the patch (see Section 4.1). The
variant (PPatHF-r) removes the reduction module (see Figure 4)
and directly inputs the original ( 𝑓𝑠 , 𝑓
′
𝑠 , 𝑓𝑓 ) into the porting module.
2) the design of a finetuning task to better adapt the LLM to
the patch porting task and inject project-specific information (see
Section 4.2). The variant (PPatHF-f) removes the finetuning and
directly adopts the pretrained off-the-shelf LLM.
Results. Table 2 presents the performance comparisons between
PPatHF and the variant PPatHF-r. Since the reduction module slims
the input functions by removing the less-relevant contexts, PPatHF
is able to port more patches compared with PPatHF-r under a given
length limit. To provide an in-depth comparison between PPatHF
and PPatHF-r, we divide the patches that PPatHF can complete the
generation into following two groups and compare the performance
within each group: ❶ patches that both models can complete the
generation (column Share), ❷ extra patches that only PPatHF can
complete the generation. For these patches, we use the pre-patch
version of the function 𝑓𝑓 as the default output of PPatHF-r and
report the corresponding performance (column Unique).
Regarding the overall porting performance, PPatHF increases
the performance of PPatHF-r in the number of correctly ported
patches by 111.8%, 40.5%, and 13.9% at the length limits of 2k, 4k,
and 8k, respectively. Large improvements are also observed on AED
and RED. The notable performance improvement is primarily due to
PPatHF’s ability to port a larger number of patches under the given
length limit (column Unique of Table 2), which is enabled by the
reduction module as it slims the input functions by removing the
less-relevant code snippets. For example, under the length limit of
2k, PPatHF is able to complete the generation for 68 more patches
than the 82 shared ones with PPatHF-r, among which PPatHF
correctly ports 38 patches (over 100% improvement) and reduces
the manual edits required to port the patch substantially.
We further compare the performance of PPatHF and PPatHFr
regarding the shared patches (column Share of Table 2), which
provides us a more comprehensive understanding of the affects
introduced by the reduction module.We find that the incorporation
of the reduction module does not harm the porting performance,
and could even benefit the performance when the input functions
are lengthy, e.g., the RED reduces by 15.7% and 17.4% at the length
limit of 4k and 8k, respectively. These observations prove that
the code snippets removed by our reduction module are indeed
irrelevant for the LLM to port the patch, and by removing these
irrelevant code snippets, the reduction module can even benefit the
patch porting by helping the LLM concentrate on the key contexts
related to the patch. Moreover, the incorporation of the reduction
module saves enormous computational cost (including the memory
footprint and the run time) for the LLM to process the same patch
as the cost is positively correlated to the input length. The average
length after applying the reduction module is 0.70 of the original.
ISSTA ’24, September 16–20, 2024, Vienna, Austria Shengyi Pan, You Wang, Zhongxin Liu, Xing Hu, Xin Xia, and Shanping Li
Table 2: Comparisons of PPatHF with variant PPatHF-r
Length Approach
Overall Share Unique
Complete Accuracy AED RED Complete Accuracy AED RED Complete Accuracy AED RED
2k
PPatHF-r 82 (26.5%) 34 (11.0%) 22.76 0.85
82
34 7.95 0.44 0 0 19.50 1.00
PPatHF 150 (48.4%) 72 (23.2%) 19.80 0.70 34 8.22 0.43 68 38 5.68 0.30
4k
PPatHF-r 185 (59.7%) 79 (25.5%) 18.37 0.71
185
79 11.52 0.51 0 0 23.58 1.00
PPatHF 242 (78.1%) 111 (35.8%) 14.45 0.51 80 10.51 0.43 57 31 5.54 0.20
8k
PPatHF-r 255 (82.3%) 115 (37.1%) 14.42 0.56
255
115 11.95 0.46 0 0 21.63 1.00
PPatHF 290 (93.5%) 131 (42.3%) 11.98 0.43 117 10.88 0.38 35 14 7.83 0.45
Table 3: Comparisons of PPatHF with variant PPatHF-f
Length Approach Complete Accuracy AED RED
2k
PPatHF-f
150
64 (42.7%) 7.83 0.50
PPatHF 72 (48.0%) 7.07 0.37
4k
PPatHF-f
242
99 (40.9%) 9.48 0.47
PPatHF 111 (45.9%) 9.34 0.37
8k
PPatHF-f
290
115 (39.7%) 10.35 0.46
PPatHF 131 (45.2%) 10.51 0.39
Table 2 presents the performance comparisons between PPatHF
and the variant PPatHF-f. Note that we only focus on the porting
performances on the patches that models can complete the generation.
The performance improvements brought by the finetuning
are substantial and similar across different length limits, e.g., 12.5%,
12.1%, and 13.9% in the number of correctly ported patches under
the length limit of 2k, 4k, and 8k, respectively. These observations
verify the effectiveness of introducing the finetuning task to better
adapt the LLM to the patch porting task.
RQ-2: Both designs improves the performance. The reduction
module slims the input functions, enabling PPatHF to port more
patches under the given length limit. The introducing of the finetuning
task helps the LLM better adapt to the patch porting task.
7 DISCUSSION
In this section, we discuss the effectiveness of PPatHF in porting
security patches, the generalizability of PPatHF regarding different
hard fork pairs, and the threats to validity.
7.1 Porting Security Patches
We evaluate the effectiveness of PPatHF in porting security patches
using CVE fixes ported from Vim to Neovim after 2022-07-01. There
are 41 CVE fixes that only involves modifications of a single function.
PPatHF can correctly port nearly half (i.e., 20) of the patches.
Besides, PPatHF can automate 78% of the edits on average (i.e., a
RED of 0.22) required for developers to manually port the patch.
Besides, we further conduct a case study with 10 CVE fixes
to investigate the performance of PPatHF in porting patches that
modify multiple functions. For each CVE fix, we leverage PPatHF to
port the patch on a function-wise basis. Table 4 shows the statistics
of the selected CVE fixes and the porting results. PPatHF correctly
ports three CVE fixes, and is expected to correctly port the patch
to over half of the modified functions (1.7/2.9) involved in a CVE
fix on average. PPatHF can help developers in porting patches
with multi-function changes by significantly reducing the required
manual edits, i.e., reducing the average edits to port a CVE fix from
60.2 to 18.8. The above results verify the effectiveness of PPatHF
in porting the patches with multi-function changes.
Table 4: Case study of porting multi-function change patches
CVE ID Vim
Patch
Neovim
Patch Accuracy1 AED
(Origin) RED
CVE-2022-1725 b62dc5e ddd69a6 3/3 0 (62) 0
CVE-2022-1897 338f1fc df4c634 4/5 3 (110) 0.03
CVE-2022-1942 71223e2 0612101 2/4 13 (69) 0.19
CVE-2022-2522 5fa9f23 8e67af1 2/4 4 (50) 0.08
CVE-2022-2598 4e677b9 88ed332 1/2 4 (36) 0.11
CVE-2022-3016 6d24a51 f72ae45 3/3 0 (25) 0
CVE-2022-3296 96b9bf8 0cb9011 0/2 121 (147) 0.82
CVE-2022-3324 8279af5 cd9ca70 2/2 0 (18) 0
CVE-2022-3352 ef97632 6bc2d6b 0/2 10 (40) 0.25
CVE-2023-2426 caf642c ab7dcef 0/2 33 (45) 0.73
Avg. - - 1.7/2.9 18.8 (60.2) 0.22
1 The Accuracy for each CVE fix is presented in the form of {#correctly patched functions}/{#
total functions changed by the patch}.
Table 5: Patch porting performance of other hard fork pairs
Hard Fork Pair Approach Accuracy AED (Origin) RED
Cpython-Cinder
FixMorph 1/13 (7.7%) 27.85 (35.77) 0.77
PPatHF 8/13 (61.5%) 9.46 (35.77) 0.19
FreeBSD-OpenBSD
FixMorph 1/7 (14.3%) 16.29 (18.14) 0.85
PPatHF 5/7 (71.4%) 8.0 (18.14) 0.17
7.2 Porting Patches of Other Hard Fork Pairs
To verify the generalizability of PPatHF, we evaluate its porting
performance with another two well-known hard fork pairs, i.e.,
Cpython-Cinder and FreeBSD-OpenBSD. Both two hard forks do
not standardize the patch porting practice (e.g., lack unified documentation).
We first filter potential ported patches by searching
for commit hash or pull request id of the source project mentioned
in the commit message, and then manually validate them and extract
the patched function pairs. We only consider patches ported
after 2022-07-01 for evaluation. We collect 13 and 7 patches for
Cpython-Cinder and FreeBSD-OpenBSD, respectively. We do not
finetune PPatHF again using commits from these two hard fork
pairs. Table 5 presents the porting performance of FixMorph and
PPatHF on the two hard fork pairs. PPatHF outperforms FixMorph
by a large margin. For both hard fork pairs, PPatHF can correctly
port over 60% of the investigated patches, and reduce over 80%
(i.e., with RED < 0.2) of the manual edits. These results verify the
generalizability of PPatHF regarding different hard fork pairs.
7.3 Threats to Validity
Threats to internal validity refer to the experiment biases and
errors. Threat related to our approach is that in our reduction
module, when mapping the removable code segments between
the source project and the hard fork, we generally rely on the
text similarity between two code segments. It can fail if the hard
fork changes the namespaces and implementations significantly.
Automating Zero-Shot Patch Porting for Hard Forks ISSTA ’24, September 16–20, 2024, Vienna, Austria
More precise mapping techniques such as semantic clone detection
are required in such cases. Another threat regarding evaluation
is that we regard the developer ported patch as the ground truth
to calculate the metrics. However, a patch that is syntactically
different but semantically equivalent to the developer ported patch
should also be considered as a correct porting. Thus, our evaluation
may under estimate the porting performance. Nevertheless, it is
challenging and can be error-prone to determine whether a patch
is semantically equivalent to the developer ported patch.
Threats to external validity refer to the generalizability of our approach.
We evaluate PPatHF using 310 patches with single-function
change.We conduct a case study to show that PPatHF can also port
patches that involves modifications of multiple functions. However,
more evaluations are needed to investigate the effectiveness
of PPatHF in porting patches where the functions from the source
project and the hard fork do not conform to a one-to-one mapping.
For example, changes of multiple functions from the source
project should be ported into one function in the hard fork. Besides,
though we evaluate PPatHF on two additional hard fork pairs (i.e.,
Cpython-Cinder, FreeBSD-OpenBSD) apart from the Vim-Neovim
(see Section 7.2), the evaluation sets for these two additional pairs
are relatively small. This is because these two hard fork pairs do
not standardize patch porting practice (e.g., lack unified documentation),
making it difficult and time-consuming for us to collect
the ported patch pairs. Future works are required to collect larger
datasets and include more hard fork pairs to more comprehensively
evaluating the generalizability of PPatHF. Furthermore, we only
investigate the effectiveness of PPatHF with StarCoder as the underlying
LLM. Although StarCoder is one of the most advanced
open-source code LLMs when we conduct our experiments, code
LLMs are undergoing rapid updates and more advanced LLMs (e.g.,
CodeLlama [49]) are constantly being introduced. Future works
should integrate PPatHF with more advanced code LLMs to investigate
its generalizability across different underlying LLMs.
8 RELATEDWORK
In this section, we describe two aspects of the related work:
Hard Forks. We follow Zhou et al. [64–66] to distinguish between
two forms of forks by referring the traditional one with the intention
of code reuse as hard forks, and the new one with the intention
of collaborative development as social forks. Our focus in this paper
is to automate the patch propagation across a family of hard forks
(i.e., independent projects with a relation of code reuse). There
are a bunch of studies focus on investigating the motivations and
outcomes of hard forks [35, 48], as well as its pros and cons to the
software development [20, 44]. Recently, Zhou et al. [66] conduct
an empirical study to revisit the general practice of hard forks on
GitHub. Businge et al. [20] empirically investigate the maintenance
practices among the hard fork families in three software ecosystems
(e.g., Android, .NET, and JavaScript). Both Zhou et al. and
Businge et al. conclude that current tools (e.g., git apply) fail to
handle the divergence between hard forks, and the lack of integration
support tools has resulted in limited coordination and duplicate
efforts. Ren et al. [47] conduct an empirical study and find that a
non-negligible portion (20.5%) of patches need to be ported across
forked projects. Different from the existing works that are mainly
focus on understanding the general behaviour of hard forks (e.g.,
motivations), we highlight the flaws in the current patch porting
practice across hard forks (especially from the security perspective)
and further propose an approach to automate the porting process.
Patch Transplantation. There are several works that try to transplant
patch from one codebase to another. Automated program
transformation [13, 16] approaches infer transformation rules from
the exiting patches, and then apply the inferred rules to new codebases.
However, these approaches need to learn the transformation
rules from multiple similar patches. FixMorph [52] backports the
patch from the mainline version of Linux to older versions. It abstracts
the patch into transformation rules at the syntax level. It
leverages the syntactic structure to find the patch locations and apply
the abstracted transformation rule. PatchWeave [53] transplant
a patch from a donor program to fix the vulnerability. It relies on
test cases and concolic execution to locate the patch insertion point
and performs namespace translations. There are two drawbacks
that limits the application of these methods to port patches for hard
forks: 1) they can not deal with complicated adaptions, e.g., the
syntactic structure of the patch needs to be modified, 2) they rely
on extra information (e.g., multiple similar patches, available test
cases) apart from the reference patch from the source project, which
are not always available in reality. In this work, we leverage LLM’s
superior understanding of code semantics and strong performance
under zero-shot setting to automate patch porting for hard forks.
9 CONCLUSION AND FUTUREWORK
In this paper, we take the first step to automate the patch porting
for hard forks.We propose a LLM-based approach (namely PPatHF)
to port the patch on a function-wise basis. PPatHF is composed of a
reduction module and a porting module. The reduction module first
slims the input functions by removing code snippets less relevant
to the patch, enabling PPatHF to port more patches under the given
length limit. The porting module effectively leverages a LLM to
apply the patch from the reference project to the target project by
1) enabling efficient in-context learning with a specially designed
prompt template, 2) effectively adapting the LLM to the patch porting
and injecting project-specific knowledge with the proposed
instruction-tuning based training task. The evaluation results on
310 Neovim patches ported from Vim demonstrate that PPatHF
outperforms the baselines substantially. PPatHF correctly ports 131
(42.3%) patches, and can help developers understand where and
how to port the patch by reducing 57% of the manual edits.
In the future, we plan to integrate other LLMs with our approach
and apply it to other projects and programming languages.
10 DATA AVAILABILITY
The replication package of our work is publicly available at [15].
ACKNOWLEDGMENTS
We thank the anonymous reviewers for their constructive comments
to improve the paper. This research/project is supported by
the National Natural Science Foundation of China (No. 62202420
and No. 62372398), the Ningbo Natural Science Foundation (No.
2023J292), and the Fundamental Research Funds for the Central
Universities (No. 226-2022-00064). Zhongxin Liu gratefully acknowledges
the support of Zhejiang University Education Foundation
Qizhen Scholar Foundation.
ISSTA ’24, September 16–20, 2024, Vienna, Austria Shengyi Pan, You Wang, Zhongxin Liu, Xing Hu, Xin Xia, and Shanping Li
REFERENCES
[1] 2023. Alpaca-LoRA. https://github.com/tloen/alpaca-lora.
[2] 2023. CVE-2022-0413. https://nvd.nist.gov/vuln/detail/CVE-2022-0413.
[3] 2023. Git - git-apply Documentation. https://git-scm.com/docs/git-apply.
[4] 2023. GitHub Neovim Repository. https://github.com/neovim/neovim.
[5] 2023. GitHub Vim Repository. https://github.com/vim/vim.
[6] 2023. Linux Patch Tool. https://man7.org/linux/man-pages/man1/patch.1.html
[7] 2023. Models - OpenAI API. https://platform.openai.com/docs/models/.
[8] 2023. Patch of CVE-2022-0413 in Neovim. https://github.com/neovim/neovim/
commit/64869831171ffa455f35d1a1ce3a3f9c7e7416a2.
[9] 2023. Patch of CVE-2022-0413 in Vim. https://github.com/vim/vim/commit/
37f47958b8a2a44abc60614271d9537e7f14e51a.
[10] 2023. POC of CVE-2022-0413 in huntr. https://huntr.com/bounties/563d1e8f-
5c3d-4669-941c-3216f4a87c38/.
[11] 2023. StarCoder · Hugging Face. https://huggingface.co/bigcode/starcoder
[12] 2023. Ubuntu Security Advisory - Vim. https://ubuntu.com/security/cves?q=
&package=vim&priority=&version=&status=.
[13] 2023. Using SLAs for better vulnerability management. https:
//phoenix.security/using-slas-for-better-vulnerability-managementremediation-
improving-developers-workflow/.
[14] 2023. Vim issue #7603. https://github.com/vim/vim/issues/7603.
[15] 2024. Replication Package. https://github.com/ZJU-CTAG/PPatHF.
[16] Johannes Bader, Andrew Scott, Michael Pradel, and Satish Chandra. 2019. Getafix:
Learning to fix bugs automatically. Proceedings of the ACM on Programming
Languages 3, OOPSLA (2019), 1–27.
[17] Rohan Bavishi, Hiroaki Yoshida, and Mukul R Prasad. 2019. Phoenix: Automated
data-driven synthesis of repairs for static analysis violations. In Proceedings of
the 2019 27th ACM Joint Meeting on European Software Engineering Conference
and Symposium on the Foundations of Software Engineering. 613–624.
[18] Leyla Bilge and Tudor Dumitraş. 2012. Before we knew it: an empirical study of
zero-day attacks in the real world. In Proceedings of the 2012 ACM conference on
Computer and communications security. 833–844.
[19] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan,
Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda
Askell, et al. 2020. Language models are few-shot learners. Advances in neural
information processing systems 33 (2020), 1877–1901.
[20] John Businge, Moses Openja, Sarah Nadi, and Thorsten Berger. 2022. Reuse
and maintenance practices among divergent forks in three software ecosystems.
Empirical Software Engineering 27, 2 (2022), 54.
[21] John Businge, Ahmed Zerouali, Alexandre Decan, Tom Mens, Serge Demeyer,
and Coen De Roover. 2022. Variant forks-motivations and impediments. In 2022
IEEE International Conference on Software Analysis, Evolution and Reengineering
(SANER). IEEE, 867–877.
[22] Mark Chen, Jerry Tworek, Heewoo Jun, Qiming Yuan, Henrique Ponde de Oliveira
Pinto, Jared Kaplan, Harri Edwards, Yuri Burda, Nicholas Joseph, Greg Brockman,
et al. 2021. Evaluating large language models trained on code. arXiv preprint
arXiv:2107.03374 (2021).
[23] Yang Chen, Andrew E Santosa, Ang Ming Yi, Abhishek Sharma, Asankhaya
Sharma, and David Lo. 2020. A machine learning approach for vulnerability
curation. In Proceedings of the 17th International Conference on Mining Software
Repositories. 32–42.
[24] HyungWon Chung, Le Hou, Shayne Longpre, Barret Zoph, Yi Tay, William Fedus,
Yunxuan Li, Xuezhi Wang, Mostafa Dehghani, Siddhartha Brahma, et al. 2022.
Scaling instruction-finetuned language models. arXiv preprint arXiv:2210.11416
(2022).
[25] Yael Dubinsky, Julia Rubin, Thorsten Berger, Slawomir Duszynski, Martin Becker,
and Krzysztof Czarnecki. 2013. An exploratory study of cloning in industrial
software product lines. In 2013 17th European Conference on Software Maintenance
and Reengineering. IEEE, 25–34.
[26] Aparna Elangovan, Jiayuan He, and Karin Verspoor. 2021. Memorization vs.
Generalization: Quantifying Data Leakage in NLP Performance Evaluation. In
Proceedings of the 16th Conference of the European Chapter of the Association for
Computational Linguistics: Main Volume. 1325–1335.
[27] Clément Elbaz, Louis Rilling, and Christine Morin. 2020. Automated Keyword
Extraction from" One-day" Vulnerabilities at Disclosure. In NOMS 2020-2020
IEEE/IFIP Network Operations and Management Symposium. IEEE, 1–9.
[28] Neil A Ernst, Steve Easterbrook, and John Mylopoulos. 2010. Code forking in
open-source software: a requirements perspective. arXiv preprint arXiv:1004.2889
(2010).
[29] Zhiyu Fan, Xiang Gao, Martin Mirchev, Abhik Roychoudhury, and Shin Hwei
Tan. 2023. Automated repair of programs from large language models. In 2023
IEEE/ACM 45th International Conference on Software Engineering (ICSE). IEEE,
1469–1481.
[30] Wolfram Fenske, Jens Meinicke, Sandro Schulze, Steffen Schulze, and Gunter
Saake. 2017. Variant-preserving refactorings for migrating cloned products to
a product line. In 2017 IEEE 24th International Conference on Software Analysis,
Evolution and Reengineering (SANER). IEEE, 316–326.
[31] Georgios Gousios, Martin Pinzger, and Arie van Deursen. 2014. An exploratory
study of the pull-based software development model. In Proceedings of the 36th
international conference on software engineering. 345–355.
[32] Georgios Gousios, Margaret-Anne Storey, and Alberto Bacchelli. 2016. Work
practices and challenges in pull-based development: The contributor’s perspective.
In Proceedings of the 38th International Conference on Software Engineering.
285–296.
[33] Edward J Hu, Phillip Wallis, Zeyuan Allen-Zhu, Yuanzhi Li, Shean Wang, Lu
Wang,Weizhu Chen, et al. 2021. LoRA: Low-Rank Adaptation of Large Language
Models. In International Conference on Learning Representations.
[34] Takashi Ishio, Yusuke Sakaguchi, Kaoru Ito, and Katsuro Inoue. 2017. Source file
set search for clone-and-own reuse analysis. In 2017 IEEE/ACM 14th International
Conference on Mining Software Repositories (MSR). IEEE, 257–268.
[35] Jing Jiang, David Lo, Jiahuan He, Xin Xia, Pavneet Singh Kochhar, and Li Zhang.
2017. Why and how developers fork what from whom in GitHub. Empirical
Software Engineering 22 (2017), 547–578.
[36] Denis Kocetkov, Raymond Li, Loubna Ben Allal, Jia Li, Chenghao Mou, Carlos
Muñoz Ferrandis, Yacine Jernite, Margaret Mitchell, Sean Hughes, Thomas
Wolf, et al. 2022. The stack: 3 tb of permissively licensed source code. arXiv
preprint arXiv:2211.15533 (2022).
[37] Hugo Larochelle, Dumitru Erhan, and Yoshua Bengio. 2008. Zero-data learning
of new tasks.. In AAAI, Vol. 1. 3.
[38] Raymond Li, Loubna Ben Allal, Yangtian Zi, Niklas Muennighoff, Denis Kocetkov,
Chenghao Mou, Marc Marone, Christopher Akiki, Jia Li, Jenny Chim, et al. 2023.
StarCoder: may the source be with you! arXiv preprint arXiv:2305.06161 (2023).
[39] Tiedong Liu and Bryan Kian Hsiang Low. 2023. Goat: Fine-tuned LLaMA Outperforms
GPT-4 on Arithmetic Tasks. arXiv preprint arXiv:2305.14201 (2023).
[40] Zhongxin Liu, Xin Xia, David Lo, Meng Yan, and Shanping Li. 2021. Just-In-
Time Obsolete Comment Detection and Update. IEEE Transactions on Software
Engineering (2021).
[41] Ilya Loshchilov and Frank Hutter. 2017. Decoupled weight decay regularization.
arXiv preprint arXiv:1711.05101 (2017).
[42] Long Ouyang, Jeffrey Wu, Xu Jiang, Diogo Almeida, Carroll Wainwright, Pamela
Mishkin, Chong Zhang, Sandhini Agarwal, Katarina Slama, Alex Ray, et al. 2022.
Training language models to follow instructions with human feedback. Advances
in Neural Information Processing Systems 35 (2022), 27730–27744.
[43] Alec Radford, Karthik Narasimhan, Tim Salimans, Ilya Sutskever, et al. 2018.
Improving language understanding by generative pre-training. (2018).
[44] Baishakhi Ray and Miryung Kim. 2012. A case study of cross-system porting in
forked projects. In Proceedings of the ACM SIGSOFT 20th International Symposium
on the Foundations of Software Engineering. 1–11.
[45] Baishakhi Ray, Miryung Kim, Suzette Person, and Neha Rungta. 2013. Detecting
and characterizing semantic inconsistencies in ported code. In 2013 28th
IEEE/ACM International Conference on Automated Software Engineering (ASE).
IEEE, 367–377.
[46] David Reid, Mahmoud Jahanshahi, and Audris Mockus. 2022. The extent of
orphan vulnerabilities from code reuse in open source software. In Proceedings
of the 44th International Conference on Software Engineering. 2104–2115.
[47] Luyao Ren. 2019. Automated patch porting across forked projects. In Proceedings
of the 2019 27th ACM Joint Meeting on European Software Engineering Conference
and Symposium on the Foundations of Software Engineering. 1199–1201.
[48] Gregorio Robles and JesúsMGonzález-Barahona. 2012. A comprehensive study of
software forks: Dates, reasons and outcomes. In Open Source Systems: Long-Term
Sustainability: 8th IFIP WG 2.13 International Conference, OSS 2012, Hammamet,
Tunisia, September 10-13, 2012. Proceedings 8. Springer, 1–14.
[49] Baptiste Roziere, Jonas Gehring, Fabian Gloeckle, Sten Sootla, Itai Gat, Xiaoqing
Ellen Tan, Yossi Adi, Jingyu Liu, Tal Remez, Jérémy Rapin, et al. 2023. Code
llama: Open foundation models for code. arXiv preprint arXiv:2308.12950 (2023).
[50] Victor Sanh, Albert Webson, Colin Raffel, Stephen H Bach, Lintang Sutawika,
Zaid Alyafeai, Antoine Chaffin, Arnaud Stiegler, Teven Le Scao, Arun Raja, et al.
2021. Multitask prompted training enables zero-shot task generalization. arXiv
preprint arXiv:2110.08207 (2021).
[51] Melanie Sclar, Yejin Choi, Yulia Tsvetkov, and Alane Suhr. 2023. Quantifying
Language Models’ Sensitivity to Spurious Features in Prompt Design or: How I
learned to start worrying about prompt formatting. In The Twelfth International
Conference on Learning Representations.
[52] Ridwan Shariffdeen, Xiang Gao, Gregory J Duck, Shin Hwei Tan, Julia Lawall, and
Abhik Roychoudhury. 2021. Automated patch backporting in Linux (experience
paper). In Proceedings of the 30th ACM SIGSOFT International Symposium on
Software Testing and Analysis. 633–645.
[53] Ridwan Salihin Shariffdeen, Shin Hwei Tan, Mingyuan Gao, and Abhik Roychoudhury.
2020. Automated patch transplantation. ACM Transactions on Software
Engineering and Methodology (TOSEM) 30, 1 (2020), 1–36.
[54] Rohan Taori, Ishaan Gulrajani, Tianyi Zhang, Yann Dubois, Xuechen Li, Carlos
Guestrin, Percy Liang, and Tatsunori B. Hashimoto. 2023. Stanford Alpaca: An
Instruction-following LLaMA model. https://github.com/tatsu-lab/stanford_
alpaca.
[55] Hugo Touvron, Thibaut Lavril, Gautier Izacard, Xavier Martinet, Marie-Anne
Lachaux, Timothée Lacroix, Baptiste Rozière, Naman Goyal, Eric Hambro, Faisal
Automating Zero-Shot Patch Porting for Hard Forks ISSTA ’24, September 16–20, 2024, Vienna, Austria
Azhar, et al. 2023. Llama: Open and efficient foundation language models. arXiv
preprint arXiv:2302.13971 (2023).
[56] Xinda Wang, Kun Sun, Archer Batcheller, and Sushil Jajodia. 2019. Detecting" 0-
day" vulnerability: An empirical study of secret security patch in OSS. In 2019 49th
Annual IEEE/IFIP International Conference on Dependable Systems and Networks
(DSN). IEEE, 485–492.
[57] Jason Wei, Maarten Bosma, Vincent Y Zhao, Kelvin Guu, Adams Wei Yu, Brian
Lester, Nan Du, Andrew M Dai, and Quoc V Le. 2021. Finetuned language models
are zero-shot learners. arXiv preprint arXiv:2109.01652 (2021).
[58] Jason Wei, Yi Tay, Rishi Bommasani, Colin Raffel, Barret Zoph, Sebastian
Borgeaud, Dani Yogatama, Maarten Bosma, Denny Zhou, Donald Metzler, et al.
2022. Emergent abilities of large language models. arXiv preprint arXiv:2206.07682
(2022).
[59] Shijie Wu, Ozan Irsoy, Steven Lu, Vadim Dabravolski, Mark Dredze, Sebastian
Gehrmann, Prabhanjan Kambadur, David Rosenberg, and Gideon Mann.
2023. Bloomberggpt: A large language model for finance. arXiv preprint
arXiv:2303.17564 (2023).
[60] Yulun Wu, Zeliang Yu, Ming Wen, Qiang Li, Deqing Zou, and Hai Jin. 2023.
Understanding the Threats of Upstream Vulnerabilities to Downstream Projects
in the Maven Ecosystem. In 2023 IEEE/ACM 45th International Conference on
Software Engineering (ICSE). IEEE, 1046–1058.
[61] Chunqiu Steven Xia, Yuxiang Wei, and Lingming Zhang. 2023. Automated
program repair in the era of large pre-trained language models. In Proceedings of
the 45th International Conference on Software Engineering (ICSE 2023). Association
for Computing Machinery.
[62] Frank F Xu, Uri Alon, Graham Neubig, and Vincent Josua Hellendoorn. 2022. A
systematic evaluation of large language models of code. In Proceedings of the 6th
ACM SIGPLAN International Symposium on Machine Programming. 1–10.
[63] Wayne Xin Zhao, Kun Zhou, Junyi Li, Tianyi Tang, Xiaolei Wang, Yupeng Hou,
Yingqian Min, Beichen Zhang, Junjie Zhang, Zican Dong, et al. 2023. A survey
of large language models. arXiv preprint arXiv:2303.18223 (2023).
[64] Shurui Zhou. 2020. Improving Collaboration Efficiency in Fork-based Development.
Ph.D. Dissertation. Carnegie Mellon University.
[65] Shurui Zhou, Bogdan Vasilescu, and Christian Kästner. 2019. What the fork: A
study of inefficient and efficient forking practices in social coding. In Proceedings
of the 2019 27th ACM joint meeting on european software engineering conference
and symposium on the foundations of software engineering. 350–361.
[66] Shurui Zhou, Bogdan Vasilescu, and Christian Kästner. 2020. How has forking
changed in the last 20 years? a study of hard forks on github. In Proceedings of
the ACM/IEEE 42nd International Conference on Software Engineering. 445–456.
Received 16-DEC-2023; accepted 2024-03-02