# API服务配置
API_BASE_URL = "http://10.150.10.76:5002"
API_MODEL_NAME = "starcoder-lora"
USE_API_SERVICE = True  # 设置为True使用API服务，False使用本地模型

# 本地模型配置（当USE_API_SERVICE=False时使用）
# we use LLM_PATH + LLM_NAME_PATH_MAPPING[llm_name] to determine the weight path of a given llm_name
LLM_PATH = "/root/Star-Coder-Model/"
LLM_NAME_PATH_MAPPING = {"starcoder": "starcoder"}

# 项目路径配置
MODEL_PATH = "./models/"
DATA_PATH = "../src/PPatHF-main/PPatHF-main/data/"
OUTPUT_PATH = "./outputs/"

EXTRACTED_METRIC_PATH = "./metrics/"

# API请求配置
API_TIMEOUT = 60  # API请求超时时间（秒）
MAX_TOKENS = 8192  # 最大生成token数
TEMPERATURE = 0.1  # 生成温度