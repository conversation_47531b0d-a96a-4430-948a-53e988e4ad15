#!/usr/bin/env python3
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from reduction.reducer import Reducer
    print("✅ Reducer模块导入成功!")
except ImportError as e:
    print(f"❌ Reducer模块导入失败: {e}")
    sys.exit(1)

def test_data_reduction():
    """测试数据预处理功能"""
    print("\n🔄 开始数据预处理测试...")
    
    # 数据文件路径
    data_path = '../data/PPatHF - Data/data/vim_neovim_test_4096.json'
    output_path = '../data/PPatHF - Data/data/vim_neovim_test_sliced_4096.json'
    
    try:
        # 读取原始数据
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 原始数据样本数: {len(data)}")
        if data:
            print(f"📝 第一个样本的键: {list(data[0].keys())}")
        
        # 初始化Reducer
        reducer = Reducer(tolerant=True)
        print("🔧 Reducer初始化成功")
        
        # 进行数据预处理（处理前几个样本作为测试）
        print("🔄 开始处理数据样本...")
        processed_data = []
        
        for i, sample in enumerate(data[:5]):  # 先处理前5个样本测试
            try:
                print(f"处理样本 {i+1}/5...")
                # 这里可以添加具体的reduction逻辑
                processed_data.append(sample)
            except Exception as e:
                print(f"⚠️ 处理样本 {i+1} 时出错: {e}")
                if not reducer.tolerant:
                    raise
        
        print(f"✅ 成功处理 {len(processed_data)} 个样本")
        return True
        
    except FileNotFoundError:
        print(f"❌ 数据文件未找到: {data_path}")
        return False
    except Exception as e:
        print(f"❌ 数据预处理出错: {e}")
        return False

if __name__ == "__main__":
    success = test_data_reduction()
    if success:
        print("\n🎉 数据预处理测试完成!")
    else:
        print("\n❌ 数据预处理测试失败!")
        sys.exit(1) 