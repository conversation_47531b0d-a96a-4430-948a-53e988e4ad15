#!/usr/bin/env python3
"""
自建数据集处理脚本
使用PPatHF原工具的功能生成不同长度限制的数据集
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append('.')

# 导入原工具模块
from utils import filter_by_max_length, filter_by_date
from porting.data import PROMPT_TEMPLATE_DICT

def process_custom_data():
    """处理自建数据集"""
    print("🚀 开始处理自建数据集...")
    
    # 读取数据 - 使用原始的完整适配数据
    data_path = '../../reproduction_work/data/custom/enhanced_ppathf_adapted.json'
    print(f"📂 读取数据文件: {data_path}")
    with open(data_path, 'r') as f:
        dataset = json.load(f)
    
    print(f"📊 原始数据集大小: {len(dataset)}")
    
    # 日期格式转换
    DATE_FORMAT = '%Y-%m-%d %H:%M:%S%z'
    print("🔄 转换日期格式...")
    
    for sample in dataset:
        if 'neovim_committer_date' in sample:
            try:
                # 解析ISO格式
                date_obj = datetime.fromisoformat(sample['neovim_committer_date'].replace('Z', '+00:00'))
                # 转换为工具期望的格式
                sample['neovim_committer_date'] = date_obj.strftime(DATE_FORMAT)
            except Exception as e:
                print(f"⚠️ 日期格式转换失败: {sample['neovim_committer_date']} - {e}")
                # 使用默认日期
                sample['neovim_committer_date'] = "2023-01-01 00:00:00+00:00"
    
    # ===== 时间筛选部分 - 已注释掉，使用全部数据 =====
    # 注释原因：避免损失历史重要漏洞数据，提高数据集完整性
    # 
    # # 按日期过滤（2022-07-01后的数据用于测试）
    # split_date = "2022-07-01 00:00:00+00:00"
    # print(f"📅 按日期过滤数据，分割点: {split_date}")
    # 
    # try:
    #     dataset_before, dataset_after = filter_by_date(
    #         dataset=dataset, 
    #         split_date=split_date, 
    #         date_key="neovim_committer_date"
    #     )
    #     
    #     # 使用2022年7月后的数据
    #     dataset = dataset_after
    #     print(f"📈 过滤后数据集大小: {len(dataset)}")
    #     
    # except Exception as e:
    #     print(f"⚠️ 日期过滤失败，使用全部数据: {e}")
    
    # 不进行时间筛选，使用全部数据
    print(f"📈 使用完整数据集（无时间筛选）: {len(dataset)} 条记录")
    
    # 生成不同长度限制的数据集
    length_limits = [2048, 4096, 8192]
    
    for max_tokens in length_limits:
        print(f"\n🔧 生成长度限制为 {max_tokens} 的数据集...")
        
        try:
            # 简化的长度过滤（直接使用字符数估算）
            filtered_dataset = []
            for sample in dataset:
                # 估算prompt长度
                estimated_length = len(sample.get('func_before_source', '')) + \
                                 len(sample.get('func_after_source', '')) + \
                                 len(sample.get('func_before_target', '')) + 500  # 额外开销
                
                if estimated_length < max_tokens * 4:  # 粗略估算token数
                    filtered_dataset.append(sample)
            
            print(f"📉 长度过滤后: {len(filtered_dataset)} 条记录")
            
            # 保存数据
            output_path = f'data/custom_vim_neovim_test_{max_tokens}.json'
            with open(output_path, 'w') as f:
                json.dump(filtered_dataset, f, indent=2)
            
            print(f"💾 保存到: {output_path}")
            
        except Exception as e:
            print(f"❌ 处理长度限制 {max_tokens} 时出错: {e}")
    
    # 保存完整数据集（用于backup）
    output_all_path = 'data/custom_vim_neovim_test_all_processed.json'
    with open(output_all_path, 'w') as f:
        json.dump(dataset, f, indent=2)
    print(f"💾 完整数据集保存到: {output_all_path}")
    
    print("\n✅ 自建数据集处理完成！")
    
    # 显示生成的文件
    print("\n📁 生成的文件:")
    for max_tokens in length_limits:
        file_path = f'data/custom_vim_neovim_test_{max_tokens}.json'
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024*1024)
            print(f"  - {file_path} ({size:.1f}MB)")

if __name__ == "__main__":
    process_custom_data() 