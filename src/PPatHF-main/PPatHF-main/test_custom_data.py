#!/usr/bin/env python3
"""
自建数据集测试脚本
验证数据格式与原工具的兼容性，并运行简单的评估测试
"""

import json
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append('.')

# 导入原工具模块
from porting.data import PROMPT_TEMPLATE_DICT, Testset
from test import generate_dummy, cal_metrics
from metrics import MetricsSampleWise

def test_custom_data():
    """测试自建数据集"""
    print("🧪 开始测试自建数据集...")
    
    # 测试不同大小的数据集
    test_files = [
        'data/custom_vim_neovim_test_2048.json',
        'data/custom_vim_neovim_test_4096.json',
        'data/custom_vim_neovim_test_8192.json'
    ]
    
    for data_path in test_files:
        if not os.path.exists(data_path):
            print(f"⚠️ 文件不存在: {data_path}")
            continue
            
        print(f"\n📊 测试数据集: {data_path}")
        
        try:
            # 1. 测试数据加载
            with open(data_path, 'r') as f:
                dataset = json.load(f)
            
            print(f"✅ 数据加载成功: {len(dataset)} 条记录")
            
            # 2. 测试Prompt生成
            if dataset:
                sample = dataset[0]
                task = sample.get('task', 'trans_patch')
                
                instruction = PROMPT_TEMPLATE_DICT[task]['instruction'].format_map(sample)
                context = PROMPT_TEMPLATE_DICT[task]['context'].format_map(sample)
                output = PROMPT_TEMPLATE_DICT[task]['output'].format_map(sample)
                
                full_prompt = instruction + context + output
                print(f"✅ Prompt生成成功: {len(full_prompt)} 字符")
                
                # 显示前几行内容
                print("📄 Prompt示例:")
                print(f"  指令: {instruction[:100]}...")
                print(f"  上下文前100字符: {context[:100]}...")
            
            # 3. 生成dummy预测结果用于测试评估
            test_size = min(5, len(dataset))  # 只测试前5条
            test_subset = dataset[:test_size]
            
            # 保存测试子集
            subset_path = data_path.replace('.json', '_test_subset.json')
            with open(subset_path, 'w') as f:
                json.dump(test_subset, f, indent=2)
            
            # 生成dummy输出（使用func_before_target作为预测结果）
            dummy_output_path = data_path.replace('.json', '_dummy_output.json')
            generate_dummy(subset_path, dummy_output_path)
            print(f"✅ 生成测试预测: {dummy_output_path}")
            
            # 4. 测试评估功能
            try:
                print("🔍 运行评估测试...")
                result = cal_metrics(
                    data_path=subset_path,
                    output_path=dummy_output_path,
                    do_recover=False,  # 暂时不使用recover功能
                    reference_key='func_after_target',
                    reference_before_key='func_before_target',
                    save_sample_wise_results=False
                )
                
                if result:
                    print("✅ 评估测试成功")
                    # 显示一些基本统计
                    exact_matches = sum(1 for r in result if r.get('exact_match', False))
                    print(f"📈 精确匹配: {exact_matches}/{len(result)}")
                
            except Exception as e:
                print(f"⚠️ 评估测试失败: {e}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n🎉 自建数据集测试完成！")

def test_simple_generation():
    """运行简单的生成测试"""
    print("\n🚀 运行简单生成测试...")
    
    data_path = 'data/custom_vim_neovim_test_2048.json'
    if not os.path.exists(data_path):
        print(f"❌ 测试数据不存在: {data_path}")
        return
    
    # 读取少量数据进行测试
    with open(data_path, 'r') as f:
        dataset = json.load(f)
    
    # 只测试前3条数据
    test_data = dataset[:3]
    test_file = 'data/simple_test_3_samples.json'
    
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print(f"📝 创建测试文件: {test_file} (3 条记录)")
    
    # 生成dummy预测作为测试
    dummy_output = 'outputs/simple_test_dummy_output.json'
    generate_dummy(test_file, dummy_output)
    
    print(f"✅ 生成测试预测: {dummy_output}")
    print("🎯 可以使用这些文件进行进一步的模型推理测试")

if __name__ == "__main__":
    test_custom_data()
    test_simple_generation() 