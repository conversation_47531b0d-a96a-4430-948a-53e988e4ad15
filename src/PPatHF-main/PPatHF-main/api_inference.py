#!/usr/bin/env python3
import json
import requests
import sys
import os
from typing import List, Dict
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入配置
from config import API_BASE_URL, API_MODEL_NAME, API_TIMEOUT, MAX_TOKENS, TEMPERATURE

class PPatHFAPIInference:
    def __init__(self):
        self.api_base_url = API_BASE_URL
        self.model_name = API_MODEL_NAME
        self.timeout = API_TIMEOUT
        self.max_tokens = min(MAX_TOKENS, 2048)  # 限制最大token数
        self.temperature = TEMPERATURE
        
    def call_api(self, prompt: str) -> str:
        """调用StarCoder API进行代码生成"""
        url = f"{self.api_base_url}/v1/completions"
        
        # 优化payload，添加适当的停止词
        payload = {
            "prompt": prompt,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "stop": ["### Function", "\n\n\n", "```"]
        }
        
        print(f"🔧 API请求参数: max_tokens={self.max_tokens}, temperature={self.temperature}")
        print(f"📏 prompt长度: {len(prompt)} 字符")
        
        try:
            response = requests.post(
                url, 
                json=payload, 
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            print(f"📊 HTTP状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ API错误响应: {response.text}")
                response.raise_for_status()
            
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["text"].strip()
            else:
                raise Exception(f"API返回格式错误: {result}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {e}")
    
    def create_simple_prompt(self, sample: Dict) -> str:
        """创建正确格式的补丁移植prompt - 使用原始仓库的格式"""
        # 获取代码片段，限制长度避免超过token限制
        func_before_source = sample.get('func_before_source', '')[:1000]  # 限制长度
        func_after_source = sample.get('func_after_source', '')[:1000]
        func_before_target = sample.get('func_before_target', '')[:1000]
        
        # 使用原始仓库的PROMPT_TEMPLATE格式
        prompt = f"""Below is a patch (including function before and function after) from vim, paired with a corresponding function before from neovim. Adapt the patch from vim to neovim by generating the function after based on the given function before.

### Function Before (vim):
{func_before_source}

### Function After (vim):
{func_after_source}

### Function Before (neovim):
{func_before_target}

### Function After (neovim):
"""
        
        return prompt
    
    def run_inference(self, data: List[Dict], max_samples: int = 3) -> List[Dict]:
        """运行推理，生成补丁移植结果"""
        results = []
        
        print(f"🚀 开始推理，处理 {min(len(data), max_samples)} 个样本...")
        
        for i, sample in enumerate(data[:max_samples]):
            print(f"\n📝 处理样本 {i+1}/{min(len(data), max_samples)}...")
            print(f"🏷️ 样本信息: commit_id={sample.get('commit_id_source', 'N/A')}")
            
            try:
                # 创建简化的prompt
                prompt = self.create_simple_prompt(sample)
                
                # 调用API
                print("🔄 调用API生成代码...")
                start_time = time.time()
                generated_code = self.call_api(prompt)
                end_time = time.time()
                
                print(f"⚡ 生成完成，耗时: {end_time - start_time:.2f}秒")
                print(f"📄 生成代码长度: {len(generated_code)} 字符")
                
                # 保存结果
                result = sample.copy()
                result['generated_func_after_target'] = generated_code
                result['generation_time'] = end_time - start_time
                result['prompt_used'] = prompt[:200] + "..." if len(prompt) > 200 else prompt
                results.append(result)
                
                # 显示生成的代码片段（前200字符）
                preview = generated_code[:200] + "..." if len(generated_code) > 200 else generated_code
                print(f"🔍 生成预览: {preview}")
                
                # 添加延迟避免API压力
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ 处理样本 {i+1} 时出错: {e}")
                # 仍然保存原始样本，标记错误
                result = sample.copy()
                result['generated_func_after_target'] = f"ERROR: {str(e)}"
                result['generation_time'] = 0
                results.append(result)
        
        return results

def test_simple_api():
    """测试简单的API调用"""
    print("🧪 测试简单API调用...")
    
    inferencer = PPatHFAPIInference()
    
    try:
        simple_prompt = "def add_numbers(a, b):"
        result = inferencer.call_api(simple_prompt)
        print(f"✅ 简单测试成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 简单测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 PPatHF API推理工具")
    print(f"🔗 API地址: {API_BASE_URL}")
    print(f"🤖 模型: {API_MODEL_NAME}")
    
    # 先进行简单测试
    if not test_simple_api():
        print("❌ API连接测试失败，请检查服务状态")
        return
    
    # 读取测试数据 - 使用高质量数据集
    data_path = '../../../reproduction_work/data/quality_fixed/custom_vim_neovim_quality_4096.json'
    
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"📊 加载数据: {len(data)} 个样本")
    except FileNotFoundError:
        print(f"❌ 数据文件未找到: {data_path}")
        return
    
    # 初始化推理器
    inferencer = PPatHFAPIInference()
    
    # 运行推理
    try:
        results = inferencer.run_inference(data, max_samples=20)  # 增加到20个样本进行更全面的测试
        
        # 保存结果
        output_path = '../../../outputs/quality_4096_api_inference_results.json'
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 推理完成！结果已保存到: {output_path}")
        print(f"📈 成功处理: {len(results)} 个样本")
        
        # 显示成功和失败的统计
        success_count = len([r for r in results if not r['generated_func_after_target'].startswith('ERROR')])
        print(f"📊 成功生成: {success_count}/{len(results)}")
        
    except Exception as e:
        print(f"❌ 推理过程出错: {e}")

if __name__ == "__main__":
    main() 