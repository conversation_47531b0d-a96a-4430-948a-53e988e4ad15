# PPatHF 复现工作目录

本目录包含了对 PPatHF 工具的复现工作，所有文档、代码和输出都已从原工具代码中分离出来，便于独立管理和后续测试。

## 目录结构

```
reproduction_work/
├── README.md                    # 本说明文档
├── requirements.txt             # Python依赖包列表
├── docs/                        # 文档目录
│   ├── 复现进度记录.md          # 复现过程的详细记录
│   ├── API输出类别分析报告.md   # API输出结果的统计分析
│   ├── 论文案例API输出分析.md   # 基于论文案例的分析结果
│   ├── django样本示例原因分析.md # Django样本的详细分析
│   ├── 复现规则.md              # 复现过程中的规则记录
│   ├── 工具缺陷记录.md          # 发现的工具问题记录
│   └── Shariffdeen 等 - 2021 - Automated Patch Transplantation.pdf  # 原始论文
├── code/                        # 复现代码目录
│   ├── eval_all_data.py         # 全量数据评估脚本
│   ├── eval_short_samples.py    # 短样本评估脚本
│   ├── analyze_output_categories.py  # 输出类别分析脚本
│   ├── REPRODUCTION_GUIDE.md    # 复现指南文档
│   ├── analysis_results/        # 分析结果目录
│   │   ├── category_analysis_report.md     # 类别分析报告
│   │   ├── category_analysis_results.json  # 分析结果JSON
│   │   └── category_statistics_summary.json # 统计摘要
│   ├── outputs/                 # 代码输出目录
│   │   └── (各种测试结果的JSON文件)
│   ├── test/                    # 测试脚本目录
├── data/                        # 数据目录
│   ├── organized/               # 整理后的数据集
│   │   ├── original/            # 原始quality_fixed数据集
│   │   ├── sliced/              # Reduction处理后的数据集
│   │   └── data_organization_summary.md  # 数据组织说明
│   ├── quality_fixed/           # 质量修复数据集
│   ├── custom/                  # 自建数据集
│   ├── backup_*/                # 数据备份
│   └── data_analysis_tools/     # 数据分析工具集
│   │   ├── simple_api_test.py   # 简单API测试
│   │   ├── quick_api_test.py    # 快速API测试
│   │   └── short_sample_test.py # 短样本测试
│   └── *.log                    # 运行日志文件
├── data/                        # 数据处理工具
│   └── ppathf_to_json_converter.py  # 数据格式转换工具
└── outputs/                     # 输出文件目录
    └── (包含各种测试和分析的输出结果)
```

## 使用方法

### 1. 环境准备
```bash
cd reproduction_work
pip install -r requirements.txt
```

### 2. 数据准备
使用 `data/ppathf_to_json_converter.py` 将您的数据转换为工具所需的JSON格式。

### 3. 运行测试
- 快速测试：`python code/test/quick_api_test.py`
- 完整评估：`python code/eval_all_data.py`
- 输出分析：`python code/analyze_output_categories.py`

### 4. 查看结果
- 查看 `outputs/` 目录中的输出文件
- 参考 `docs/` 目录中的分析报告

## 与原工具的关系

- **原工具代码位置**：`../src/PPatHF-main/PPatHF-main/`
- **本目录**：独立的复现工作空间，不依赖原工具目录
- **数据共享**：可以使用 `../data/` 中的原始数据文件

## 注意事项

1. 本目录中的代码已经过测试，可以独立运行
2. 如需使用自己的数据，请先使用数据转换工具进行格式转换
3. 所有分析报告和文档都基于实际的复现结果
4. 推荐在运行大规模测试前先运行快速测试确认环境配置正确

## 后续工作

您可以在此基础上：
- 使用自己的数据集进行测试
- 修改评估参数和配置
- 扩展分析功能
- 添加新的测试用例

## 联系信息

如有问题，请参考 `docs/复现进度记录.md` 中的详细记录，或查看具体的分析报告。 