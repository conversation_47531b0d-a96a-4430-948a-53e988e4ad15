# 数据集处理脚本路径处理机制详解

## 📁 整体项目结构

我们的项目路径结构如下：

```
/home/<USER>/nas-files/Re-paper/ppathf/
├── reproduction_work/                    # 我们的工作目录
│   ├── data_adapter.py                  # 数据适配器脚本
│   ├── original_tool_integration.py     # 原工具集成脚本
│   └── data/custom/                     # 适配后数据存储目录
│       ├── enhanced_ppathf_adapted.json
│       ├── adaptation_report.json
│       └── *.log
├── src/PPatHF-main/PPatHF-main/          # 原工具目录
│   ├── data/                            # 原工具数据目录
│   │   ├── custom_vim_neovim_test_*.json # 我们适配的数据
│   ├── config.py                        # 原工具配置文件
│   ├── predict.py                       # 原工具推理脚本
│   └── ...
└── data/data/enhanced_data/              # 原始自建数据集
    └── enhanced_and_nvd_dataset.json
```

## 🔧 路径处理机制详解

### 1. data_adapter.py 路径处理

#### 初始化阶段路径设置

```python
class CustomDataAdapter:
    def __init__(self, 
                 input_file: str = "../data/data/enhanced_data/enhanced_and_nvd_dataset.json",
                 output_file: str = "data/custom/enhanced_ppathf_adapted.json"):
        """
        路径说明：
        - input_file: 相对于reproduction_work目录的原始数据路径
        - output_file: 相对于reproduction_work目录的输出路径
        """
        self.input_file = Path(input_file)      # 转换为Path对象
        self.output_file = Path(output_file)    # 转换为Path对象
        
        # 自动创建输出目录
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
```

#### 实际路径解析

当脚本在 `reproduction_work/` 目录下运行时：

- **输入路径**: `../data/data/enhanced_data/enhanced_and_nvd_dataset.json`
  - 解析为: `/home/<USER>/nas-files/Re-paper/ppathf/data/data/enhanced_data/enhanced_and_nvd_dataset.json`
  
- **输出路径**: `data/custom/enhanced_ppathf_adapted.json`
  - 解析为: `/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json`

- **日志路径**: 自动设置为输出目录下的 `data_adaptation.log`

### 2. original_tool_integration.py 路径处理

#### 初始化阶段路径设置

```python
class OriginalToolIntegration:
    def __init__(self, 
                 adapted_data_path: str = "data/custom/enhanced_ppathf_adapted.json",
                 original_tool_dir: str = "../src/PPatHF-main/PPatHF-main"):
        """
        路径说明：
        - adapted_data_path: 适配后数据的路径（来自data_adapter.py的输出）
        - original_tool_dir: 原工具目录的相对路径
        """
        self.adapted_data_path = Path(adapted_data_path)
        self.original_tool_dir = Path(original_tool_dir)
        self.data_dir = self.original_tool_dir / "data"        # 原工具数据目录
        self.config_file = self.original_tool_dir / "config.py" # 原工具配置文件
```

#### 实际路径解析

当脚本在 `reproduction_work/` 目录下运行时：

- **适配数据路径**: `data/custom/enhanced_ppathf_adapted.json`
  - 解析为: `/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json`
  
- **原工具目录**: `../src/PPatHF-main/PPatHF-main`
  - 解析为: `/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main`
  
- **原工具数据目录**: `../src/PPatHF-main/PPatHF-main/data`
  - 解析为: `/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/data`

#### 工作目录切换机制

在 `prepare_datasets_with_length_filtering()` 方法中：

```python
def prepare_datasets_with_length_filtering(self, source_file: Path):
    # 1. 在切换目录前先读取数据（避免路径问题）
    with open(source_file, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    # 2. 保存原工作目录
    original_cwd = os.getcwd()  # /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work
    
    # 3. 切换到原工具目录
    os.chdir(self.original_tool_dir)  # /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
    
    try:
        # 4. 在原工具目录下进行操作
        sys.path.insert(0, str(self.original_tool_dir))
        
        # 5. 保存文件时使用相对于原工具目录的路径
        output_file = Path("data") / f"custom_vim_neovim_test_{max_length}.json"
        # 实际保存到: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/data/custom_vim_neovim_test_*.json
        
    finally:
        # 6. 恢复原工作目录
        os.chdir(original_cwd)
        sys.path.remove(str(self.original_tool_dir))
```

### 3. 配置文件路径更新

在 `update_config()` 方法中：

```python
def update_config(self):
    # 备份原配置
    backup_file = self.config_file.with_suffix('.py.backup')
    shutil.copy2(self.config_file, backup_file)
    
    # 修改数据路径配置
    new_data_path = str(self.data_dir) + "/"
    # 将: DATA_PATH = "./src/PPatHF-main/data/PPatHF - Data/data/"
    # 改为: DATA_PATH = "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/data/"
```

## 🚀 脚本启动路径要求

### 推荐的启动方式

```bash
# 1. 确保在正确的工作目录
cd /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work

# 2. 运行数据适配器（处理相对路径）
python data_adapter.py --max-records 5

# 3. 运行原工具集成（处理工作目录切换）
python original_tool_integration.py
```

### 路径依赖关系

1. **data_adapter.py** 依赖：
   - 当前工作目录: `reproduction_work/`
   - 输入数据: `../data/data/enhanced_data/enhanced_and_nvd_dataset.json`
   - 输出目录: `data/custom/` (自动创建)

2. **original_tool_integration.py** 依赖：
   - 当前工作目录: `reproduction_work/`
   - 输入数据: `data/custom/enhanced_ppathf_adapted.json`
   - 目标目录: `../src/PPatHF-main/PPatHF-main/`

## ⚠️ 常见路径问题与解决方案

### 问题1: 找不到输入文件

```bash
# 错误信息
FileNotFoundError: [Errno 2] No such file or directory: '../data/data/enhanced_data/enhanced_and_nvd_dataset.json'

# 解决方案
# 确保在正确的目录下运行脚本
pwd  # 应该显示 .../ppathf/reproduction_work
ls -la ../data/data/enhanced_data/  # 确认文件存在
```

### 问题2: 工作目录切换问题

```bash
# 错误信息
[Errno 2] No such file or directory: '../src/PPatHF-main/PPatHF-main/data/custom_vim_neovim_test_2048.json'

# 解决方案已实现
# 在切换工作目录前先读取数据，在正确目录下保存文件
```

### 问题3: 配置文件路径不匹配

```bash
# 检查原工具配置
cat ../src/PPatHF-main/PPatHF-main/config.py | grep DATA_PATH

# 验证更新是否成功
ls -la ../src/PPatHF-main/PPatHF-main/data/custom_*
```

## 🔍 调试路径问题的方法

### 1. 检查当前工作目录

```python
import os
from pathlib import Path

print(f"当前工作目录: {os.getcwd()}")
print(f"脚本所在目录: {Path(__file__).parent}")
print(f"绝对路径示例: {Path('data/custom').absolute()}")
```

### 2. 验证路径存在性

```python
from pathlib import Path

input_path = Path("../data/data/enhanced_data/enhanced_and_nvd_dataset.json")
print(f"输入文件存在: {input_path.exists()}")
print(f"绝对路径: {input_path.absolute()}")

output_dir = Path("data/custom")
print(f"输出目录存在: {output_dir.exists()}")
output_dir.mkdir(parents=True, exist_ok=True)
```

### 3. 监控工作目录变化

```python
import os

def trace_directory_changes():
    print(f"进入函数时的工作目录: {os.getcwd()}")
    
    original_cwd = os.getcwd()
    os.chdir("../src/PPatHF-main/PPatHF-main")
    print(f"切换后的工作目录: {os.getcwd()}")
    
    # 执行操作...
    
    os.chdir(original_cwd)
    print(f"恢复后的工作目录: {os.getcwd()}")
```

## 📋 最佳实践建议

1. **总是在 `reproduction_work/` 目录下启动脚本**
2. **使用相对路径时要考虑工作目录的变化**
3. **使用 `Path` 对象而不是字符串处理路径**
4. **在切换工作目录前读取所需数据**
5. **及时恢复原工作目录，避免影响后续操作**
6. **使用绝对路径调试路径问题**

这样的路径处理机制确保了脚本的稳定性和可维护性，同时支持灵活的部署环境。 