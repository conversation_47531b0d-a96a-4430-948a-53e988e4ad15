# 快速开始指南

本指南帮助您快速开始使用复现的PPatHF工具进行测试。

## 🚀 快速启动

### 1. 环境准备
```bash
cd reproduction_work
pip install -r requirements.txt
```

### 2. 快速验证 (推荐首次运行)
```bash
# 运行快速API测试（约2-3分钟）
python code/test/quick_api_test.py
```

### 3. 使用您自己的数据

#### 数据格式转换
```bash
# 将您的数据转换为工具所需格式
python data/ppathf_to_json_converter.py --input your_data.txt --output your_data.json
```

#### 运行评估
```bash
# 对少量样本进行评估
python code/eval_short_samples.py --data_path your_data.json --max_samples 10

# 对全量数据进行评估（时间较长）
python code/eval_all_data.py --data_path your_data.json
```

## 📊 结果分析

### 查看输出类别分析
```bash
python code/analyze_output_categories.py --result_file outputs/your_result.json
```

### 主要输出文件
- `outputs/`: 包含所有测试结果的JSON文件
- `code/analysis_results/`: 包含详细的分析报告
- `code/*.log`: 运行日志文件

## 🔍 故障排除

### 常见问题

1. **API连接问题**
   - 检查网络连接
   - 确认API密钥配置正确

2. **内存不足**
   - 减少批处理大小
   - 使用`eval_short_samples.py`进行小规模测试

3. **输出格式问题**
   - 参考`docs/`目录中的分析报告
   - 查看`outputs/`中的示例输出文件

### 获取帮助

- 查看详细文档：`docs/复现进度记录.md`
- 参考分析报告：`docs/API输出类别分析报告.md`
- 检查代码指南：`code/REPRODUCTION_GUIDE.md`

## 📋 测试建议

1. **初次使用**：先运行`quick_api_test.py`确认环境正常
2. **小规模测试**：使用`eval_short_samples.py`测试少量数据
3. **大规模评估**：确认前两步正常后再运行`eval_all_data.py`

## 💡 提示

- 所有脚本都支持`--help`参数查看详细使用说明
- 建议保存运行日志以便问题排查
- 大规模测试前建议先在小样本上验证结果格式 