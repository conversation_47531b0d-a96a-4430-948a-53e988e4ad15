#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自建数据集质量分析脚本

快速分析数据集中commit信息的完整性，帮助理解数据质量问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_adapter import CustomDataAdapter

async def main():
    print("🔍 开始分析自建数据集质量...")
    
    # 创建适配器实例
    adapter = CustomDataAdapter()
    
    # 执行数据质量分析
    adapter.analyze_data_quality(max_records=1000)  # 分析前1000条记录
    
    print("\n💡 分析建议:")
    print("1. 如果大部分记录缺少commit信息，说明数据收集阶段需要改进")
    print("2. 可以考虑使用其他数据源补充commit信息")
    print("3. 对于unified_id中包含commit信息的记录，可以尝试提取")
    print("4. 建议优先处理commit信息完整的记录")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 