["#ifndef __LINUX_USERMODE_DRIVER_H__\n#define __LINUX_USERMODE_DRIVER_H__\n\n#include <linux/umh.h>\n#include <linux/path.h>\n\nstruct umd_info {\n\tconst char *driver_name;\n\tstruct file *pipe_to_umh;\n\tstruct file *pipe_from_umh;\n\tstruct path wd;\n\tstruct pid *tgid;\n};\nint umd_load_blob(struct umd_info *info, const void *data, size_t len);\nint umd_unload_blob(struct umd_info *info);\nint fork_usermode_driver(struct umd_info *info);\nvoid umd_cleanup_helper(struct umd_info *info);\n\n#endif /* __LINUX_USERMODE_DRIVER_H__ */", "#include <linux/bio.h>\n#include <linux/io.h>\n#include <xen/page.h>\n\nbool xen_biovec_phys_mergeable(const struct bio_vec *vec1,\n\t\t\t       const struct bio_vec *vec2)\n{\n\tunsigned long mfn1 = pfn_to_mfn(page_to_pfn(vec1->bv_page));\n\tunsigned long mfn2 = pfn_to_mfn(page_to_pfn(vec2->bv_page));\n\n\treturn mfn1 + PFN_DOWN(vec1->bv_offset + vec1->bv_len) == mfn2;\n}", "/* SPDX-License-Identifier: GPL-2.0 */\n#ifndef _NET_ESP_H\n#define _NET_ESP_H\n\n#include <linux/skbuff.h>\n\n#define ESP_SKB_FRAG_MAXSIZE (PAGE_SIZE << SKB_FRAG_PAGE_ORDER)\n\nstruct ip_esp_hdr;\n\nstatic inline struct ip_esp_hdr *ip_esp_hdr(const struct sk_buff *skb)\n{\n\treturn (struct ip_esp_hdr *)skb_transport_header(skb);\n}\n\nstruct esp_info {\n\tstruct\tip_esp_hdr *esph;\n\t__be64\tseqno;\n\tint\ttfclen;\n\tint\ttailen;\n\tint\tplen;\n\tint\tclen;\n\tint \tlen;\n\tint \tnfrags;\n\t__u8\tproto;\n\tbool\tinplace;\n};\n\nint esp_output_head(struct xfrm_state *x, struct sk_buff *skb, struct esp_info *esp);\nint esp_output_tail(struct xfrm_state *x, struct sk_buff *skb, struct esp_info *esp);\nint esp_input_done2(struct sk_buff *skb, int err);\nint esp6_output_head(struct xfrm_state *x, struct sk_buff *skb, struct esp_info *esp);\nint esp6_output_tail(struct xfrm_state *x, struct sk_buff *skb, struct esp_info *esp);\nint esp6_input_done2(struct sk_buff *skb, int err);\n#endif", "/* SPDX-License-Identifier: GPL-2.0 */\n/*\n * Author: <PERSON><PERSON><PERSON> Lv <lv<PERSON><PERSON><PERSON>@loongson.cn>\n *         <PERSON><PERSON><PERSON> <<EMAIL>>\n * Copyright (C) 2020-2022 Loongson Technology Corporation Limited\n */\n\n#ifndef _ASM_LOONGARCH_ACPI_H\n#define _ASM_LOONGARCH_ACPI_H\n\n#include <asm/suspend.h>\n\n#ifdef CONFIG_ACPI\nextern int acpi_strict;\nextern int acpi_disabled;\nextern int acpi_pci_disabled;\nextern int acpi_noirq;\nextern int pptt_enabled;\n\n#define acpi_os_ioremap acpi_os_ioremap\nvoid __iomem *acpi_os_ioremap(acpi_physical_address phys, acpi_size size);\n\nstatic inline void disable_acpi(void)\n{\n\tacpi_disabled = 1;\n\tacpi_pci_disabled = 1;\n\tacpi_noirq = 1;\n}\n\nstatic inline bool acpi_has_cpu_in_madt(void)\n{\n\treturn true;\n}\n\n#define MAX_CORE_PIC 256\n\nextern struct list_head acpi_wakeup_device_list;\nextern struct acpi_madt_core_pic acpi_core_pic[MAX_CORE_PIC];\n\nextern int __init parse_acpi_topology(void);\n\nstatic inline u32 get_acpi_id_for_cpu(unsigned int cpu)\n{\n\treturn acpi_core_pic[cpu_logical_map(cpu)].processor_id;\n}\n\n#endif /* !CONFIG_ACPI */\n\n#define ACPI_TABLE_UPGRADE_MAX_PHYS ARCH_LOW_ADDRESS_LIMIT\n\nextern int loongarch_acpi_suspend(void);\nextern int (*acpi_suspend_lowlevel)(void);\n\nstatic inline unsigned long acpi_get_wakeup_address(void)\n{\n#ifdef CONFIG_SUSPEND\n\treturn (unsigned long)loongarch_wakeup_start;\n#endif\n\treturn 0UL;\n}\n\n#endif /* _ASM_LOONGARCH_ACPI_H */", "#ifndef _ASM_X86_PAGE_32_DEFS_H\n#define _ASM_X86_PAGE_32_DEFS_H\n\n#include <linux/const.h>\n\n/*\n * This handles the memory map.\n *\n * A __PAGE_OFFSET of 0xC0000000 means that the kernel has\n * a virtual address space of one gigabyte, which limits the\n * amount of physical memory you can use to about 950MB.\n *\n * If you want more physical memory than this then see the CONFIG_HIGHMEM4G\n * and CONFIG_HIGHMEM64G options in the kernel configuration.\n */\n#define __PAGE_OFFSET\t\t_AC(CONFIG_PAGE_OFFSET, UL)\n\n#define THREAD_ORDER\t1\n#define THREAD_SIZE \t(PAGE_SIZE << THREAD_ORDER)\n\n#define DOUBLEFAULT_STACK 1\n#define NMI_STACK 0\n#define DEBUG_STACK 0\n#define MCE_STACK 0\n#define N_EXCEPTION_STACKS 1\n\n#ifdef CONFIG_X86_PAE\n/* 44=32+12, the limit we can fit into an unsigned long pfn */\n#define __PHYSICAL_MASK_SHIFT\t44\n#define __VIRTUAL_MASK_SHIFT\t32\n\n#else  /* !CONFIG_X86_PAE */\n#define __PHYSICAL_MASK_SHIFT\t32\n#define __VIRTUAL_MASK_SHIFT\t32\n#endif\t/* CONFIG_X86_PAE */\n\n/*\n * Kernel image size is limited to 512 MB (see in arch/x86/kernel/head_32.S)\n */\n#define KERNEL_IMAGE_SIZE\t(512 * 1024 * 1024)\n\n#ifndef __ASSEMBLY__\n\n/*\n * This much address space is reserved for vmalloc() and iomap()\n * as well as fixmap mappings.\n */\nextern unsigned int __VMALLOC_RESERVE;\nextern int sysctl_legacy_va_layout;\n\nextern void find_low_pfn_range(void);\nextern void setup_bootmem_allocator(void);\n\n#endif\t/* !__ASSEMBLY__ */\n\n#endif /* _ASM_X86_PAGE_32_DEFS_H */", "#ifndef _ASM_X86_PAGE_32_DEFS_H\n#define _ASM_X86_PAGE_32_DEFS_H\n\n#include <linux/const.h>\n\n/*\n * This handles the memory map.\n *\n * A __PAGE_OFFSET of 0xC0000000 means that the kernel has\n * a virtual address space of one gigabyte, which limits the\n * amount of physical memory you can use to about 950MB.\n *\n * If you want more physical memory than this then see the CONFIG_HIGHMEM4G\n * and CONFIG_HIGHMEM64G options in the kernel configuration.\n */\n#define __PAGE_OFFSET\t\t_AC(CONFIG_PAGE_OFFSET, UL)\n\n#define THREAD_ORDER\t1\n#define THREAD_SIZE \t(PAGE_SIZE << THREAD_ORDER)\n\n#define DOUBLEFAULT_STACK 1\n#define NMI_STACK 0\n#define DEBUG_STACK 0\n#define MCE_STACK 0\n#define N_EXCEPTION_STACKS 1\n\n#ifdef CONFIG_X86_PAE\n/* 44=32+12, the limit we can fit into an unsigned long pfn */\n#define __PHYSICAL_MASK_SHIFT\t44\n#define __VIRTUAL_MASK_SHIFT\t32\n\n#else  /* !CONFIG_X86_PAE */\n#define __PHYSICAL_MASK_SHIFT\t32\n#define __VIRTUAL_MASK_SHIFT\t32\n#endif\t/* CONFIG_X86_PAE */\n\n/*\n * Kernel image size is limited to 512 MB (see in arch/x86/kernel/head_32.S)\n */\n#define KERNEL_IMAGE_SIZE\t(512 * 1024 * 1024)\n\n#ifndef __ASSEMBLY__\n\n/*\n * This much address space is reserved for vmalloc() and iomap()\n * as well as fixmap mappings.\n */\nextern unsigned int __VMALLOC_RESERVE;\nextern int sysctl_legacy_va_layout;\n\nextern void find_low_pfn_range(void);\nextern void setup_bootmem_allocator(void);\n\n#endif\t/* !__ASSEMBLY__ */\n\n#endif /* _ASM_X86_PAGE_32_DEFS_H */", "// SPDX-License-Identifier: GPL-2.0\n/*\n * linux/arch/arm/mach-footbridge/cats-pci.c\n *\n * PCI bios-type initialisation for PCI machines\n *\n * Bits taken from various places.\n */\n#include <linux/kernel.h>\n#include <linux/pci.h>\n#include <linux/init.h>\n\n#include <asm/irq.h>\n#include <asm/mach/pci.h>\n#include <asm/mach-types.h>\n\n/* cats host-specific stuff */\nstatic int irqmap_cats[] = { IRQ_PCI, IRQ_IN0, IRQ_IN1, IRQ_IN3 };\n\nstatic u8 cats_no_swizzle(struct pci_dev *dev, u8 *pin)\n{\n\treturn 0;\n}\n\nstatic int cats_map_irq(const struct pci_dev *dev, u8 slot, u8 pin)\n{\n\tif (dev->irq >= 255)\n\t\treturn -1;\t/* not a valid interrupt. */\n\n\tif (dev->irq >= 128)\n\t\treturn dev->irq & 0x1f;\n\n\tif (dev->irq >= 1 && dev->irq <= 4)\n\t\treturn irqmap_cats[dev->irq - 1];\n\n\tif (dev->irq != 0)\n\t\tprintk(\"PCI: device %02x:%02x has unknown irq line %x\\n\",\n\t\t       dev->bus->number, dev->devfn, dev->irq);\n\n\treturn -1;\n}\n\n/*\n * why not the standard PCI swizzle?  does this prevent 4-port tulip\n * cards being used (ie, pci-pci bridge based cards)?\n */\nstatic struct hw_pci cats_pci __initdata = {\n\t.swizzle\t\t= cats_no_swizzle,\n\t.map_irq\t\t= cats_map_irq,\n\t.nr_controllers\t\t= 1,\n\t.ops\t\t\t= &dc21285_ops,\n\t.setup\t\t\t= dc21285_setup,\n\t.preinit\t\t= dc21285_preinit,\n\t.postinit\t\t= dc21285_postinit,\n};\n\nstatic int __init cats_pci_init(void)\n{\n\tif (machine_is_cats())\n\t\tpci_common_init(&cats_pci);\n\treturn 0;\n}\n\nsubsys_initcall(cats_pci_init);", "#ifndef _LINUX_PSI_H\n#define _LINUX_PSI_H\n\n#include <linux/jump_label.h>\n#include <linux/psi_types.h>\n#include <linux/sched.h>\n#include <linux/poll.h>\n\nstruct seq_file;\nstruct css_set;\n\n#ifdef CONFIG_PSI\n\nextern struct static_key_false psi_disabled;\nextern struct psi_group psi_system;\n\nvoid psi_init(void);\n\nvoid psi_task_change(struct task_struct *task, int clear, int set);\nvoid psi_task_switch(struct task_struct *prev, struct task_struct *next,\n\t\t     bool sleep);\n\nvoid psi_memstall_enter(unsigned long *flags);\nvoid psi_memstall_leave(unsigned long *flags);\n\nint psi_show(struct seq_file *s, struct psi_group *group, enum psi_res res);\n\n#ifdef CONFIG_CGROUPS\nint psi_cgroup_alloc(struct cgroup *cgrp);\nvoid psi_cgroup_free(struct cgroup *cgrp);\nvoid cgroup_move_task(struct task_struct *p, struct css_set *to);\n\nstruct psi_trigger *psi_trigger_create(struct psi_group *group,\n\t\t\tchar *buf, size_t nbytes, enum psi_res res);\nvoid psi_trigger_destroy(struct psi_trigger *t);\n\n__poll_t psi_trigger_poll(void **trigger_ptr, struct file *file,\n\t\t\tpoll_table *wait);\n#endif\n\n#else /* CONFIG_PSI */\n\nstatic inline void psi_init(void) {}\n\nstatic inline void psi_memstall_enter(unsigned long *flags) {}\nstatic inline void psi_memstall_leave(unsigned long *flags) {}\n\n#ifdef CONFIG_CGROUPS\nstatic inline int psi_cgroup_alloc(struct cgroup *cgrp)\n{\n\treturn 0;\n}\nstatic inline void psi_cgroup_free(struct cgroup *cgrp)\n{\n}\nstatic inline void cgroup_move_task(struct task_struct *p, struct css_set *to)\n{\n\trcu_assign_pointer(p->cgroups, to);\n}\n#endif\n\n#endif /* CONFIG_PSI */\n\n#endif /* _LINUX_PSI_H */", "// SPDX-License-Identifier: GPL-2.0-or-later\n/*\n * Copyright (C) 2020-2022 Loongson Technology Corporation Limited\n */\n#include <linux/efi.h>\n#include <linux/initrd.h>\n#include <linux/memblock.h>\n\n#include <asm/bootinfo.h>\n#include <asm/loongson.h>\n#include <asm/sections.h>\n\nvoid __init memblock_init(void)\n{\n\tu32 mem_type;\n\tu64 mem_start, mem_end, mem_size;\n\tefi_memory_desc_t *md;\n\n\t/* Parse memory information */\n\tfor_each_efi_memory_desc(md) {\n\t\tmem_type = md->type;\n\t\tmem_start = md->phys_addr;\n\t\tmem_size = md->num_pages << EFI_PAGE_SHIFT;\n\t\tmem_end = mem_start + mem_size;\n\n\t\tswitch (mem_type) {\n\t\tcase EFI_LOADER_CODE:\n\t\tcase EFI_LOADER_DATA:\n\t\tcase EFI_BOOT_SERVICES_CODE:\n\t\tcase EFI_BOOT_SERVICES_DATA:\n\t\tcase EFI_PERSISTENT_MEMORY:\n\t\tcase EFI_CONVENTIONAL_MEMORY:\n\t\t\tmemblock_add(mem_start, mem_size);\n\t\t\tif (max_low_pfn < (mem_end >> PAGE_SHIFT))\n\t\t\t\tmax_low_pfn = mem_end >> PAGE_SHIFT;\n\t\t\tbreak;\n\t\tcase EFI_PAL_CODE:\n\t\tcase EFI_UNUSABLE_MEMORY:\n\t\tcase EFI_ACPI_RECLAIM_MEMORY:\n\t\t\tmemblock_add(mem_start, mem_size);\n\t\t\tfallthrough;\n\t\tcase EFI_RESERVED_TYPE:\n\t\tcase EFI_RUNTIME_SERVICES_CODE:\n\t\tcase EFI_RUNTIME_SERVICES_DATA:\n\t\tcase EFI_MEMORY_MAPPED_IO:\n\t\tcase EFI_MEMORY_MAPPED_IO_PORT_SPACE:\n\t\t\tmemblock_reserve(mem_start, mem_size);\n\t\t\tbreak;\n\t\t}\n\t}\n\n\tmemblock_set_current_limit(PFN_PHYS(max_low_pfn));\n\n\t/* Reserve the first 2MB */\n\tmemblock_reserve(PHYS_OFFSET, 0x200000);\n\n\t/* Reserve the kernel text/data/bss */\n\tmemblock_reserve(__pa_symbol(&_text),\n\t\t\t __pa_symbol(&_end) - __pa_symbol(&_text));\n\n\tmemblock_set_node(0, PHYS_ADDR_MAX, &memblock.memory, 0);\n\tmemblock_set_node(0, PHYS_ADDR_MAX, &memblock.reserved, 0);\n}", "/*\n * Copyright (C) 2002 - 2007 <PERSON> (jdike@{addtoit,linux.intel}.com)\n * Licensed under the GPL\n */\n\n#include <linux/ctype.h>\n#include <linux/init.h>\n#include <linux/kernel.h>\n#include <linux/module.h>\n#include <linux/proc_fs.h>\n#include <linux/seq_file.h>\n#include <linux/types.h>\n#include <asm/uaccess.h>\n\n/*\n * If read and write race, the read will still atomically read a valid\n * value.\n */\nint uml_exitcode = 0;\n\nstatic int exitcode_proc_show(struct seq_file *m, void *v)\n{\n\tint val;\n\n\t/*\n\t * Save uml_exitcode in a local so that we don't need to guarantee\n\t * that sprintf accesses it atomically.\n\t */\n\tval = uml_exitcode;\n\tseq_printf(m, \"%d\\n\", val);\n\treturn 0;\n}\n\nstatic int exitcode_proc_open(struct inode *inode, struct file *file)\n{\n\treturn single_open(file, exitcode_proc_show, NULL);\n}\n\nstatic ssize_t exitcode_proc_write(struct file *file,\n\t\tconst char __user *buffer, size_t count, loff_t *pos)\n{\n\tchar *end, buf[sizeof(\"nnnnn\\0\")];\n\tsize_t size;\n\tint tmp;\n\n\tsize = min(count, sizeof(buf));\n\tif (copy_from_user(buf, buffer, size))\n\t\treturn -EFAULT;\n\n\ttmp = simple_strtol(buf, &end, 0);\n\tif ((*end != '\\0') && !isspace(*end))\n\t\treturn -EINVAL;\n\n\tuml_exitcode = tmp;\n\treturn count;\n}\n\nstatic const struct file_operations exitcode_proc_fops = {\n\t.owner\t\t= THIS_MODULE,\n\t.open\t\t= exitcode_proc_open,\n\t.read\t\t= seq_read,\n\t.llseek\t\t= seq_lseek,\n\t.release\t= single_release,\n\t.write\t\t= exitcode_proc_write,\n};\n\nstatic int make_proc_exitcode(void)\n{\n\tstruct proc_dir_entry *ent;\n\n\tent = proc_create(\"exitcode\", 0600, NULL, &exitcode_proc_fops);\n\tif (ent == NULL) {\n\t\tprintk(KERN_WARNING \"make_proc_exitcode : Failed to register \"\n\t\t       \"/proc/exitcode\\n\");\n\t\treturn 0;\n\t}\n\treturn 0;\n}\n\n__initcall(make_proc_exitcode);", "/*\n * Copyright (C) 2002 - 2007 <PERSON> (jdike@{addtoit,linux.intel}.com)\n * Licensed under the GPL\n */\n\n#include <linux/ctype.h>\n#include <linux/init.h>\n#include <linux/kernel.h>\n#include <linux/module.h>\n#include <linux/proc_fs.h>\n#include <linux/seq_file.h>\n#include <linux/types.h>\n#include <asm/uaccess.h>\n\n/*\n * If read and write race, the read will still atomically read a valid\n * value.\n */\nint uml_exitcode = 0;\n\nstatic int exitcode_proc_show(struct seq_file *m, void *v)\n{\n\tint val;\n\n\t/*\n\t * Save uml_exitcode in a local so that we don't need to guarantee\n\t * that sprintf accesses it atomically.\n\t */\n\tval = uml_exitcode;\n\tseq_printf(m, \"%d\\n\", val);\n\treturn 0;\n}\n\nstatic int exitcode_proc_open(struct inode *inode, struct file *file)\n{\n\treturn single_open(file, exitcode_proc_show, NULL);\n}\n\nstatic ssize_t exitcode_proc_write(struct file *file,\n\t\tconst char __user *buffer, size_t count, loff_t *pos)\n{\n\tchar *end, buf[sizeof(\"nnnnn\\0\")];\n\tsize_t size;\n\tint tmp;\n\n\tsize = min(count, sizeof(buf));\n\tif (copy_from_user(buf, buffer, size))\n\t\treturn -EFAULT;\n\n\ttmp = simple_strtol(buf, &end, 0);\n\tif ((*end != '\\0') && !isspace(*end))\n\t\treturn -EINVAL;\n\n\tuml_exitcode = tmp;\n\treturn count;\n}\n\nstatic const struct file_operations exitcode_proc_fops = {\n\t.owner\t\t= THIS_MODULE,\n\t.open\t\t= exitcode_proc_open,\n\t.read\t\t= seq_read,\n\t.llseek\t\t= seq_lseek,\n\t.release\t= single_release,\n\t.write\t\t= exitcode_proc_write,\n};\n\nstatic int make_proc_exitcode(void)\n{\n\tstruct proc_dir_entry *ent;\n\n\tent = proc_create(\"exitcode\", 0600, NULL, &exitcode_proc_fops);\n\tif (ent == NULL) {\n\t\tprintk(KERN_WARNING \"make_proc_exitcode : Failed to register \"\n\t\t       \"/proc/exitcode\\n\");\n\t\treturn 0;\n\t}\n\treturn 0;\n}\n\n__initcall(make_proc_exitcode);", "/******************************************************************************\n\n(c) 2008 NetApp.  All Rights Reserved.\n\nNetApp provides this source code under the GPL v2 License.\nThe GPL v2 license is available at\nhttp://opensource.org/licenses/gpl-license.php.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\nLIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\nA PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR\nCONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\nEXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\nPROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\nPROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\nLIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\nNEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\nSOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\n******************************************************************************/\n\n/*\n * Functions and macros used internally by RPC\n */\n\n#ifndef _NET_SUNRPC_SUNRPC_H\n#define _NET_SUNRPC_SUNRPC_H\n\n#include <linux/net.h>\n\n/*\n * Header for dynamically allocated rpc buffers.\n */\nstruct rpc_buffer {\n\tsize_t\tlen;\n\tchar\tdata[];\n};\n\nstatic inline int sock_is_loopback(struct sock *sk)\n{\n\tstruct dst_entry *dst;\n\tint loopback = 0;\n\trcu_read_lock();\n\tdst = rcu_dereference(sk->sk_dst_cache);\n\tif (dst && dst->dev &&\n\t    (dst->dev->features & NETIF_F_LOOPBACK))\n\t\tloopback = 1;\n\trcu_read_unlock();\n\treturn loopback;\n}\n\nint svc_send_common(struct socket *sock, struct xdr_buf *xdr,\n\t\t    struct page *headpage, unsigned long headoffset,\n\t\t    struct page *tailpage, unsigned long tailoffset);\n\nint rpc_clients_notifier_register(void);\nvoid rpc_clients_notifier_unregister(void);\nvoid auth_domain_cleanup(void);\n#endif /* _NET_SUNRPC_SUNRPC_H */", "/*\n * ARC700 mmap\n *\n * (started from arm version - for VIPT alias handling)\n *\n * Copyright (C) 2013 Synopsys, Inc. (www.synopsys.com)\n *\n * This program is free software; you can redistribute it and/or modify\n * it under the terms of the GNU General Public License version 2 as\n * published by the Free Software Foundation.\n */\n\n#include <linux/fs.h>\n#include <linux/mm.h>\n#include <linux/mman.h>\n#include <linux/sched.h>\n#include <asm/cacheflush.h>\n\n#define COLOUR_ALIGN(addr, pgoff)\t\t\t\\\n\t((((addr) + SHMLBA - 1) & ~(SHMLBA - 1)) +\t\\\n\t (((pgoff) << PAGE_SHIFT) & (SHMLBA - 1)))\n\n/*\n * Ensure that shared mappings are correctly aligned to\n * avoid aliasing issues with VIPT caches.\n * We need to ensure that\n * a specific page of an object is always mapped at a multiple of\n * SHMLBA bytes.\n */\nunsigned long\narch_get_unmapped_area(struct file *filp, unsigned long addr,\n\t\tunsigned long len, unsigned long pgoff, unsigned long flags)\n{\n\tstruct mm_struct *mm = current->mm;\n\tstruct vm_area_struct *vma;\n\tint do_align = 0;\n\tint aliasing = cache_is_vipt_aliasing();\n\tstruct vm_unmapped_area_info info;\n\n\t/*\n\t * We only need to do colour alignment if D cache aliases.\n\t */\n\tif (aliasing)\n\t\tdo_align = filp || (flags & MAP_SHARED);\n\n\t/*\n\t * We enforce the MAP_FIXED case.\n\t */\n\tif (flags & MAP_FIXED) {\n\t\tif (aliasing && flags & MAP_SHARED &&\n\t\t    (addr - (pgoff << PAGE_SHIFT)) & (SHMLBA - 1))\n\t\t\treturn -EINVAL;\n\t\treturn addr;\n\t}\n\n\tif (len > TASK_SIZE)\n\t\treturn -ENOMEM;\n\n\tif (addr) {\n\t\tif (do_align)\n\t\t\taddr = COLOUR_ALIGN(addr, pgoff);\n\t\telse\n\t\t\taddr = PAGE_ALIGN(addr);\n\n\t\tvma = find_vma(mm, addr);\n\t\tif (TASK_SIZE - len >= addr &&\n\t\t    (!vma || addr + len <= vma->vm_start))\n\t\t\treturn addr;\n\t}\n\n\tinfo.flags = 0;\n\tinfo.length = len;\n\tinfo.low_limit = mm->mmap_base;\n\tinfo.high_limit = TASK_SIZE;\n\tinfo.align_mask = do_align ? (PAGE_MASK & (SHMLBA - 1)) : 0;\n\tinfo.align_offset = pgoff << PAGE_SHIFT;\n\treturn vm_unmapped_area(&info);\n}", "/*\n * ARC700 mmap\n *\n * (started from arm version - for VIPT alias handling)\n *\n * Copyright (C) 2013 Synopsys, Inc. (www.synopsys.com)\n *\n * This program is free software; you can redistribute it and/or modify\n * it under the terms of the GNU General Public License version 2 as\n * published by the Free Software Foundation.\n */\n\n#include <linux/fs.h>\n#include <linux/mm.h>\n#include <linux/mman.h>\n#include <linux/sched.h>\n#include <asm/cacheflush.h>\n\n#define COLOUR_ALIGN(addr, pgoff)\t\t\t\\\n\t((((addr) + SHMLBA - 1) & ~(SHMLBA - 1)) +\t\\\n\t (((pgoff) << PAGE_SHIFT) & (SHMLBA - 1)))\n\n/*\n * Ensure that shared mappings are correctly aligned to\n * avoid aliasing issues with VIPT caches.\n * We need to ensure that\n * a specific page of an object is always mapped at a multiple of\n * SHMLBA bytes.\n */\nunsigned long\narch_get_unmapped_area(struct file *filp, unsigned long addr,\n\t\tunsigned long len, unsigned long pgoff, unsigned long flags)\n{\n\tstruct mm_struct *mm = current->mm;\n\tstruct vm_area_struct *vma;\n\tint do_align = 0;\n\tint aliasing = cache_is_vipt_aliasing();\n\tstruct vm_unmapped_area_info info;\n\n\t/*\n\t * We only need to do colour alignment if D cache aliases.\n\t */\n\tif (aliasing)\n\t\tdo_align = filp || (flags & MAP_SHARED);\n\n\t/*\n\t * We enforce the MAP_FIXED case.\n\t */\n\tif (flags & MAP_FIXED) {\n\t\tif (aliasing && flags & MAP_SHARED &&\n\t\t    (addr - (pgoff << PAGE_SHIFT)) & (SHMLBA - 1))\n\t\t\treturn -EINVAL;\n\t\treturn addr;\n\t}\n\n\tif (len > TASK_SIZE)\n\t\treturn -ENOMEM;\n\n\tif (addr) {\n\t\tif (do_align)\n\t\t\taddr = COLOUR_ALIGN(addr, pgoff);\n\t\telse\n\t\t\taddr = PAGE_ALIGN(addr);\n\n\t\tvma = find_vma(mm, addr);\n\t\tif (TASK_SIZE - len >= addr &&\n\t\t    (!vma || addr + len <= vma->vm_start))\n\t\t\treturn addr;\n\t}\n\n\tinfo.flags = 0;\n\tinfo.length = len;\n\tinfo.low_limit = mm->mmap_base;\n\tinfo.high_limit = TASK_SIZE;\n\tinfo.align_mask = do_align ? (PAGE_MASK & (SHMLBA - 1)) : 0;\n\tinfo.align_offset = pgoff << PAGE_SHIFT;\n\treturn vm_unmapped_area(&info);\n}", "/* Test NSS query path for plugins that only implement gethostbyname2\n   (#30843).\n   Copyright The GNU Toolchain Authors.\n   This file is part of the GNU C Library.\n\n   The GNU C Library is free software; you can redistribute it and/or\n   modify it under the terms of the GNU Lesser General Public\n   License as published by the Free Software Foundation; either\n   version 2.1 of the License, or (at your option) any later version.\n\n   The GNU C Library is distributed in the hope that it will be useful,\n   but WITHOUT ANY WARRANTY; without even the implied warranty of\n   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n   Lesser General Public License for more details.\n\n   You should have received a copy of the GNU Lesser General Public\n   License along with the GNU C Library; if not, see\n   <https://www.gnu.org/licenses/>.  */\n\n#include <nss.h>\n#include <netdb.h>\n#include <stdlib.h>\n#include <string.h>\n#include <mcheck.h>\n#include <support/check.h>\n#include <support/xstdio.h>\n#include \"nss/tst-nss-gai-hv2-canonname.h\"\n\n#define PREPARE do_prepare\n\nstatic void do_prepare (int a, char **av)\n{\n  FILE *hosts = xfopen (\"/etc/hosts\", \"w\");\n  for (unsigned i = 2; i < 255; i++)\n    {\n      fprintf (hosts, \"ff01::ff02:ff03:%u:2\\ttest.example.com\\n\", i);\n      fprintf (hosts, \"192.168.0.%u\\ttest.example.com\\n\", i);\n    }\n  xfclose (hosts);\n}\n\nstatic int\ndo_test (void)\n{\n  mtrace ();\n\n  __nss_configure_lookup (\"hosts\", \"test_gai_hv2_canonname\");\n\n  struct addrinfo hints = {};\n  struct addrinfo *result = NULL;\n\n  hints.ai_family = AF_INET6;\n  hints.ai_flags = AI_ALL | AI_V4MAPPED | AI_CANONNAME;\n\n  int ret = getaddrinfo (QUERYNAME, NULL, &hints, &result);\n\n  if (ret != 0)\n    FAIL_EXIT1 (\"getaddrinfo failed: %s\\n\", gai_strerror (ret));\n\n  TEST_COMPARE_STRING (result->ai_canonname, QUERYNAME);\n\n  freeaddrinfo(result);\n  return 0;\n}\n\n#include <support/test-driver.c>", "/* Test NSS query path for plugins that only implement gethostbyname2\n   (#30843).\n   Copyright The GNU Toolchain Authors.\n   This file is part of the GNU C Library.\n\n   The GNU C Library is free software; you can redistribute it and/or\n   modify it under the terms of the GNU Lesser General Public\n   License as published by the Free Software Foundation; either\n   version 2.1 of the License, or (at your option) any later version.\n\n   The GNU C Library is distributed in the hope that it will be useful,\n   but WITHOUT ANY WARRANTY; without even the implied warranty of\n   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n   Lesser General Public License for more details.\n\n   You should have received a copy of the GNU Lesser General Public\n   License along with the GNU C Library; if not, see\n   <https://www.gnu.org/licenses/>.  */\n\n#include <nss.h>\n#include <netdb.h>\n#include <stdlib.h>\n#include <string.h>\n#include <mcheck.h>\n#include <support/check.h>\n#include <support/xstdio.h>\n#include \"nss/tst-nss-gai-hv2-canonname.h\"\n\n#define PREPARE do_prepare\n\nstatic void do_prepare (int a, char **av)\n{\n  FILE *hosts = xfopen (\"/etc/hosts\", \"w\");\n  for (unsigned i = 2; i < 255; i++)\n    {\n      fprintf (hosts, \"ff01::ff02:ff03:%u:2\\ttest.example.com\\n\", i);\n      fprintf (hosts, \"192.168.0.%u\\ttest.example.com\\n\", i);\n    }\n  xfclose (hosts);\n}\n\nstatic int\ndo_test (void)\n{\n  mtrace ();\n\n  __nss_configure_lookup (\"hosts\", \"test_gai_hv2_canonname\");\n\n  struct addrinfo hints = {};\n  struct addrinfo *result = NULL;\n\n  hints.ai_family = AF_INET6;\n  hints.ai_flags = AI_ALL | AI_V4MAPPED | AI_CANONNAME;\n\n  int ret = getaddrinfo (QUERYNAME, NULL, &hints, &result);\n\n  if (ret != 0)\n    FAIL_EXIT1 (\"getaddrinfo failed: %s\\n\", gai_strerror (ret));\n\n  TEST_COMPARE_STRING (result->ai_canonname, QUERYNAME);\n\n  freeaddrinfo(result);\n  return 0;\n}\n\n#include <support/test-driver.c>", "#ifndef __LINUX_NET_AFUNIX_H\n#define __LINUX_NET_AFUNIX_H\n\n#include <linux/socket.h>\n#include <linux/un.h>\n#include <linux/mutex.h>\n#include <net/sock.h>\n\nvoid unix_inflight(struct file *fp);\nvoid unix_notinflight(struct file *fp);\nvoid unix_gc(void);\nvoid wait_for_unix_gc(void);\nstruct sock *unix_get_socket(struct file *filp);\nstruct sock *unix_peer_get(struct sock *);\n\n#define UNIX_HASH_SIZE\t256\n#define UNIX_HASH_BITS\t8\n\nextern unsigned int unix_tot_inflight;\nextern spinlock_t unix_table_lock;\nextern struct hlist_head unix_socket_table[2 * UNIX_HASH_SIZE];\n\nstruct unix_address {\n\tatomic_t\trefcnt;\n\tint\t\tlen;\n\tunsigned int\thash;\n\tstruct sockaddr_un name[0];\n};\n\nstruct unix_skb_parms {\n\tstruct pid\t\t*pid;\t\t/* Skb credentials\t*/\n\tkuid_t\t\t\tuid;\n\tkgid_t\t\t\tgid;\n\tstruct scm_fp_list\t*fp;\t\t/* Passed files\t\t*/\n#ifdef CONFIG_SECURITY_NETWORK\n\tu32\t\t\tsecid;\t\t/* Security ID\t\t*/\n#endif\n\tu32\t\t\tconsumed;\n};\n\n#define UNIXCB(skb) \t(*(struct unix_skb_parms *)&((skb)->cb))\n#define UNIXSID(skb)\t(&UNIXCB((skb)).secid)\n\n#define unix_state_lock(s)\tspin_lock(&unix_sk(s)->lock)\n#define unix_state_unlock(s)\tspin_unlock(&unix_sk(s)->lock)\n#define unix_state_lock_nested(s) \\\n\t\t\t\tspin_lock_nested(&unix_sk(s)->lock, \\\n\t\t\t\tSINGLE_DEPTH_NESTING)\n\n/* The AF_UNIX socket */\nstruct unix_sock {\n\t/* WARNING: sk has to be the first member */\n\tstruct sock\t\tsk;\n\tstruct unix_address     *addr;\n\tstruct path\t\tpath;\n\tstruct mutex\t\treadlock;\n\tstruct sock\t\t*peer;\n\tstruct list_head\tlink;\n\tatomic_long_t\t\tinflight;\n\tspinlock_t\t\tlock;\n\tunsigned char\t\trecursion_level;\n\tunsigned long\t\tgc_flags;\n#define UNIX_GC_CANDIDATE\t0\n#define UNIX_GC_MAYBE_CYCLE\t1\n\tstruct socket_wq\tpeer_wq;\n\twait_queue_t\t\tpeer_wake;\n};\n\nstatic inline struct unix_sock *unix_sk(struct sock *sk)\n{\n\treturn (struct unix_sock *)sk;\n}\n\n#define peer_wait peer_wq.wait\n\nlong unix_inq_len(struct sock *sk);\nlong unix_outq_len(struct sock *sk);\n\n#ifdef CONFIG_SYSCTL\nint unix_sysctl_register(struct net *net);\nvoid unix_sysctl_unregister(struct net *net);\n#else\nstatic inline int unix_sysctl_register(struct net *net) { return 0; }\nstatic inline void unix_sysctl_unregister(struct net *net) {}\n#endif\n#endif", "/******************************************************************************\n * hypervisor.h\n *\n * Linux-specific hypervisor handling.\n *\n * Copyright (c) 2002-2004, <PERSON> <PERSON> Fraser\n *\n * This program is free software; you can redistribute it and/or\n * modify it under the terms of the GNU General Public License version 2\n * as published by the Free Software Foundation; or, when distributed\n * separately from the Linux kernel or incorporated into other\n * software packages, subject to the following license:\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this source file (the \"Software\"), to deal in the Software without\n * restriction, including without limitation the rights to use, copy, modify,\n * merge, publish, distribute, sublicense, and/or sell copies of the Software,\n * and to permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n * IN THE SOFTWARE.\n */\n\n#ifndef _ASM_X86_XEN_HYPERVISOR_H\n#define _ASM_X86_XEN_HYPERVISOR_H\n\nextern struct shared_info *HYPERVISOR_shared_info;\nextern struct start_info *xen_start_info;\n\n#include <asm/processor.h>\n\nstatic inline uint32_t xen_cpuid_base(void)\n{\n\treturn hypervisor_cpuid_base(\"XenVMMXenVMM\", 2);\n}\n\n#ifdef CONFIG_XEN\nextern bool xen_hvm_need_lapic(void);\n\nstatic inline bool xen_x2apic_para_available(void)\n{\n\treturn xen_hvm_need_lapic();\n}\n#else\nstatic inline bool xen_x2apic_para_available(void)\n{\n\treturn (xen_cpuid_base() != 0);\n}\n#endif\n\n#endif /* _ASM_X86_XEN_HYPERVISOR_H */", "/* SPDX-License-Identifier: GPL-2.0 */\n#ifndef _NET_DST_OPS_H\n#define _NET_DST_OPS_H\n#include <linux/types.h>\n#include <linux/percpu_counter.h>\n#include <linux/cache.h>\n\nstruct dst_entry;\nstruct kmem_cachep;\nstruct net_device;\nstruct sk_buff;\nstruct sock;\nstruct net;\n\nstruct dst_ops {\n\tunsigned short\t\tfamily;\n\tunsigned int\t\tgc_thresh;\n\n\tvoid\t\t\t(*gc)(struct dst_ops *ops);\n\tstruct dst_entry *\t(*check)(struct dst_entry *, __u32 cookie);\n\tunsigned int\t\t(*default_advmss)(const struct dst_entry *);\n\tunsigned int\t\t(*mtu)(const struct dst_entry *);\n\tu32 *\t\t\t(*cow_metrics)(struct dst_entry *, unsigned long);\n\tvoid\t\t\t(*destroy)(struct dst_entry *);\n\tvoid\t\t\t(*ifdown)(struct dst_entry *,\n\t\t\t\t\t  struct net_device *dev, int how);\n\tstruct dst_entry *\t(*negative_advice)(struct dst_entry *);\n\tvoid\t\t\t(*link_failure)(struct sk_buff *);\n\tvoid\t\t\t(*update_pmtu)(struct dst_entry *dst, struct sock *sk,\n\t\t\t\t\t       struct sk_buff *skb, u32 mtu,\n\t\t\t\t\t       bool confirm_neigh);\n\tvoid\t\t\t(*redirect)(struct dst_entry *dst, struct sock *sk,\n\t\t\t\t\t    struct sk_buff *skb);\n\tint\t\t\t(*local_out)(struct net *net, struct sock *sk, struct sk_buff *skb);\n\tstruct neighbour *\t(*neigh_lookup)(const struct dst_entry *dst,\n\t\t\t\t\t\tstruct sk_buff *skb,\n\t\t\t\t\t\tconst void *daddr);\n\tvoid\t\t\t(*confirm_neigh)(const struct dst_entry *dst,\n\t\t\t\t\t\t const void *daddr);\n\n\tstruct kmem_cache\t*kmem_cachep;\n\n\tstruct percpu_counter\tpcpuc_entries ____cacheline_aligned_in_smp;\n};\n\nstatic inline int dst_entries_get_fast(struct dst_ops *dst)\n{\n\treturn percpu_counter_read_positive(&dst->pcpuc_entries);\n}\n\nstatic inline int dst_entries_get_slow(struct dst_ops *dst)\n{\n\treturn percpu_counter_sum_positive(&dst->pcpuc_entries);\n}\n\n#define DST_PERCPU_COUNTER_BATCH 32\nstatic inline void dst_entries_add(struct dst_ops *dst, int val)\n{\n\tpercpu_counter_add_batch(&dst->pcpuc_entries, val,\n\t\t\t\t DST_PERCPU_COUNTER_BATCH);\n}\n\nstatic inline int dst_entries_init(struct dst_ops *dst)\n{\n\treturn percpu_counter_init(&dst->pcpuc_entries, 0, GFP_KERNEL);\n}\n\nstatic inline void dst_entries_destroy(struct dst_ops *dst)\n{\n\tpercpu_counter_destroy(&dst->pcpuc_entries);\n}\n\n#endif", "// SPDX-License-Identifier: GPL-2.0\n/* Copyright (C) 1995, 1996 <NAME> <<EMAIL>> */\n\n#include <linux/sched.h>\n#include \"nfsd.h\"\n#include \"auth.h\"\n\nint nfsexp_flags(struct svc_rqst *rqstp, struct svc_export *exp)\n{\n\tstruct exp_flavor_info *f;\n\tstruct exp_flavor_info *end = exp->ex_flavors + exp->ex_nflavors;\n\n\tfor (f = exp->ex_flavors; f < end; f++) {\n\t\tif (f->pseudoflavor == rqstp->rq_cred.cr_flavor)\n\t\t\treturn f->flags;\n\t}\n\treturn exp->ex_flags;\n\n}\n\nint nfsd_setuser(struct svc_rqst *rqstp, struct svc_export *exp)\n{\n\tstruct group_info *rqgi;\n\tstruct group_info *gi;\n\tstruct cred *new;\n\tint i;\n\tint flags = nfsexp_flags(rqstp, exp);\n\n\tvalidate_process_creds();\n\n\t/* discard any old override before preparing the new set */\n\trevert_creds(get_cred(current_real_cred()));\n\tnew = prepare_creds();\n\tif (!new)\n\t\treturn -ENOMEM;\n\n\tnew->fsuid = rqstp->rq_cred.cr_uid;\n\tnew->fsgid = rqstp->rq_cred.cr_gid;\n\n\trqgi = rqstp->rq_cred.cr_group_info;\n\n\tif (flags & NFSEXP_ALLSQUASH) {\n\t\tnew->fsuid = exp->ex_anon_uid;\n\t\tnew->fsgid = exp->ex_anon_gid;\n\t\tgi = groups_alloc(0);\n\t\tif (!gi)\n\t\t\tgoto oom;\n\t} else if (flags & NFSEXP_ROOTSQUASH) {\n\t\tif (uid_eq(new->fsuid, GLOBAL_ROOT_UID))\n\t\t\tnew->fsuid = exp->ex_anon_uid;\n\t\tif (gid_eq(new->fsgid, GLOBAL_ROOT_GID))\n\t\t\tnew->fsgid = exp->ex_anon_gid;\n\n\t\tgi = groups_alloc(rqgi->ngroups);\n\t\tif (!gi)\n\t\t\tgoto oom;\n\n\t\tfor (i = 0; i < rqgi->ngroups; i++) {\n\t\t\tif (gid_eq(GLOBAL_ROOT_GID, rqgi->gid[i]))\n\t\t\t\tgi->gid[i] = exp->ex_anon_gid;\n\t\t\telse\n\t\t\t\tgi->gid[i] = rqgi->gid[i];\n\t\t}\n\n\t\t/* Each thread allocates its own gi, no race */\n\t\tgroups_sort(gi);\n\t} else {\n\t\tgi = get_group_info(rqgi);\n\t}\n\n\tif (uid_eq(new->fsuid, INVALID_UID))\n\t\tnew->fsuid = exp->ex_anon_uid;\n\tif (gid_eq(new->fsgid, INVALID_GID))\n\t\tnew->fsgid = exp->ex_anon_gid;\n\n\tset_groups(new, gi);\n\tput_group_info(gi);\n\n\tif (!uid_eq(new->fsuid, GLOBAL_ROOT_UID))\n\t\tnew->cap_effective = cap_drop_nfsd_set(new->cap_effective);\n\telse\n\t\tnew->cap_effective = cap_raise_nfsd_set(new->cap_effective,\n\t\t\t\t\t\t\tnew->cap_permitted);\n\tvalidate_process_creds();\n\tput_cred(override_creds(new));\n// ... 函数内容截断 ...\n\t\tif (!gi)\n\t\t\tgoto oom;\n\t} else if (flags & NFSEXP_ROOTSQUASH) {\n\t\tif (uid_eq(new->fsuid, GLOBAL_ROOT_UID))\n\t\t\tnew->fsuid = exp->ex_anon_uid;\n\t\tif (gid_eq(new->fsgid, GLOBAL_ROOT_GID))\n\t\t\tnew->fsgid = exp->ex_anon_gid;\n\n\t\tgi = groups_alloc(rqgi->ngroups);\n\t\tif (!gi)\n\t\t\tgoto oom;\n\n\t\tfor (i = 0; i < rqgi->ngroups; i++) {\n\t\t\tif (gid_eq(GLOBAL_ROOT_GID, GROUP_AT(rqgi, i)))\n\t\t\t\tGROUP_AT(gi, i) = exp->ex_anon_gid;\n\t\t\telse\n\t\t\t\tGROUP_AT(gi, i) = GROUP_AT(rqgi, i);\n\n\t\t\t/* Each thread allocates its own gi, no race */\n\t\t\tgroups_sort(gi);\n\t\t}\n\t} else {\n\t\tgi = get_group_info(rqgi);\n\t}\n\n\tif (uid_eq(new->fsuid, INVALID_UID))\n\t\tnew->fsuid = exp->ex_anon_uid;\n\tif (gid_eq(new->fsgid, INVALID_GID))\n\t\tnew->fsgid = exp->ex_anon_gid;\n\n\tset_groups(new, gi);\n\tput_group_info(gi);\n\n\tif (!uid_eq(new->fsuid, GLOBAL_ROOT_UID))\n\t\tnew->cap_effective = cap_drop_nfsd_set(new->cap_effective);\n\telse\n\t\tnew->cap_effective = cap_raise_nfsd_set(new->cap_effective,\n\t\t\t\t\t\t\tnew->cap_permitted);\n\tvalidate_process_creds();\n\tput_cred(override_creds(new));\n\tput_cred(new);\n\tvalidate_process_creds();\n\treturn 0;\n\noom:\n\tabort_creds(new);\n\treturn -ENOMEM;\n}", "/*\n * sysctl_net_llc.c: sysctl interface to LLC net subsystem.\n *\n * Arnal<PERSON> <<EMAIL>>\n */\n\n#include <linux/mm.h>\n#include <linux/init.h>\n#include <linux/sysctl.h>\n#include <net/llc.h>\n\n#ifndef CONFIG_SYSCTL\n#error This file should not be compiled without CONFIG_SYSCTL defined\n#endif\n\nstatic struct ctl_table llc2_timeout_table[] = {\n\t{\n\t\t.procname\t= \"ack\",\n\t\t.data\t\t= &sysctl_llc2_ack_timeout,\n\t\t.maxlen\t\t= sizeof(sysctl_llc2_ack_timeout),\n\t\t.mode\t\t= 0644,\n\t\t.proc_handler   = proc_dointvec_jiffies,\n\t},\n\t{\n\t\t.procname\t= \"busy\",\n\t\t.data\t\t= &sysctl_llc2_busy_timeout,\n\t\t.maxlen\t\t= sizeof(sysctl_llc2_busy_timeout),\n\t\t.mode\t\t= 0644,\n\t\t.proc_handler   = proc_dointvec_jiffies,\n\t},\n\t{\n\t\t.procname\t= \"p\",\n\t\t.data\t\t= &sysctl_llc2_p_timeout,\n\t\t.maxlen\t\t= sizeof(sysctl_llc2_p_timeout),\n\t\t.mode\t\t= 0644,\n\t\t.proc_handler   = proc_dointvec_jiffies,\n\t},\n\t{\n\t\t.procname\t= \"rej\",\n\t\t.data\t\t= &sysctl_llc2_rej_timeout,\n\t\t.maxlen\t\t= sizeof(sysctl_llc2_rej_timeout),\n\t\t.mode\t\t= 0644,\n\t\t.proc_handler   = proc_dointvec_jiffies,\n\t},\n\t{ },\n};\n\nstatic struct ctl_table llc_station_table[] = {\n\t{\n\t\t.procname\t= \"ack_timeout\",\n\t\t.data\t\t= &sysctl_llc_station_ack_timeout,\n\t\t.maxlen\t\t= sizeof(sysctl_llc_station_ack_timeout),\n\t\t.mode\t\t= 0644,\n\t\t.proc_handler   = proc_dointvec_jiffies,\n\t},\n\t{ },\n};\n\nstatic struct ctl_table llc2_dir_timeout_table[] = {\n\t{\n\t\t.procname\t= \"timeout\",\n\t\t.mode\t\t= 0555,\n\t\t.child\t\t= llc2_timeout_table,\n\t},\n\t{ },\n};\n\nstatic struct ctl_table llc_table[] = {\n\t{\n\t\t.procname\t= \"llc2\",\n\t\t.mode\t\t= 0555,\n\t\t.child\t\t= llc2_dir_timeout_table,\n\t},\n\t{\n\t\t.procname       = \"station\",\n\t\t.mode           = 0555,\n\t\t.child          = llc_station_table,\n\t},\n\t{ },\n};\n\nstatic struct ctl_path llc_path[] = {\n\t{ .procname = \"net\", },\n\t{ .procname = \"llc\", },\n\t{ }\n};\n\nstatic struct ctl_table_header *llc_table_header;\n\nint __init llc_sysctl_init(void)\n{\n\tllc_table_header = register_sysctl_paths(llc_path, llc_table);\n\n\treturn llc_table_header ? 0 : -ENOMEM;\n}\n\nvoid llc_sysctl_exit(void)\n{\n\tif (llc_table_header) {\n\t\tunregister_sysctl_table(llc_table_header);\n\t\tllc_table_header = NULL;\n\t}\n}", "/*\n * FUSE: Filesystem in Userspace\n * Copyright (C) 2016 Canonical Ltd. <<EMAIL>>\n *\n * This program can be distributed under the terms of the GNU GPL.\n * See the file COPYING.\n */\n\n#include \"fuse_i.h\"\n\n#include <linux/posix_acl.h>\n#include <linux/posix_acl_xattr.h>\n\nstruct posix_acl *fuse_get_acl(struct inode *inode, int type)\n{\n\tstruct fuse_conn *fc = get_fuse_conn(inode);\n\tint size;\n\tconst char *name;\n\tvoid *value = NULL;\n\tstruct posix_acl *acl;\n\n\tif (fuse_is_bad(inode))\n\t\treturn ERR_PTR(-EIO);\n\n\tif (!fc->posix_acl || fc->no_getxattr)\n\t\treturn NULL;\n\n\tif (type == ACL_TYPE_ACCESS)\n\t\tname = XATTR_NAME_POSIX_ACL_ACCESS;\n\telse if (type == ACL_TYPE_DEFAULT)\n\t\tname = XATTR_NAME_POSIX_ACL_DEFAULT;\n\telse\n\t\treturn ERR_PTR(-EOPNOTSUPP);\n\n\tvalue = kmalloc(PAGE_SIZE, GFP_KERNEL);\n\tif (!value)\n\t\treturn ERR_PTR(-ENOMEM);\n\tsize = fuse_getxattr(inode, name, value, PAGE_SIZE);\n\tif (size > 0)\n\t\tacl = posix_acl_from_xattr(fc->user_ns, value, size);\n\telse if ((size == 0) || (size == -ENODATA) ||\n\t\t (size == -EOPNOTSUPP && fc->no_getxattr))\n\t\tacl = NULL;\n\telse if (size == -ERANGE)\n\t\tacl = ERR_PTR(-E2BIG);\n\telse\n\t\tacl = ERR_PTR(size);\n\n\tkfree(value);\n\treturn acl;\n}\n\nint fuse_set_acl(struct inode *inode, struct posix_acl *acl, int type)\n{\n\tstruct fuse_conn *fc = get_fuse_conn(inode);\n\tconst char *name;\n\tint ret;\n\n\tif (fuse_is_bad(inode))\n\t\treturn -EIO;\n\n\tif (!fc->posix_acl || fc->no_setxattr)\n\t\treturn -EOPNOTSUPP;\n\n\tif (type == ACL_TYPE_ACCESS)\n\t\tname = XATTR_NAME_POSIX_ACL_ACCESS;\n\telse if (type == ACL_TYPE_DEFAULT)\n\t\tname = XATTR_NAME_POSIX_ACL_DEFAULT;\n\telse\n\t\treturn -EINVAL;\n\n\tif (acl) {\n\t\t/*\n\t\t * Fuse userspace is responsible for updating access\n\t\t * permissions in the inode, if needed. fuse_setxattr\n\t\t * invalidates the inode attributes, which will force\n\t\t * them to be refreshed the next time they are used,\n\t\t * and it also updates i_ctime.\n\t\t */\n\t\tsize_t size = posix_acl_xattr_size(acl->a_count);\n\t\tvoid *value;\n\n\t\tif (size > PAGE_SIZE)\n\t\t\treturn -E2BIG;\n\n\t\tvalue = kmalloc(size, GFP_KERNEL);\n\t\tif (!value)\n\t\t\treturn -ENOMEM;\n\n\t\tret = posix_acl_to_xattr(fc->user_ns, acl, value, size);\n\t\tif (ret < 0) {\n\t\t\tkfree(value);\n\t\t\treturn ret;\n\t\t}\n\n\t\tret = fuse_setxattr(inode, name, value, size, 0);\n\t\tkfree(value);\n\t} else {\n\t\tret = fuse_removexattr(inode, name);\n\t}\n\tforget_all_cached_acls(inode);\n\tfuse_invalidate_attr(inode);\n\n\treturn ret;\n}", "#ifndef _LINUX_USER_NAMESPACE_H\n#define _LINUX_USER_NAMESPACE_H\n\n#include <linux/kref.h>\n#include <linux/nsproxy.h>\n#include <linux/sched.h>\n#include <linux/err.h>\n\n#define UID_GID_MAP_MAX_EXTENTS 5\n\nstruct uid_gid_map {\t/* 64 bytes -- 1 cache line */\n\tu32 nr_extents;\n\tstruct uid_gid_extent {\n\t\tu32 first;\n\t\tu32 lower_first;\n\t\tu32 count;\n\t} extent[UID_GID_MAP_MAX_EXTENTS];\n};\n\nstruct user_namespace {\n\tstruct uid_gid_map\tuid_map;\n\tstruct uid_gid_map\tgid_map;\n\tstruct uid_gid_map\tprojid_map;\n\tatomic_t\t\tcount;\n\tstruct user_namespace\t*parent;\n\tint\t\t\tlevel;\n\tkuid_t\t\t\towner;\n\tkgid_t\t\t\tgroup;\n\tunsigned int\t\tproc_inum;\n};\n\nextern struct user_namespace init_user_ns;\n\n#ifdef CONFIG_USER_NS\n\nstatic inline struct user_namespace *get_user_ns(struct user_namespace *ns)\n{\n\tif (ns)\n\t\tatomic_inc(&ns->count);\n\treturn ns;\n}\n\nextern int create_user_ns(struct cred *new);\nextern int unshare_userns(unsigned long unshare_flags, struct cred **new_cred);\nextern void free_user_ns(struct user_namespace *ns);\n\nstatic inline void put_user_ns(struct user_namespace *ns)\n{\n\tif (ns && atomic_dec_and_test(&ns->count))\n\t\tfree_user_ns(ns);\n}\n\nstruct seq_operations;\nextern struct seq_operations proc_uid_seq_operations;\nextern struct seq_operations proc_gid_seq_operations;\nextern struct seq_operations proc_projid_seq_operations;\nextern ssize_t proc_uid_map_write(struct file *, const char __user *, size_t, loff_t *);\nextern ssize_t proc_gid_map_write(struct file *, const char __user *, size_t, loff_t *);\nextern ssize_t proc_projid_map_write(struct file *, const char __user *, size_t, loff_t *);\nextern bool userns_may_setgroups(const struct user_namespace *ns);\n#else\n\nstatic inline struct user_namespace *get_user_ns(struct user_namespace *ns)\n{\n\treturn &init_user_ns;\n}\n\nstatic inline int create_user_ns(struct cred *new)\n{\n\treturn -EINVAL;\n}\n\nstatic inline int unshare_userns(unsigned long unshare_flags,\n\t\t\t\t struct cred **new_cred)\n{\n\tif (unshare_flags & CLONE_NEWUSER)\n\t\treturn -EINVAL;\n\treturn 0;\n}\n\nstatic inline void put_user_ns(struct user_namespace *ns)\n{\n}\n\n#endif\n\n#endif /* _LINUX_USER_H */", "// SPDX-License-Identifier: GPL-2.0\n\n/*\n * Intel PMC Core platform init\n * Copyright (c) 2019, Google Inc.\n * Author - <NAME>\n *\n * This code instantiates platform devices for intel_pmc_core driver, only\n * on supported platforms that may not have the ACPI devices in the ACPI tables.\n * No new platforms should be added here, because we expect that new platforms\n * should all have the ACPI device, which is the preferred way of enumeration.\n */\n\n#include <linux/acpi.h>\n#include <linux/module.h>\n#include <linux/platform_device.h>\n\n#include <asm/cpu_device_id.h>\n#include <asm/intel-family.h>\n\nstatic void intel_pmc_core_release(struct device *dev)\n{\n\tkfree(dev);\n}\n\nstatic struct platform_device *pmc_core_device;\n\n/*\n * intel_pmc_core_platform_ids is the list of platforms where we want to\n * instantiate the platform_device if not already instantiated. This is\n * different than intel_pmc_core_ids in intel_pmc_core.c which is the\n * list of platforms that the driver supports for pmc_core device. The\n * other list may grow, but this list should not.\n */\nstatic const struct x86_cpu_id intel_pmc_core_platform_ids[] = {\n\tX86_MATCH_INTEL_FAM6_MODEL(SKYLAKE_L,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(SKYLAKE,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(KABYLAKE_L,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(KABYLAKE,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(CANNONLAKE_L,\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(ICELAKE_L,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(COMETLAKE,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(COMETLAKE_L,\t\t&pmc_core_device),\n\t{}\n};\nMODULE_DEVICE_TABLE(x86cpu, intel_pmc_core_platform_ids);\n\nstatic int __init pmc_core_platform_init(void)\n{\n\tint retval;\n\n\t/* Skip creating the platform device if ACPI already has a device */\n\tif (acpi_dev_present(\"INT33A1\", NULL, -1))\n\t\treturn -ENODEV;\n\n\tif (!x86_match_cpu(intel_pmc_core_platform_ids))\n\t\treturn -ENODEV;\n\n\tpmc_core_device = kzalloc(sizeof(*pmc_core_device), GFP_KERNEL);\n\tif (!pmc_core_device)\n\t\treturn -ENOMEM;\n\n\tpmc_core_device->name = \"intel_pmc_core\";\n\tpmc_core_device->dev.release = intel_pmc_core_release;\n\n\tretval = platform_device_register(pmc_core_device);\n\tif (retval)\n\t\tplatform_device_put(pmc_core_device);\n\n\treturn retval;\n}\n// ... 函数内容截断 ...\n * different than intel_pmc_core_ids in intel_pmc_core.c which is the\n * list of platforms that the driver supports for pmc_core device. The\n * other list may grow, but this list should not.\n */\nstatic const struct x86_cpu_id intel_pmc_core_platform_ids[] = {\n\tX86_MATCH_INTEL_FAM6_MODEL(SKYLAKE_L,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(SKYLAKE,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(KABYLAKE_L,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(KABYLAKE,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(CANNONLAKE_L,\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(ICELAKE_L,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(COMETLAKE,\t\t&pmc_core_device),\n\tX86_MATCH_INTEL_FAM6_MODEL(COMETLAKE_L,\t\t&pmc_core_device),\n\t{}\n};\nMODULE_DEVICE_TABLE(x86cpu, intel_pmc_core_platform_ids);\n\nstatic int __init pmc_core_platform_init(void)\n{\n\tint retval;\n\n\t/* Skip creating the platform device if ACPI already has a device */\n\tif (acpi_dev_present(\"INT33A1\", NULL, -1))\n\t\treturn -ENODEV;\n\n\tif (!x86_match_cpu(intel_pmc_core_platform_ids))\n\t\treturn -ENODEV;\n\n\tpmc_core_device = kzalloc(sizeof(*pmc_core_device), GFP_KERNEL);\n\tif (!pmc_core_device)\n\t\treturn -ENOMEM;\n\n\tpmc_core_device->name = \"intel_pmc_core\";\n\tpmc_core_device->dev.release = intel_pmc_core_release;\n\n\tretval = platform_device_register(pmc_core_device);\n\tif (retval)\n\t\tkfree(pmc_core_device);\n\n\treturn retval;\n}\n\nstatic void __exit pmc_core_platform_exit(void)\n{\n\tplatform_device_unregister(pmc_core_device);\n}\n\nmodule_init(pmc_core_platform_init);\nmodule_exit(pmc_core_platform_exit);\nMODULE_LICENSE(\"GPL v2\");", "/* SPDX-License-Identifier: GPL-2.0-or-later */\n/*\n * c 2001 PPC 64 Team, IBM Corp\n */\n#ifndef _ASM_POWERPC_PPC_PCI_H\n#define _ASM_POWERPC_PPC_PCI_H\n#ifdef __KERNEL__\n\n#ifdef CONFIG_PCI\n\n#include <linux/pci.h>\n#include <asm/pci-bridge.h>\n\nextern unsigned long isa_io_base;\n\nextern struct list_head hose_list;\n\nextern struct pci_dev *isa_bridge_pcidev;\t/* may be NULL if no ISA bus */\n\n/** Bus Unit ID macros; get low and hi 32-bits of the 64-bit BUID */\n#define BUID_HI(buid) upper_32_bits(buid)\n#define BUID_LO(buid) lower_32_bits(buid)\n\n/* PCI device_node operations */\nstruct device_node;\nstruct pci_dn;\n\nvoid *pci_traverse_device_nodes(struct device_node *start,\n\t\t\t\tvoid *(*fn)(struct device_node *, void *),\n\t\t\t\tvoid *data);\nextern void pci_devs_phb_init_dynamic(struct pci_controller *phb);\nextern void ppc_iommu_register_device(struct pci_controller *phb);\nextern void ppc_iommu_unregister_device(struct pci_controller *phb);\n\n\n/* From rtas_pci.h */\nextern void init_pci_config_tokens (void);\nextern unsigned long get_phb_buid (struct device_node *);\nextern int rtas_setup_phb(struct pci_controller *phb);\n\n#ifdef CONFIG_EEH\n\nvoid eeh_addr_cache_insert_dev(struct pci_dev *dev);\nvoid eeh_addr_cache_rmv_dev(struct pci_dev *dev);\nstruct eeh_dev *eeh_addr_cache_get_dev(unsigned long addr);\nvoid eeh_slot_error_detail(struct eeh_pe *pe, int severity);\nint eeh_pci_enable(struct eeh_pe *pe, int function);\nint eeh_pe_reset_full(struct eeh_pe *pe, bool include_passed);\nvoid eeh_save_bars(struct eeh_dev *edev);\nint rtas_write_config(struct pci_dn *, int where, int size, u32 val);\nint rtas_read_config(struct pci_dn *, int where, int size, u32 *val);\nvoid eeh_pe_state_mark(struct eeh_pe *pe, int state);\nvoid eeh_pe_mark_isolated(struct eeh_pe *pe);\nvoid eeh_pe_state_clear(struct eeh_pe *pe, int state, bool include_passed);\nvoid eeh_pe_state_mark_with_cfg(struct eeh_pe *pe, int state);\nvoid eeh_pe_dev_mode_mark(struct eeh_pe *pe, int mode);\n\nvoid eeh_sysfs_add_device(struct pci_dev *pdev);\nvoid eeh_sysfs_remove_device(struct pci_dev *pdev);\n\n#endif /* CONFIG_EEH */\n\n#ifdef CONFIG_FSL_ULI1575\nvoid __init uli_init(void);\n#endif /* CONFIG_FSL_ULI1575 */\n\n// ... 函数内容截断 ...\nstruct pci_dn;\n\nvoid *pci_traverse_device_nodes(struct device_node *start,\n\t\t\t\tvoid *(*fn)(struct device_node *, void *),\n\t\t\t\tvoid *data);\nextern void pci_devs_phb_init_dynamic(struct pci_controller *phb);\n\n/* From rtas_pci.h */\nextern void init_pci_config_tokens (void);\nextern unsigned long get_phb_buid (struct device_node *);\nextern int rtas_setup_phb(struct pci_controller *phb);\n\n#ifdef CONFIG_EEH\n\nvoid eeh_addr_cache_insert_dev(struct pci_dev *dev);\nvoid eeh_addr_cache_rmv_dev(struct pci_dev *dev);\nstruct eeh_dev *eeh_addr_cache_get_dev(unsigned long addr);\nvoid eeh_slot_error_detail(struct eeh_pe *pe, int severity);\nint eeh_pci_enable(struct eeh_pe *pe, int function);\nint eeh_pe_reset_full(struct eeh_pe *pe, bool include_passed);\nvoid eeh_save_bars(struct eeh_dev *edev);\nint rtas_write_config(struct pci_dn *, int where, int size, u32 val);\nint rtas_read_config(struct pci_dn *, int where, int size, u32 *val);\nvoid eeh_pe_state_mark(struct eeh_pe *pe, int state);\nvoid eeh_pe_mark_isolated(struct eeh_pe *pe);\nvoid eeh_pe_state_clear(struct eeh_pe *pe, int state, bool include_passed);\nvoid eeh_pe_state_mark_with_cfg(struct eeh_pe *pe, int state);\nvoid eeh_pe_dev_mode_mark(struct eeh_pe *pe, int mode);\n\nvoid eeh_sysfs_add_device(struct pci_dev *pdev);\nvoid eeh_sysfs_remove_device(struct pci_dev *pdev);\n\n#endif /* CONFIG_EEH */\n\n#ifdef CONFIG_FSL_ULI1575\nvoid __init uli_init(void);\n#endif /* CONFIG_FSL_ULI1575 */\n\n#define PCI_BUSNO(bdfn) ((bdfn >> 8) & 0xff)\n\n#else /* CONFIG_PCI */\nstatic inline void init_pci_config_tokens(void) { }\n#endif /* !CONFIG_PCI */\n\n#if !defined(CONFIG_PCI) || !defined(CONFIG_FSL_ULI1575)\nstatic inline void __init uli_init(void) {}\n#endif /* !defined(CONFIG_PCI) || !defined(CONFIG_FSL_ULI1575) */\n\n#endif /* __KERNEL__ */\n#endif /* _ASM_POWERPC_PPC_PCI_H */", "// SPDX-License-Identifier: GPL-2.0\n#include <linux/pagemap.h>\n#include <linux/slab.h>\n\n#include \"nouveau_drv.h\"\n#include \"nouveau_mem.h\"\n#include \"nouveau_ttm.h\"\n\nstruct nouveau_sgdma_be {\n\t/* this has to be the first field so populate/unpopulated in\n\t * nouve_bo.c works properly, otherwise have to move them here\n\t */\n\tstruct ttm_dma_tt ttm;\n\tstruct nouveau_mem *mem;\n};\n\nstatic void\nnouveau_sgdma_destroy(struct ttm_tt *ttm)\n{\n\tstruct nouveau_sgdma_be *nvbe = (struct nouveau_sgdma_be *)ttm;\n\n\tif (ttm) {\n\t\tttm_dma_tt_fini(&nvbe->ttm);\n\t\tkfree(nvbe);\n\t}\n}\n\nstatic int\nnv04_sgdma_bind(struct ttm_tt *ttm, struct ttm_mem_reg *reg)\n{\n// ... 函数内容截断 ...\n\t\t/*\n\t\t * A failing ttm_dma_tt_init() will call ttm_tt_destroy()\n\t\t * and thus our nouveau_sgdma_destroy() hook, so we don't need\n\t\t * to free nvbe here.\n\t\t */\n\t\treturn NULL;\n\treturn &nvbe->ttm.ttm;\n}", "/* SPDX-License-Identifier: GPL-2.0 */\n/*\n * see notice in l1oip.c\n */\n\n/* debugging */\n#define DEBUG_L1OIP_INIT\t0x00010000\n#define DEBUG_L1OIP_SOCKET\t0x00020000\n#define DEBUG_L1OIP_MGR\t\t0x00040000\n#define DEBUG_L1OIP_MSG\t\t0x00080000\n\n/* enable to disorder received bchannels by sequence **********... */\n/*\n  #define REORDER_DEBUG\n*/\n\n/* frames */\n#define L1OIP_MAX_LEN\t\t2048\t\t/* max packet size form l2 */\n#define L1OIP_MAX_PERFRAME\t1400\t\t/* max data size in one frame */\n\n\n/* timers */\n#define L1OIP_KEEPALIVE\t\t15\n#define L1OIP_TIMEOUT\t\t65\n\n\n/* socket */\n#define L1OIP_DEFAULTPORT\t931\n\n\n/* channel structure */\nstruct l1oip_chan {\n\tstruct dchannel\t\t*dch;\n\tstruct bchannel\t\t*bch;\n\tu32\t\t\ttx_counter;\t/* counts xmit bytes/packets */\n\tu32\t\t\trx_counter;\t/* counts recv bytes/packets */\n\tu32\t\t\tcodecstate;\t/* used by codec to save data */\n#ifdef REORDER_DEBUG\n\tint\t\t\tdisorder_flag;\n\tstruct sk_buff\t\t*disorder_skb;\n\tu32\t\t\tdisorder_cnt;\n#endif\n};\n\n\n/* card structure */\nstruct l1oip {\n\tstruct list_head        list;\n\n\t/* card */\n\tint\t\t\tregistered;\t/* if registered with mISDN */\n\tchar\t\t\tname[MISDN_MAX_IDLEN];\n\tint\t\t\tidx;\t\t/* card index */\n\tint\t\t\tpri;\t\t/* 1=pri, 0=bri */\n\tint\t\t\td_idx;\t\t/* current dchannel number */\n\tint\t\t\tb_num;\t\t/* number of bchannels */\n\tu32\t\t\tid;\t\t/* id of connection */\n\tint\t\t\tondemand;\t/* if transmis. is on demand */\n\tint\t\t\tbundle;\t\t/* bundle channels in one frm */\n\tint\t\t\tcodec;\t\t/* codec to use for transmis. */\n\tint\t\t\tlimit;\t\t/* limit number of bchannels */\n\tbool\t\t\tshutdown;\t/* if card is released */\n\n\t/* timer */\n\tstruct timer_list\tkeep_tl;\n\tstruct timer_list\ttimeout_tl;\n\tint\t\t\ttimeout_on;\n\tstruct work_struct\tworkq;\n\n\t/* socket */\n\tstruct socket\t\t*socket;\t/* if set, socket is created */\n\tstruct completion\tsocket_complete;/* completion of sock thread */\n\tstruct task_struct\t*socket_thread;\n\tspinlock_t\t\tsocket_lock;\t/* access sock outside thread */\n\tu32\t\t\tremoteip;\t/* if all set, ip is assigned */\n\tu16\t\t\tlocalport;\t/* must always be set */\n\tu16\t\t\tremoteport;\t/* must always be set */\n\tstruct sockaddr_in\tsin_local;\t/* local socket name */\n\tstruct sockaddr_in\tsin_remote;\t/* remote socket name */\n\tstruct msghdr\t\tsendmsg;\t/* ip message to send */\n\tstruct kvec\t\tsendiov;\t/* iov for message */\n\n// ... 函数内容截断 ...\n#endif\n};\n\n\n/* card structure */\nstruct l1oip {\n\tstruct list_head        list;\n\n\t/* card */\n\tint\t\t\tregistered;\t/* if registered with mISDN */\n\tchar\t\t\tname[MISDN_MAX_IDLEN];\n\tint\t\t\tidx;\t\t/* card index */\n\tint\t\t\tpri;\t\t/* 1=pri, 0=bri */\n\tint\t\t\td_idx;\t\t/* current dchannel number */\n\tint\t\t\tb_num;\t\t/* number of bchannels */\n\tu32\t\t\tid;\t\t/* id of connection */\n\tint\t\t\tondemand;\t/* if transmis. is on demand */\n\tint\t\t\tbundle;\t\t/* bundle channels in one frm */\n\tint\t\t\tcodec;\t\t/* codec to use for transmis. */\n\tint\t\t\tlimit;\t\t/* limit number of bchannels */\n\n\t/* timer */\n\tstruct timer_list\tkeep_tl;\n\tstruct timer_list\ttimeout_tl;\n\tint\t\t\ttimeout_on;\n\tstruct work_struct\tworkq;\n\n\t/* socket */\n\tstruct socket\t\t*socket;\t/* if set, socket is created */\n\tstruct completion\tsocket_complete;/* completion of sock thread */\n\tstruct task_struct\t*socket_thread;\n\tspinlock_t\t\tsocket_lock;\t/* access sock outside thread */\n\tu32\t\t\tremoteip;\t/* if all set, ip is assigned */\n\tu16\t\t\tlocalport;\t/* must always be set */\n\tu16\t\t\tremoteport;\t/* must always be set */\n\tstruct sockaddr_in\tsin_local;\t/* local socket name */\n\tstruct sockaddr_in\tsin_remote;\t/* remote socket name */\n\tstruct msghdr\t\tsendmsg;\t/* ip message to send */\n\tstruct kvec\t\tsendiov;\t/* iov for message */\n\n\t/* frame */\n\tstruct l1oip_chan\tchan[128];\t/* channel instances */\n};\n\nextern int l1oip_law_to_4bit(u8 *data, int len, u8 *result, u32 *state);\nextern int l1oip_4bit_to_law(u8 *data, int len, u8 *result);\nextern int l1oip_alaw_to_ulaw(u8 *data, int len, u8 *result);\nextern int l1oip_ulaw_to_alaw(u8 *data, int len, u8 *result);\nextern void l1oip_4bit_free(void);\nextern int l1oip_4bit_alloc(int ulaw);", "// SPDX-License-Identifier: GPL-2.0-or-later\n/*\n * Queued read/write locks\n *\n * (C) Copyright 2013-2014 Hewlett-Packard Development Company, L.P.\n *\n * Authors: <AUTHORS>\n */\n#include <linux/smp.h>\n#include <linux/bug.h>\n#include <linux/cpumask.h>\n#include <linux/percpu.h>\n#include <linux/hardirq.h>\n#include <linux/spinlock.h>\n\n/**\n * queued_read_lock_slowpath - acquire read lock of a queue rwlock\n * @lock: Pointer to queue rwlock structure\n */\nvoid queued_read_lock_slowpath(struct qrwlock *lock)\n{\n\t/*\n\t * Readers come here when they cannot get the lock without waiting\n\t */\n\tif (unlikely(in_interrupt())) {\n\t\t/*\n\t\t * Readers in interrupt context will get the lock immediately\n\t\t * if the writer is just waiting (not holding the lock yet),\n\t\t * so spin with ACQUIRE semantics until the lock is available\n\t\t * without waiting in the queue.\n\t\t */\n\t\tatomic_cond_read_acquire(&lock->cnts, !(VAL & _QW_LOCKED));\n\t\treturn;\n\t}\n\tatomic_sub(_QR_BIAS, &lock->cnts);\n\n\t/*\n\t * Put the reader into the wait queue\n\t */\n\tarch_spin_lock(&lock->wait_lock);\n\tatomic_add(_QR_BIAS, &lock->cnts);\n\n\t/*\n\t * The ACQUIRE semantics of the following spinning code ensure\n\t * that accesses can't leak upwards out of our subsequent critical\n\t * section in the case that the lock is currently held for write.\n\t */\n\tatomic_cond_read_acquire(&lock->cnts, !(VAL & _QW_LOCKED));\n\n\t/*\n\t * Signal the next one in queue to become queue head\n\t */\n\tarch_spin_unlock(&lock->wait_lock);\n}\nEXPORT_SYMBOL(queued_read_lock_slowpath);\n\n/**\n * queued_write_lock_slowpath - acquire write lock of a queue rwlock\n * @lock : Pointer to queue rwlock structure\n */\nvoid queued_write_lock_slowpath(struct qrwlock *lock)\n{\n\tint cnts;\n\n\t/* Put the writer into the wait queue */\n\tarch_spin_lock(&lock->wait_lock);\n\n\t/* Try to acquire the lock directly if no reader is present */\n\tif (!atomic_read(&lock->cnts) &&\n\t    (atomic_cmpxchg_acquire(&lock->cnts, 0, _QW_LOCKED) == 0))\n\t\tgoto unlock;\n\n\t/* Set the waiting flag to notify readers that a writer is pending */\n\tatomic_add(_QW_WAITING, &lock->cnts);\n\n\t/* When no more readers or writers, set the locked flag */\n\tdo {\n\t\tatomic_cond_read_acquire(&lock->cnts, VAL == _QW_WAITING);\n\t} while (atomic_cmpxchg_relaxed(&lock->cnts, _QW_WAITING,\n\t\t\t\t\t_QW_LOCKED) != _QW_WAITING);\nunlock:\n\tarch_spin_unlock(&lock->wait_lock);\n}\nEXPORT_SYMBOL(queued_write_lock_slowpath);", "#include <linux/interrupt.h>\n#include <linux/mutex.h>\n#include <linux/kernel.h>\n#include <linux/spi/spi.h>\n#include <linux/slab.h>\n#include <linux/bitops.h>\n#include <linux/export.h>\n\n#include <linux/iio/iio.h>\n#include <linux/iio/buffer.h>\n#include <linux/iio/triggered_buffer.h>\n#include <linux/iio/trigger_consumer.h>\n\n#include \"adis16400.h\"\n\nint adis16400_update_scan_mode(struct iio_dev *indio_dev,\n\tconst unsigned long *scan_mask)\n{\n\tstruct adis16400_state *st = iio_priv(indio_dev);\n\tstruct adis *adis = &st->adis;\n\tunsigned int burst_length;\n\tu8 *tx;\n\n\tif (st->variant->flags & ADIS16400_NO_BURST)\n\t\treturn adis_update_scan_mode(indio_dev, scan_mask);\n\n\tkfree(adis->xfer);\n\tkfree(adis->buffer);\n\n\t/* All but the timestamp channel */\n\tburst_length = (indio_dev->num_channels - 1) * sizeof(u16);\n\tif (st->variant->flags & ADIS16400_BURST_DIAG_STAT)\n\t\tburst_length += sizeof(u16);\n\n\tadis->xfer = kcalloc(2, sizeof(*adis->xfer), GFP_KERNEL);\n\tif (!adis->xfer)\n\t\treturn -ENOMEM;\n\n\tadis->buffer = kzalloc(burst_length + sizeof(u16), GFP_KERNEL);\n\tif (!adis->buffer) {\n\t\tkfree(adis->xfer);\n\t\tadis->xfer = NULL;\n\t\treturn -ENOMEM;\n\t}\n\n\ttx = adis->buffer + burst_length;\n\ttx[0] = ADIS_READ_REG(ADIS16400_GLOB_CMD);\n\ttx[1] = 0;\n\n\tadis->xfer[0].tx_buf = tx;\n\tadis->xfer[0].bits_per_word = 8;\n\tadis->xfer[0].len = 2;\n\tadis->xfer[1].rx_buf = adis->buffer;\n\tadis->xfer[1].bits_per_word = 8;\n\tadis->xfer[1].len = burst_length;\n\n\tspi_message_init(&adis->msg);\n\tspi_message_add_tail(&adis->xfer[0], &adis->msg);\n\tspi_message_add_tail(&adis->xfer[1], &adis->msg);\n\n\treturn 0;\n}\n\nirqreturn_t adis16400_trigger_handler(int irq, void *p)\n{\n\tstruct iio_poll_func *pf = p;\n\tstruct iio_dev *indio_dev = pf->indio_dev;\n\tstruct adis16400_state *st = iio_priv(indio_dev);\n\tstruct adis *adis = &st->adis;\n\tu32 old_speed_hz = st->adis.spi->max_speed_hz;\n\tvoid *buffer;\n\tint ret;\n\n\tif (!adis->buffer)\n\t\treturn -ENOMEM;\n\n\tif (!(st->variant->flags & ADIS16400_NO_BURST) &&\n\t\tst->adis.spi->max_speed_hz > ADIS16400_SPI_BURST) {\n\t\tst->adis.spi->max_speed_hz = ADIS16400_SPI_BURST;\n\t\tspi_setup(st->adis.spi);\n\t}\n\n\tret = spi_sync(adis->spi, &adis->msg);\n\tif (ret)\n\t\tdev_err(&adis->spi->dev, \"Failed to read data: %d\\n\", ret);\n\n\tif (!(st->variant->flags & ADIS16400_NO_BURST)) {\n\t\tst->adis.spi->max_speed_hz = old_speed_hz;\n\t\tspi_setup(st->adis.spi);\n\t}\n\n\tif (st->variant->flags & ADIS16400_BURST_DIAG_STAT)\n\t\tbuffer = adis->buffer + sizeof(u16);\n\telse\n\t\tbuffer = adis->buffer;\n\n\tiio_push_to_buffers_with_timestamp(indio_dev, buffer,\n\t\tpf->timestamp);\n\n\tiio_trigger_notify_done(indio_dev->trig);\n\n\treturn IRQ_HANDLED;\n}", "/*\n * Copyright 2004-2021 The OpenSSL Project Authors. All Rights Reserved.\n *\n * Licensed under the Apache License 2.0 (the \"License\").  You may not use\n * this file except in compliance with the License.  You can obtain a copy\n * in the file LICENSE in the source distribution or at\n * https://www.openssl.org/source/license.html\n */\n\n#include \"internal/cryptlib.h\"\n#include <openssl/x509.h>\n#include <openssl/x509v3.h>\n#include \"crypto/x509.h\"\n\n#include \"pcy_local.h\"\n\n/*\n * Set policy mapping entries in cache. Note: this modifies the passed\n * POLICY_MAPPINGS structure\n */\n\nint ossl_policy_cache_set_mapping(X509 *x, POLICY_MAPPINGS *maps)\n{\n    POLICY_MAPPING *map;\n    X509_POLICY_DATA *data;\n    X509_POLICY_CACHE *cache = x->policy_cache;\n    int i;\n    int ret = 0;\n    if (sk_POLICY_MAPPING_num(maps) == 0) {\n        ret = -1;\n        goto bad_mapping;\n    }\n    for (i = 0; i < sk_POLICY_MAPPING_num(maps); i++) {\n        map = sk_POLICY_MAPPING_value(maps, i);\n        /* Reject if map to or from anyPolicy */\n        if ((OBJ_obj2nid(map->subjectDomainPolicy) == NID_any_policy)\n            || (OBJ_obj2nid(map->issuerDomainPolicy) == NID_any_policy)) {\n            ret = -1;\n            goto bad_mapping;\n        }\n\n        /* Attempt to find matching policy data */\n        data = ossl_policy_cache_find_data(cache, map->issuerDomainPolicy);\n        /* If we don't have anyPolicy can't map */\n        if (data == NULL && !cache->anyPolicy)\n            continue;\n\n        /* Create a NODE from anyPolicy */\n        if (data == NULL) {\n            data = ossl_policy_data_new(NULL, map->issuerDomainPolicy,\n                                        cache->anyPolicy->flags\n                                        & POLICY_DATA_FLAG_CRITICAL);\n            if (data == NULL)\n                goto bad_mapping;\n            data->qualifier_set = cache->anyPolicy->qualifier_set;\n            /*\n             * map->issuerDomainPolicy = NULL;\n             */\n            data->flags |= POLICY_DATA_FLAG_MAPPED_ANY;\n            data->flags |= POLICY_DATA_FLAG_SHARED_QUALIFIERS;\n            if (!sk_X509_POLICY_DATA_push(cache->data, data)) {\n                ossl_policy_data_free(data);\n                goto bad_mapping;\n            }\n        } else\n            data->flags |= POLICY_DATA_FLAG_MAPPED;\n        if (!sk_ASN1_OBJECT_push(data->expected_policy_set,\n                                 map->subjectDomainPolicy))\n            goto bad_mapping;\n        map->subjectDomainPolicy = NULL;\n\n    }\n\n    ret = 1;\n// ... 函数内容截断 ...\n        map = sk_POLICY_MAPPING_value(maps, i);\n        /* Reject if map to or from anyPolicy */\n        if ((OBJ_obj2nid(map->subjectDomainPolicy) == NID_any_policy)\n            || (OBJ_obj2nid(map->issuerDomainPolicy) == NID_any_policy)) {\n            ret = -1;\n            goto bad_mapping;\n        }\n\n        /* Attempt to find matching policy data */\n        data = ossl_policy_cache_find_data(cache, map->issuerDomainPolicy);\n        /* If we don't have anyPolicy can't map */\n        if (data == NULL && !cache->anyPolicy)\n            continue;\n\n        /* Create a NODE from anyPolicy */\n        if (data == NULL) {\n            data = ossl_policy_data_new(NULL, map->issuerDomainPolicy,\n                                        cache->anyPolicy->flags\n                                        & POLICY_DATA_FLAG_CRITICAL);\n            if (data == NULL)\n                goto bad_mapping;\n            data->qualifier_set = cache->anyPolicy->qualifier_set;\n            /*\n             * map->issuerDomainPolicy = NULL;\n             */\n            data->flags |= POLICY_DATA_FLAG_MAPPED_ANY;\n            data->flags |= POLICY_DATA_FLAG_SHARED_QUALIFIERS;\n            if (!sk_X509_POLICY_DATA_push(cache->data, data)) {\n                ossl_policy_data_free(data);\n                goto bad_mapping;\n            }\n        } else\n            data->flags |= POLICY_DATA_FLAG_MAPPED;\n        if (!sk_ASN1_OBJECT_push(data->expected_policy_set,\n                                 map->subjectDomainPolicy))\n            goto bad_mapping;\n        map->subjectDomainPolicy = NULL;\n\n    }\n\n    ret = 1;\n bad_mapping:\n    if (ret == -1 && CRYPTO_THREAD_write_lock(x->lock)) {\n        x->ex_flags |= EXFLAG_INVALID_POLICY;\n        CRYPTO_THREAD_unlock(x->lock);\n    }\n    sk_POLICY_MAPPING_pop_free(maps, POLICY_MAPPING_free);\n    return ret;\n\n}", "#ifndef _LINUX_POISON_H\n#define _LINUX_POISON_H\n\n/********** include/linux/list.h **********/\n\n/*\n * Architectures might want to move the poison pointer offset\n * into some well-recognized area such as 0xdead000000000000,\n * that is also not mappable by user-space exploits:\n */\n#ifdef CONFIG_ILLEGAL_POINTER_VALUE\n# define POISON_POINTER_DELTA _AC(CONFIG_ILLEGAL_POINTER_VALUE, UL)\n#else\n# define POISON_POINTER_DELTA 0\n#endif\n\n/*\n * These are non-NULL pointers that will result in page faults\n * under normal circumstances, used to verify that nobody uses\n * non-initialized list entries.\n */\n#define LIST_POISON1  ((void *) 0x100 + POISON_POINTER_DELTA)\n#define LIST_POISON2  ((void *) 0x200 + POISON_POINTER_DELTA)\n\n/********** include/linux/timer.h **********/\n/*\n * Magic number \"tsta\" to indicate a static timer initializer\n * for the object debugging code.\n */\n#define TIMER_ENTRY_STATIC\t((void *) 0x74737461)\n\n/********** mm/debug-pagealloc.c **********/\n#define PAGE_POISON 0xaa\n\n/********** mm/slab.c **********/\n/*\n * Magic nums for obj red zoning.\n * Placed in the first word before and the first word after an obj.\n */\n#define\tRED_INACTIVE\t0x09F911029D74E35BULL\t/* when obj is inactive */\n#define\tRED_ACTIVE\t0xD84156C5635688C0ULL\t/* when obj is active */\n\n#define SLUB_RED_INACTIVE\t0xbb\n#define SLUB_RED_ACTIVE\t\t0xcc\n\n/* ...and for poisoning */\n#define\tPOISON_INUSE\t0x5a\t/* for use-uninitialised poisoning */\n#define POISON_FREE\t0x6b\t/* for use-after-free poisoning */\n#define\tPOISON_END\t0xa5\t/* end-byte of poisoning */\n\n/********** arch/$ARCH/mm/init.c **********/\n#define POISON_FREE_INITMEM\t0xcc\n\n/********** arch/ia64/hp/common/sba_iommu.c **********/\n/*\n * arch/ia64/hp/common/sba_iommu.c uses a 16-byte poison string with a\n * value of \"SBAIOMMU POISON\\0\" for spill-over poisoning.\n */\n\n/********** fs/jbd/journal.c **********/\n#define JBD_POISON_FREE\t\t0x5b\n#define JBD2_POISON_FREE\t0x5c\n\n/********** drivers/base/dmapool.c **********/\n#define\tPOOL_POISON_FREED\t0xa7\t/* !inuse */\n#define\tPOOL_POISON_ALLOCATED\t0xa9\t/* !initted */\n\n/********** drivers/atm/ **********/\n#define ATM_POISON_FREE\t\t0x12\n#define ATM_POISON\t\t0xdeadbeef\n\n/********** net/ **********/\n#define NEIGHBOR_DEAD\t\t0xdeadbeef\n#define NETFILTER_LINK_POISON\t0xdead57ac\n\n/********** kernel/mutexes **********/\n#define MUTEX_DEBUG_INIT\t0x11\n#define MUTEX_DEBUG_FREE\t0x22\n\n/********** lib/flex_array.c **********/\n#define FLEX_ARRAY_FREE\t0x6c\t/* for use-after-free poisoning */\n\n/********** security/ **********/\n#define KEY_DESTROY\t\t0xbd\n\n/********** sound/oss/ **********/\n#define OSS_POISON_FREE\t\t0xAB\n\n#endif", "/* SPDX-License-Identifier: GPL-2.0-or-later */\n/*\n * c 2001 PPC 64 Team, IBM Corp\n */\n#ifndef _ASM_POWERPC_PPC_PCI_H\n#define _ASM_POWERPC_PPC_PCI_H\n#ifdef __KERNEL__\n\n#ifdef CONFIG_PCI\n\n#include <linux/pci.h>\n#include <asm/pci-bridge.h>\n\nextern unsigned long isa_io_base;\n\nextern struct list_head hose_list;\n\nextern struct pci_dev *isa_bridge_pcidev;\t/* may be NULL if no ISA bus */\n\n/** Bus Unit ID macros; get low and hi 32-bits of the 64-bit BUID */\n#define BUID_HI(buid) upper_32_bits(buid)\n#define BUID_LO(buid) lower_32_bits(buid)\n\n/* PCI device_node operations */\nstruct device_node;\nstruct pci_dn;\n\nvoid *pci_traverse_device_nodes(struct device_node *start,\n\t\t\t\tvoid *(*fn)(struct device_node *, void *),\n\t\t\t\tvoid *data);\nextern void pci_devs_phb_init_dynamic(struct pci_controller *phb);\n\n#if defined(CONFIG_IOMMU_API) && (defined(CONFIG_PPC_PSERIES) || \\\n\t\t\t\t  defined(CONFIG_PPC_POWERNV))\nextern void ppc_iommu_register_device(struct pci_controller *phb);\nextern void ppc_iommu_unregister_device(struct pci_controller *phb);\n#else\nstatic inline void ppc_iommu_register_device(struct pci_controller *phb) { }\nstatic inline void ppc_iommu_unregister_device(struct pci_controller *phb) { }\n#endif\n\n\n/* From rtas_pci.h */\nextern void init_pci_config_tokens (void);\nextern unsigned long get_phb_buid (struct device_node *);\nextern int rtas_setup_phb(struct pci_controller *phb);\n\n#ifdef CONFIG_EEH\n\nvoid eeh_addr_cache_insert_dev(struct pci_dev *dev);\nvoid eeh_addr_cache_rmv_dev(struct pci_dev *dev);\nstruct eeh_dev *eeh_addr_cache_get_dev(unsigned long addr);\nvoid eeh_slot_error_detail(struct eeh_pe *pe, int severity);\nint eeh_pci_enable(struct eeh_pe *pe, int function);\nint eeh_pe_reset_full(struct eeh_pe *pe, bool include_passed);\nvoid eeh_save_bars(struct eeh_dev *edev);\nint rtas_write_config(struct pci_dn *, int where, int size, u32 val);\nint rtas_read_config(struct pci_dn *, int where, int size, u32 *val);\nvoid eeh_pe_state_mark(struct eeh_pe *pe, int state);\nvoid eeh_pe_mark_isolated(struct eeh_pe *pe);\nvoid eeh_pe_state_clear(struct eeh_pe *pe, int state, bool include_passed);\nvoid eeh_pe_state_mark_with_cfg(struct eeh_pe *pe, int state);\nvoid eeh_pe_dev_mode_mark(struct eeh_pe *pe, int mode);\n\nvoid eeh_sysfs_add_device(struct pci_dev *pdev);\nvoid eeh_sysfs_remove_device(struct pci_dev *pdev);\n// ... 函数内容截断 ...\nstruct pci_dn;\n\nvoid *pci_traverse_device_nodes(struct device_node *start,\n\t\t\t\tvoid *(*fn)(struct device_node *, void *),\n\t\t\t\tvoid *data);\nextern void pci_devs_phb_init_dynamic(struct pci_controller *phb);\n\n/* From rtas_pci.h */\nextern void init_pci_config_tokens (void);\nextern unsigned long get_phb_buid (struct device_node *);\nextern int rtas_setup_phb(struct pci_controller *phb);\n\n#ifdef CONFIG_EEH\n\nvoid eeh_addr_cache_insert_dev(struct pci_dev *dev);\nvoid eeh_addr_cache_rmv_dev(struct pci_dev *dev);\nstruct eeh_dev *eeh_addr_cache_get_dev(unsigned long addr);\nvoid eeh_slot_error_detail(struct eeh_pe *pe, int severity);\nint eeh_pci_enable(struct eeh_pe *pe, int function);\nint eeh_pe_reset_full(struct eeh_pe *pe, bool include_passed);\nvoid eeh_save_bars(struct eeh_dev *edev);\nint rtas_write_config(struct pci_dn *, int where, int size, u32 val);\nint rtas_read_config(struct pci_dn *, int where, int size, u32 *val);\nvoid eeh_pe_state_mark(struct eeh_pe *pe, int state);\nvoid eeh_pe_mark_isolated(struct eeh_pe *pe);\nvoid eeh_pe_state_clear(struct eeh_pe *pe, int state, bool include_passed);\nvoid eeh_pe_state_mark_with_cfg(struct eeh_pe *pe, int state);\nvoid eeh_pe_dev_mode_mark(struct eeh_pe *pe, int mode);\n\nvoid eeh_sysfs_add_device(struct pci_dev *pdev);\nvoid eeh_sysfs_remove_device(struct pci_dev *pdev);\n\n#endif /* CONFIG_EEH */\n\n#ifdef CONFIG_FSL_ULI1575\nvoid __init uli_init(void);\n#endif /* CONFIG_FSL_ULI1575 */\n\n#define PCI_BUSNO(bdfn) ((bdfn >> 8) & 0xff)\n\n#else /* CONFIG_PCI */\nstatic inline void init_pci_config_tokens(void) { }\n#endif /* !CONFIG_PCI */\n\n#if !defined(CONFIG_PCI) || !defined(CONFIG_FSL_ULI1575)\nstatic inline void __init uli_init(void) {}\n#endif /* !defined(CONFIG_PCI) || !defined(CONFIG_FSL_ULI1575) */\n\n#endif /* __KERNEL__ */\n#endif /* _ASM_POWERPC_PPC_PCI_H */", "// SPDX-License-Identifier: GPL-2.0\n#include <linux/ptrace.h>\n#include <linux/sched.h>\n#include <linux/sched/task_stack.h>\n#include <linux/export.h>\n#include <asm/syscall.h>\n\nstatic int collect_syscall(struct task_struct *target, struct syscall_info *info)\n{\n\tunsigned long args[6] = { };\n\tstruct pt_regs *regs;\n\n\tif (!try_get_task_stack(target)) {\n\t\t/* Task has no stack, so the task isn't in a syscall. */\n\t\tmemset(info, 0, sizeof(*info));\n\t\tinfo->data.nr = -1;\n\t\treturn 0;\n\t}\n\n\tregs = task_pt_regs(target);\n\tif (unlikely(!regs)) {\n\t\tput_task_stack(target);\n\t\treturn -EAGAIN;\n\t}\n\n\tinfo->sp = user_stack_pointer(regs);\n\tinfo->data.instruction_pointer = instruction_pointer(regs);\n\n\tinfo->data.nr = syscall_get_nr(target, regs);\n\tif (info->data.nr != -1L)\n\t\tsyscall_get_arguments(target, regs, args);\n\n\tinfo->data.args[0] = args[0];\n\tinfo->data.args[1] = args[1];\n\tinfo->data.args[2] = args[2];\n\tinfo->data.args[3] = args[3];\n\tinfo->data.args[4] = args[4];\n\tinfo->data.args[5] = args[5];\n\n\tput_task_stack(target);\n\treturn 0;\n}\n\n/**\n * task_current_syscall - Discover what a blocked task is doing.\n * @target:\t\tthread to examine\n * @info:\t\tstructure with the following fields:\n *\t\t\t .sp        - filled with user stack pointer\n *\t\t\t .data.nr   - filled with system call number or -1\n *\t\t\t .data.args - filled with @maxargs system call arguments\n *\t\t\t .data.instruction_pointer - filled with user PC\n *\n * If @target is blocked in a system call, returns zero with @info.data.nr\n * set to the the call's number and @info.data.args filled in with its\n * arguments. Registers not used for system call arguments may not be available\n * and it is not kosher to use &struct user_regset calls while the system\n * call is still in progress.  Note we may get this result if @target\n * has finished its system call but not yet returned to user mode, such\n * as when it's stopped for signal handling or syscall exit tracing.\n *\n * If @target is blocked in the kernel during a fault or exception,\n * returns zero with *@info.data.nr set to -1 and does not fill in\n * @info.data.args. If so, it's now safe to examine @target using\n * &struct user_regset get() calls as long as we're sure @target won't return\n * to user mode.\n *\n * Returns -%EAGAIN if @target does not remain blocked.\n */\nint task_current_syscall(struct task_struct *target, struct syscall_info *info)\n{\n\tlong state;\n\tunsigned long ncsw;\n\n\tif (target == current)\n\t\treturn collect_syscall(target, info);\n\n\tstate = target->state;\n\tif (unlikely(!state))\n\t\treturn -EAGAIN;\n\n\tncsw = wait_task_inactive(target, state);\n\tif (unlikely(!ncsw) ||\n\t    unlikely(collect_syscall(target, info)) ||\n\t    unlikely(wait_task_inactive(target, state) != ncsw))\n\t\treturn -EAGAIN;\n\n\treturn 0;\n}", "#ifndef _ASM_POWERPC_ASM_PROTOTYPES_H\n#define _ASM_POWERPC_ASM_PROTOTYPES_H\n/*\n * This file is for prototypes of C functions that are only called\n * from asm, and any associated variables.\n *\n * Copyright 2016, <NAME>, IBM Corporation.\n *\n * This program is free software; you can redistribute it and/or\n * modify it under the terms of the GNU General Public License\n * as published by the Free Software Foundation; either version 2\n * of the License, or (at your option) any later version.\n */\n\n#include <linux/threads.h>\n#include <asm/cacheflush.h>\n#include <asm/checksum.h>\n#include <linux/uaccess.h>\n#include <asm/epapr_hcalls.h>\n#include <asm/dcr.h>\n#include <asm/mmu_context.h>\n\n#include <uapi/asm/ucontext.h>\n\n/* SMP */\nextern struct thread_info *current_set[NR_CPUS];\nextern struct thread_info *secondary_ti;\nvoid start_secondary(void *unused);\n\n/* kexec */\n// ... 函数内容截断 ...\nextern s32 patch__flush_count_cache_return;\nextern s32 patch__flush_link_stack_return;\nextern s32 patch__memset_nocache, patch__memcpy_nocache;\n\nextern long flush_count_cache;\n\n#endif /* _ASM_POWERPC_ASM_PROTOTYPES_H */", "// SPDX-License-Identifier: GPL-2.0\n\n#include \"mtk_vcodec_fw_priv.h\"\n#include \"mtk_vcodec_util.h\"\n#include \"mtk_vcodec_drv.h\"\n\nstatic int mtk_vcodec_vpu_load_firmware(struct mtk_vcodec_fw *fw)\n{\n\treturn vpu_load_firmware(fw->pdev);\n}\n\nstatic unsigned int mtk_vcodec_vpu_get_vdec_capa(struct mtk_vcodec_fw *fw)\n{\n\treturn vpu_get_vdec_hw_capa(fw->pdev);\n}\n\nstatic unsigned int mtk_vcodec_vpu_get_venc_capa(struct mtk_vcodec_fw *fw)\n{\n\treturn vpu_get_venc_hw_capa(fw->pdev);\n}\n\nstatic void *mtk_vcodec_vpu_map_dm_addr(struct mtk_vcodec_fw *fw,\n\t\t\t\t\tu32 dtcm_dmem_addr)\n{\n\treturn vpu_mapping_dm_addr(fw->pdev, dtcm_dmem_addr);\n}\n\nstatic int mtk_vcodec_vpu_set_ipi_register(struct mtk_vcodec_fw *fw, int id,\n\t\t\t\t\t   mtk_vcodec_ipi_handler handler,\n\t\t\t\t\t   const char *name, void *priv)\n{\n\t/*\n\t * The handler we receive takes a void * as its first argument. We\n\t * cannot change this because it needs to be passed down to the rproc\n\t * subsystem when SCP is used. VPU takes a const argument, which is\n\t * more constrained, so the conversion below is safe.\n\t */\n\tipi_handler_t handler_const = (ipi_handler_t)handler;\n\n\treturn vpu_ipi_register(fw->pdev, id, handler_const, name, priv);\n}\n\nstatic int mtk_vcodec_vpu_ipi_send(struct mtk_vcodec_fw *fw, int id, void *buf,\n\t\t\t\t   unsigned int len, unsigned int wait)\n{\n\treturn vpu_ipi_send(fw->pdev, id, buf, len);\n}\n\nstatic void mtk_vcodec_vpu_release(struct mtk_vcodec_fw *fw)\n{\n\tput_device(&fw->pdev->dev);\n}\n\nstatic void mtk_vcodec_vpu_reset_handler(void *priv)\n{\n\tstruct mtk_vcodec_dev *dev = priv;\n\tstruct mtk_vcodec_ctx *ctx;\n\n\tmtk_v4l2_err(\"Watchdog timeout!!\");\n\n\tmutex_lock(&dev->dev_mutex);\n\tlist_for_each_entry(ctx, &dev->ctx_list, list) {\n\t\tctx->state = MTK_STATE_ABORT;\n\t\tmtk_v4l2_debug(0, \"[%d] Change to state MTK_STATE_ABORT\",\n\t\t\t       ctx->id);\n\t}\n\tmutex_unlock(&dev->dev_mutex);\n}\n\nstatic const struct mtk_vcodec_fw_ops mtk_vcodec_vpu_msg = {\n\t.load_firmware = mtk_vcodec_vpu_load_firmware,\n\t.get_vdec_capa = mtk_vcodec_vpu_get_vdec_capa,\n\t.get_venc_capa = mtk_vcodec_vpu_get_venc_capa,\n\t.map_dm_addr = mtk_vcodec_vpu_map_dm_addr,\n\t.ipi_register = mtk_vcodec_vpu_set_ipi_register,\n\t.ipi_send = mtk_vcodec_vpu_ipi_send,\n\t.release = mtk_vcodec_vpu_release,\n};\n\nstruct mtk_vcodec_fw *mtk_vcodec_fw_vpu_init(struct mtk_vcodec_dev *dev,\n\t\t\t\t\t     enum mtk_vcodec_fw_use fw_use)\n{\n\tstruct platform_device *fw_pdev;\n\tstruct mtk_vcodec_fw *fw;\n\tenum rst_id rst_id;\n\n\tswitch (fw_use) {\n\tcase ENCODER:\n\t\trst_id = VPU_RST_ENC;\n\t\tbreak;\n\tcase DECODER:\n\tdefault:\n\t\trst_id = VPU_RST_DEC;\n\t\tbreak;\n\t}\n\n\tfw_pdev = vpu_get_plat_device(dev->plat_dev);\n\tif (!fw_pdev) {\n\t\tmtk_v4l2_err(\"firmware device is not ready\");\n\t\treturn ERR_PTR(-EINVAL);\n\t}\n\tvpu_wdt_reg_handler(fw_pdev, mtk_vcodec_vpu_reset_handler, dev, rst_id);\n\n\tfw = devm_kzalloc(&dev->plat_dev->dev, sizeof(*fw), GFP_KERNEL);\n\tfw->type = VPU;\n\tfw->ops = &mtk_vcodec_vpu_msg;\n\tfw->pdev = fw_pdev;\n\n\treturn fw;\n}", "// SPDX-License-Identifier: GPL-2.0-only\n\n#define pr_fmt(fmt) \"efi: \" fmt\n\n#include <linux/module.h>\n#include <linux/init.h>\n#include <linux/efi.h>\n#include <linux/libfdt.h>\n#include <linux/of_fdt.h>\n\n#include <asm/unaligned.h>\n\nenum {\n\tSYSTAB,\n\tMMBASE,\n\tMMSIZE,\n\tDCSIZE,\n\tDCVERS,\n\n\tPARAMCOUNT\n};\n\nstatic __initconst const char name[][22] = {\n\t[SYSTAB] = \"System Table         \",\n\t[MMBASE] = \"MemMap Address       \",\n\t[MMSIZE] = \"MemMap Size          \",\n\t[DCSIZE] = \"MemMap Desc. Size    \",\n\t[DCVERS] = \"MemMap Desc. Version \",\n};\n\nstatic __initconst const struct {\n\tconst char\tpath[17];\n\tconst char\tparams[PARAMCOUNT][26];\n} dt_params[] = {\n\t{\n#ifdef CONFIG_XEN    //  <-------17------>\n\t\t.path = \"/hypervisor/uefi\",\n\t\t.params = {\n\t\t\t[SYSTAB] = \"xen,uefi-system-table\",\n\t\t\t[MMBASE] = \"xen,uefi-mmap-start\",\n\t\t\t[MMSIZE] = \"xen,uefi-mmap-size\",\n\t\t\t[DCSIZE] = \"xen,uefi-mmap-desc-size\",\n\t\t\t[DCVERS] = \"xen,uefi-mmap-desc-ver\",\n\t\t}\n\t}, {\n#endif\n\t\t.path = \"/chosen\",\n\t\t.params = {\t//  <-----------26----------->\n\t\t\t[SYSTAB] = \"linux,uefi-system-table\",\n\t\t\t[MMBASE] = \"linux,uefi-mmap-start\",\n\t\t\t[MMSIZE] = \"linux,uefi-mmap-size\",\n\t\t\t[DCSIZE] = \"linux,uefi-mmap-desc-size\",\n\t\t\t[DCVERS] = \"linux,uefi-mmap-desc-ver\",\n\t\t}\n\t}\n};\n\nstatic int __init efi_get_fdt_prop(const void *fdt, int node, const char *pname,\n\t\t\t\t   const char *rname, void *var, int size)\n{\n\tconst void *prop;\n\tint len;\n\tu64 val;\n\n\tprop = fdt_getprop(fdt, node, pname, &len);\n\tif (!prop)\n\t\treturn 1;\n\n\tval = (len == 4) ? (u64)be32_to_cpup(prop) : get_unaligned_be64(prop);\n\n\tif (size == 8)\n\t\t*(u64 *)var = val;\n\telse\n\t\t*(u32 *)var = (val < U32_MAX) ? val : U32_MAX; // saturate\n\n\tif (efi_enabled(EFI_DBG))\n\t\tpr_info(\"  %s: 0x%0*llx\\n\", rname, size * 2, val);\n\n\treturn 0;\n}\n\nu64 __init efi_get_fdt_params(struct efi_memory_map_data *mm)\n{\n\tconst void *fdt = initial_boot_params;\n\tunsigned long systab;\n\tint i, j, node;\n\tstruct {\n\t\tvoid\t*var;\n\t\tint\tsize;\n\t} target[] = {\n\t\t[SYSTAB] = { &systab,\t\tsizeof(systab) },\n\t\t[MMBASE] = { &mm->phys_map,\tsizeof(mm->phys_map) },\n\t\t[MMSIZE] = { &mm->size,\t\tsizeof(mm->size) },\n\t\t[DCSIZE] = { &mm->desc_size,\tsizeof(mm->desc_size) },\n\t\t[DCVERS] = { &mm->desc_version,\tsizeof(mm->desc_version) },\n\t};\n\n\tBUILD_BUG_ON(ARRAY_SIZE(target) != ARRAY_SIZE(name));\n\tBUILD_BUG_ON(ARRAY_SIZE(target) != ARRAY_SIZE(dt_params[0].params));\n\n\tfor (i = 0; i < ARRAY_SIZE(dt_params); i++) {\n\t\tnode = fdt_path_offset(fdt, dt_params[i].path);\n\t\tif (node < 0)\n\t\t\tcontinue;\n\n\t\tif (efi_enabled(EFI_DBG))\n\t\t\tpr_info(\"Getting UEFI parameters from %s in DT:\\n\",\n\t\t\t\tdt_params[i].path);\n\n\t\tfor (j = 0; j < ARRAY_SIZE(target); j++) {\n\t\t\tconst char *pname = dt_params[i].params[j];\n\n\t\t\tif (!efi_get_fdt_prop(fdt, node, pname, name[j],\n\t\t\t\t\t      target[j].var, target[j].size))\n\t\t\t\tcontinue;\n\t\t\tif (!j)\n\t\t\t\tgoto notfound;\n\t\t\tpr_err(\"Can't find property '%s' in DT!\\n\", pname);\n\t\t\treturn 0;\n\t\t}\n\t\treturn systab;\n\t}\nnotfound:\n\tpr_info(\"UEFI not found.\\n\");\n\treturn 0;\n}", "/*\n * Copyright (C) 2003-2008 <PERSON><PERSON><PERSON>\n *\n * This is free software; you can redistribute it and/or modify\n * it under the terms of the GNU General Public License as published by\n * the Free Software Foundation; either version 2 of the License, or\n * (at your option) any later version.\n *\n * This is distributed in the hope that it will be useful,\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\n * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n * GNU General Public License for more details.\n *\n * You should have received a copy of the GNU General Public License\n * along with this program; if not, write to the Free Software\n * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307,\n * USA.\n */\n\n#ifndef __USBIP_STUB_H\n#define __USBIP_STUB_H\n\n#include <linux/list.h>\n#include <linux/slab.h>\n#include <linux/spinlock.h>\n#include <linux/types.h>\n#include <linux/usb.h>\n#include <linux/wait.h>\n\n#define STUB_BUSID_OTHER 0\n#define STUB_BUSID_REMOV 1\n#define STUB_BUSID_ADDED 2\n#define STUB_BUSID_ALLOC 3\n\nstruct stub_device {\n\tstruct usb_device *udev;\n\n\tstruct usbip_device ud;\n\t__u32 devid;\n\n\t/*\n\t * stub_priv preserves private data of each urb.\n\t * It is allocated as stub_priv_cache and assigned to urb->context.\n\t *\n\t * stub_priv is always linked to any one of 3 lists;\n\t *\tpriv_init: linked to this until the comletion of a urb.\n\t *\tpriv_tx  : linked to this after the completion of a urb.\n\t *\tpriv_free: linked to this after the sending of the result.\n\t *\n\t * Any of these list operations should be locked by priv_lock.\n\t */\n\tspinlock_t priv_lock;\n\tstruct list_head priv_init;\n\tstruct list_head priv_tx;\n\tstruct list_head priv_free;\n\n\t/* see comments for unlinking in stub_rx.c */\n\tstruct list_head unlink_tx;\n\tstruct list_head unlink_free;\n\n\twait_queue_head_t tx_waitq;\n};\n\n/* private data into urb->priv */\nstruct stub_priv {\n\tunsigned long seqnum;\n\tstruct list_head list;\n\tstruct stub_device *sdev;\n\tstruct urb *urb;\n\n\tint unlinking;\n};\n\nstruct stub_unlink {\n\tunsigned long seqnum;\n\tstruct list_head list;\n\t__u32 status;\n};\n\n/* same as SYSFS_BUS_ID_SIZE */\n#define BUSID_SIZE 32\n\nstruct bus_id_priv {\n\tchar name[BUSID_SIZE];\n\tchar status;\n\tint interf_count;\n\tstruct stub_device *sdev;\n\tstruct usb_device *udev;\n\tchar shutdown_busid;\n\tspinlock_t busid_lock;\n};\n\n/* stub_priv is allocated from stub_priv_cache */\nextern struct kmem_cache *stub_priv_cache;\n\n/* stub_dev.c */\nextern struct usb_device_driver stub_driver;\n\n/* stub_main.c */\nstruct bus_id_priv *get_busid_priv(const char *busid);\nvoid put_busid_priv(struct bus_id_priv *bid);\nint del_match_busid(char *busid);\nvoid stub_device_cleanup_urbs(struct stub_device *sdev);\n\n/* stub_rx.c */\nint stub_rx_loop(void *data);\n\n/* stub_tx.c */\nvoid stub_enqueue_ret_unlink(struct stub_device *sdev, __u32 seqnum,\n\t\t\t     __u32 status);\nvoid stub_complete(struct urb *urb);\nint stub_tx_loop(void *data);\n\n#endif /* __USBIP_STUB_H */", ":\n// SPDX-License-Identifier: GPL-2.0-only\n/*\n * Copyright (C) 2020 BAIKAL ELECTRONICS, JSC\n *\n * Authors: <AUTHORS>\n *\n * Baikal-T1 Physically Mapped Internal ROM driver\n */\n#include <linux/bits.h>\n#include <linux/device.h>\n#include <linux/kernel.h>\n#include <linux/mtd/map.h>\n#include <linux/mtd/xip.h>\n#include <linux/mux/consumer.h>\n#include <linux/of.h>\n#include <linux/of_device.h>\n#include <linux/platform_device.h>\n#include <linux/string.h>\n#include <linux/types.h>\n\n#include \"physmap-bt1-rom.h\"\n\n/*\n * Baikal-T1 SoC ROMs are only accessible by the dword-aligned instructions.\n * We have to take this into account when implementing the data read-methods.\n * Note there is no need in bothering with endianness, since both Baikal-T1\n * CPU and MMIO are LE.\n */\nstatic map_word __xipram bt1_rom_map_read(struct map_info *map,\n\t\t\t\t\t  unsigned long ofs)\n{\n\tvoid __iomem *src = map->virt + ofs;\n\tunsigned long shift;\n\tmap_word ret;\n\tu32 data;\n\n\t/* Read data within offset dword. */\n\tshift = (unsigned long)src & 0x3;\n\tdata = readl_relaxed(src - shift);\n\tif (!shift) {\n\t\tret.x[0] = data;\n\t\treturn ret;\n\t}\n\tret.x[0] = data >> (shift * BITS_PER_BYTE);\n\n\t/* Read data from the next dword. */\n\tshift = 4 - shift;\n\tif (ofs + shift >= map->size)\n\t\treturn ret;\n\n\tdata = readl_relaxed(src + shift);\n\tret.x[0] |= data << (shift * BITS_PER_BYTE);\n\n\treturn ret;\n}\n\nstatic void __xipram bt1_rom_map_copy_from(struct map_info *map,\n\t\t\t\t\t   void *to, unsigned long from,\n\t\t\t\t\t   ssize_t len)\n{\n\tvoid __iomem *src = map->virt + from;\n\tssize_t shift, chunk;\n\tu32 data;\n\n\tif (len <= 0 || from >= map->size)\n\t\treturn;\n\n\t/* Make sure we don't go over the map limit. */\n\tlen = min_t(ssize_t, map->size - from, len);\n\n\t/*\n\t * Since requested data size can be pretty big we have to implement\n\t * the copy procedure as optimal as possible. That's why it's split\n\t * up into the next three stages: unaligned head, aligned body,\n\t * unaligned tail.\n\t */\n\tshift = (ssize_t)src & 0x3;\n\tif (shift) {\n\t\tchunk = min_t(ssize_t, 4 - shift, len);\n\t\tdata = readl_relaxed(src - shift);\n\t\tmemcpy(to, (char *)&data + shift, chunk);\n\t\tsrc += chunk;\n\t\tto += chunk;\n\t\tlen -= chunk;\n\t}\n\n\twhile (len >= 4) {\n\t\tdata = readl_relaxed(src);\n\t\tmemcpy(to, &data, 4);\n\t\tsrc += 4;\n\t\tto += 4;\n\t\tlen -= 4;\n\t}\n\n\tif (len) {\n\t\tdata = readl_relaxed(src);\n\t\tmemcpy(to, &data, len);\n\t}\n}\n\nint of_flash_probe_bt1_rom(struct platform_device *pdev,\n\t\t\t   struct device_node *np,\n\t\t\t   struct map_info *map)\n{\n\tstruct device *dev = &pdev->dev;\n\n\t/* It's supposed to be read-only MTD. */\n\tif (!of_device_is_compatible(np, \"mtd-rom\")) {\n\t\tdev_info(dev, \"No mtd-rom compatible string\\n\");\n\t\treturn 0;\n\t}\n\n\t/* Multiplatform guard. */\n\tif (!of_device_is_compatible(np, \"baikal,bt1-int-rom\"))\n\t\treturn 0;\n\n\t/* Sanity check the device parameters retrieved from DTB. */\n\tif (map->bankwidth != 4)\n\t\tdev_warn(dev, \"Bank width is supposed to be 32 bits wide\\n\");\n\n\tmap->read = bt1_rom_map_read;\n\tmap->copy_from = bt1_rom_map_copy_from;\n\n\treturn 0;\n}", "// SPDX-License-Identifier: GPL-2.0\n/*\n * Copyright (c) 2018-2020, The Linux Foundation. All rights reserved.\n */\n\n#include <linux/mhi.h>\n#include <linux/mod_devicetable.h>\n#include <linux/module.h>\n#include <linux/skbuff.h>\n#include <net/sock.h>\n\n#include \"qrtr.h\"\n\nstruct qrtr_mhi_dev {\n\tstruct qrtr_endpoint ep;\n\tstruct mhi_device *mhi_dev;\n\tstruct device *dev;\n};\n\n/* From MHI to QRTR */\nstatic void qcom_mhi_qrtr_dl_callback(struct mhi_device *mhi_dev,\n\t\t\t\t      struct mhi_result *mhi_res)\n{\n\tstruct qrtr_mhi_dev *qdev = dev_get_drvdata(&mhi_dev->dev);\n\tint rc;\n\n\tif (!qdev || mhi_res->transaction_status)\n\t\treturn;\n\n\trc = qrtr_endpoint_post(&qdev->ep, mhi_res->buf_addr,\n\t\t\t\tmhi_res->bytes_xferd);\n\tif (rc == -EINVAL)\n\t\tdev_err(qdev->dev, \"invalid ipcrouter packet\\n\");\n}\n\n/* From QRTR to MHI */\nstatic void qcom_mhi_qrtr_ul_callback(struct mhi_device *mhi_dev,\n\t\t\t\t      struct mhi_result *mhi_res)\n{\n\tstruct sk_buff *skb = mhi_res->buf_addr;\n\n\tif (skb->sk)\n\t\tsock_put(skb->sk);\n\tconsume_skb(skb);\n}\n\n/* Send data over MHI */\nstatic int qcom_mhi_qrtr_send(struct qrtr_endpoint *ep, struct sk_buff *skb)\n{\n\tstruct qrtr_mhi_dev *qdev = container_of(ep, struct qrtr_mhi_dev, ep);\n\tint rc;\n\n\tif (skb->sk)\n\t\tsock_hold(skb->sk);\n\n\trc = skb_linearize(skb);\n\tif (rc)\n\t\tgoto free_skb;\n\n\trc = mhi_queue_skb(qdev->mhi_dev, DMA_TO_DEVICE, skb, skb->len,\n\t\t\t   MHI_EOT);\n\tif (rc)\n\t\tgoto free_skb;\n\n\treturn rc;\n\nfree_skb:\n\tif (skb->sk)\n\t\tsock_put(skb->sk);\n\tkfree_skb(skb);\n\n\treturn rc;\n}\n\nstatic int qcom_mhi_qrtr_probe(struct mhi_device *mhi_dev,\n\t\t\t       const struct mhi_device_id *id)\n{\n\tstruct qrtr_mhi_dev *qdev;\n\tint rc;\n\n\tqdev = devm_kzalloc(&mhi_dev->dev, sizeof(*qdev), GFP_KERNEL);\n\tif (!qdev)\n\t\treturn -ENOMEM;\n\n\tqdev->mhi_dev = mhi_dev;\n\tqdev->dev = &mhi_dev->dev;\n\tqdev->ep.xmit = qcom_mhi_qrtr_send;\n\n\tdev_set_drvdata(&mhi_dev->dev, qdev);\n\trc = qrtr_endpoint_register(&qdev->ep, QRTR_EP_NID_AUTO);\n\tif (rc)\n\t\treturn rc;\n\n\tdev_dbg(qdev->dev, \"Qualcomm MHI QRTR driver probed\\n\");\n\n\treturn 0;\n}\n\nstatic void qcom_mhi_qrtr_remove(struct mhi_device *mhi_dev)\n{\n\tstruct qrtr_mhi_dev *qdev = dev_get_drvdata(&mhi_dev->dev);\n\n\tqrtr_endpoint_unregister(&qdev->ep);\n\tdev_set_drvdata(&mhi_dev->dev, NULL);\n}\n\nstatic const struct mhi_device_id qcom_mhi_qrtr_id_table[] = {\n\t{ .chan = \"IPCR\" },\n\t{}\n};\nMODULE_DEVICE_TABLE(mhi, qcom_mhi_qrtr_id_table);\n\nstatic struct mhi_driver qcom_mhi_qrtr_driver = {\n\t.probe = qcom_mhi_qrtr_probe,\n\t.remove = qcom_mhi_qrtr_remove,\n\t.dl_xfer_cb = qcom_mhi_qrtr_dl_callback,\n\t.ul_xfer_cb = qcom_mhi_qrtr_ul_callback,\n\t.id_table = qcom_mhi_qrtr_id_table,\n\t.driver = {\n\t\t.name = \"qcom_mhi_qrtr\",\n\t},\n};\n\nmodule_mhi_driver(qcom_mhi_qrtr_driver);\n\nMODULE_AUTHOR(\"<NAME> <<EMAIL>>\");\nMODULE_AUTHOR(\"<NAME> <<EMAIL>>\");\nMODULE_DESCRIPTION(\"Qualcomm IPC-Router MHI interface driver\");\nMODULE_LICENSE(\"GPL v2\");", "/* SPDX-License-Identifier: GPL-2.0 */\n#ifndef __INCLUDE_LINUX_OOM_H\n#define __INCLUDE_LINUX_OOM_H\n\n\n#include <linux/sched/signal.h>\n#include <linux/types.h>\n#include <linux/nodemask.h>\n#include <uapi/linux/oom.h>\n#include <linux/sched/coredump.h> /* MMF_* */\n#include <linux/mm.h> /* VM_FAULT* */\n\nstruct zonelist;\nstruct notifier_block;\nstruct mem_cgroup;\nstruct task_struct;\n\n/*\n * Details of the page allocation that triggered the oom killer that are used to\n * determine what should be killed.\n */\nstruct oom_control {\n\t/* Used to determine cpuset */\n\tstruct zonelist *zonelist;\n\n\t/* Used to determine mempolicy */\n\tnodemask_t *nodemask;\n\n\t/* Memory cgroup in which oom is invoked, or NULL for global oom */\n\tstruct mem_cgroup *memcg;\n\n\t/* Used to determine cpuset and node locality requirement */\n\tconst gfp_t gfp_mask;\n\n\t/*\n\t * order == -1 means the oom kill is required by sysrq, otherwise only\n\t * for display purposes.\n\t */\n\tconst int order;\n\n\t/* Used by oom implementation, do not set */\n\tunsigned long totalpages;\n\tstruct task_struct *chosen;\n\tunsigned long chosen_points;\n};\n\nextern struct mutex oom_lock;\n\nstatic inline void set_current_oom_origin(void)\n{\n\tcurrent->signal->oom_flag_origin = true;\n}\n\nstatic inline void clear_current_oom_origin(void)\n{\n\tcurrent->signal->oom_flag_origin = false;\n}\n\nstatic inline bool oom_task_origin(const struct task_struct *p)\n{\n\treturn p->signal->oom_flag_origin;\n}\n\nstatic inline bool tsk_is_oom_victim(struct task_struct * tsk)\n{\n\treturn tsk->signal->oom_mm;\n}\n\n/*\n * Use this helper if tsk->mm != mm and the victim mm needs a special\n * handling. This is guaranteed to stay true after once set.\n */\nstatic inline bool mm_is_oom_victim(struct mm_struct *mm)\n{\n\treturn test_bit(MMF_OOM_VICTIM, &mm->flags);\n}\n\n/*\n * Checks whether a page fault on the given mm is still reliable.\n * This is no longer true if the oom reaper started to reap the\n * address space which is reflected by MMF_UNSTABLE flag set in\n * the mm. At that moment any !shared mapping would lose the content\n * and could cause a memory corruption (zero pages instead of the\n * original content).\n *\n * User should call this before establishing a page table entry for\n * a !shared mapping and under the proper page table lock.\n *\n * Return 0 when the PF is safe VM_FAULT_SIGBUS otherwise.\n */\nstatic inline int check_stable_address_space(struct mm_struct *mm)\n{\n\tif (unlikely(test_bit(MMF_UNSTABLE, &mm->flags)))\n\t\treturn VM_FAULT_SIGBUS;\n\treturn 0;\n}\n\nvoid __oom_reap_task_mm(struct mm_struct *mm);\n\nextern unsigned long oom_badness(struct task_struct *p,\n\t\tstruct mem_cgroup *memcg, const nodemask_t *nodemask,\n\t\tunsigned long totalpages);\n\nextern bool out_of_memory(struct oom_control *oc);\n\nextern void exit_oom_victim(void);\n\nextern int register_oom_notifier(struct notifier_block *nb);\nextern int unregister_oom_notifier(struct notifier_block *nb);\n\nextern bool oom_killer_disable(signed long timeout);\nextern void oom_killer_enable(void);\n\nextern struct task_struct *find_lock_task_mm(struct task_struct *p);\n\n/* sysctls */\nextern int sysctl_oom_dump_tasks;\nextern int sysctl_oom_kill_allocating_task;\nextern int sysctl_panic_on_oom;\n#endif /* _INCLUDE_LINUX_OOM_H */", "/*\n * IOCTL interface for SCLP\n *\n * Copyright IBM Corp. 2012\n *\n * Author: <NAME> <<EMAIL>>\n */\n\n#include <linux/compat.h>\n#include <linux/uaccess.h>\n#include <linux/miscdevice.h>\n#include <linux/gfp.h>\n#include <linux/module.h>\n#include <linux/ioctl.h>\n#include <linux/fs.h>\n#include <asm/compat.h>\n#include <asm/sclp_ctl.h>\n#include <asm/sclp.h>\n\n#include \"sclp.h\"\n\n/*\n * Supported command words\n */\nstatic unsigned int sclp_ctl_sccb_wlist[] = {\n\t0x00400002,\n\t0x00410002,\n};\n\n/*\n * Check if command word is supported\n */\nstatic int sclp_ctl_cmdw_supported(unsigned int cmdw)\n{\n\tint i;\n\n\tfor (i = 0; i < ARRAY_SIZE(sclp_ctl_sccb_wlist); i++) {\n\t\tif (cmdw == sclp_ctl_sccb_wlist[i])\n\t\t\treturn 1;\n\t}\n\treturn 0;\n}\n\nstatic void __user *u64_to_uptr(u64 value)\n{\n\tif (is_compat_task())\n\t\treturn compat_ptr(value);\n\telse\n\t\treturn (void __user *)(unsigned long)value;\n}\n\n/*\n * Start SCLP request\n */\nstatic int sclp_ctl_ioctl_sccb(void __user *user_area)\n{\n\tstruct sclp_ctl_sccb ctl_sccb;\n\tstruct sccb_header *sccb;\n\tunsigned long copied;\n\tint rc;\n\n\tif (copy_from_user(&ctl_sccb, user_area, sizeof(ctl_sccb)))\n\t\treturn -EFAULT;\n\tif (!sclp_ctl_cmdw_supported(ctl_sccb.cmdw))\n\t\treturn -EOPNOTSUPP;\n\tsccb = (void *) get_zeroed_page(GFP_KERNEL | GFP_DMA);\n\tif (!sccb)\n\t\treturn -ENOMEM;\n\tcopied = PAGE_SIZE -\n\t\tcopy_from_user(sccb, u64_to_uptr(ctl_sccb.sccb), PAGE_SIZE);\n\tif (offsetof(struct sccb_header, length) +\n\t    sizeof(sccb->length) > copied || sccb->length > copied) {\n\t\trc = -EFAULT;\n\t\tgoto out_free;\n\t}\n\tif (sccb->length < 8) {\n\t\trc = -EINVAL;\n\t\tgoto out_free;\n\t}\n\trc = sclp_sync_request(ctl_sccb.cmdw, sccb);\n\tif (rc)\n\t\tgoto out_free;\n\tif (copy_to_user(u64_to_uptr(ctl_sccb.sccb), sccb, sccb->length))\n\t\trc = -EFAULT;\nout_free:\n\tfree_page((unsigned long) sccb);\n\treturn rc;\n}\n\n/*\n * SCLP SCCB ioctl function\n */\nstatic long sclp_ctl_ioctl(struct file *filp, unsigned int cmd,\n\t\t\t   unsigned long arg)\n{\n\tvoid __user *argp;\n\n\tif (is_compat_task())\n\t\targp = compat_ptr(arg);\n\telse\n\t\targp = (void __user *) arg;\n\tswitch (cmd) {\n\tcase SCLP_CTL_SCCB:\n\t\treturn sclp_ctl_ioctl_sccb(argp);\n\tdefault: /* unknown ioctl number */\n\t\treturn -ENOTTY;\n\t}\n}\n\n/*\n * File operations\n */\nstatic const struct file_operations sclp_ctl_fops = {\n\t.owner = THIS_MODULE,\n\t.open = nonseekable_open,\n\t.unlocked_ioctl = sclp_ctl_ioctl,\n\t.compat_ioctl = sclp_ctl_ioctl,\n\t.llseek = no_llseek,\n};\n\n/*\n * Misc device definition\n */\nstatic struct miscdevice sclp_ctl_device = {\n\t.minor = MISC_DYNAMIC_MINOR,\n\t.name = \"sclp\",\n\t.fops = &sclp_ctl_fops,\n};\n\n/*\n * Register sclp_ctl misc device\n */\nstatic int __init sclp_ctl_init(void)\n{\n\treturn misc_register(&sclp_ctl_device);\n}\nmodule_init(sclp_ctl_init);\n\n/*\n * Deregister sclp_ctl misc device\n */\nstatic void __exit sclp_ctl_exit(void)\n{\n\tmisc_deregister(&sclp_ctl_device);\n}\nmodule_exit(sclp_ctl_exit);", "#ifndef _NF_CONNTRACK_EXTEND_H\n#define _NF_CONNTRACK_EXTEND_H\n\n#include <linux/slab.h>\n\n#include <net/netfilter/nf_conntrack.h>\n\nenum nf_ct_ext_id {\n\tNF_CT_EXT_HELPER,\n#if defined(CONFIG_NF_NAT) || defined(CONFIG_NF_NAT_MODULE)\n\tNF_CT_EXT_NAT,\n#endif\n\tNF_CT_EXT_SEQADJ,\n\tNF_CT_EXT_ACCT,\n#ifdef CONFIG_NF_CONNTRACK_EVENTS\n\tNF_CT_EXT_ECACHE,\n#endif\n#ifdef CONFIG_NF_CONNTRACK_ZONES\n\tNF_CT_EXT_ZONE,\n#endif\n#ifdef CONFIG_NF_CONNTRACK_TIMESTAMP\n\tNF_CT_EXT_TSTAMP,\n#endif\n#ifdef CONFIG_NF_CONNTRACK_TIMEOUT\n\tNF_CT_EXT_TIMEOUT,\n#endif\n#ifdef CONFIG_NF_CONNTRACK_LABELS\n\tNF_CT_EXT_LABELS,\n#endif\n#if IS_ENABLED(CONFIG_NETFILTER_SYNPROXY)\n\tNF_CT_EXT_SYNPROXY,\n#endif\n\tNF_CT_EXT_NUM,\n};\n\n#define NF_CT_EXT_HELPER_TYPE struct nf_conn_help\n#define NF_CT_EXT_NAT_TYPE struct nf_conn_nat\n#define NF_CT_EXT_SEQADJ_TYPE struct nf_conn_seqadj\n#define NF_CT_EXT_ACCT_TYPE struct nf_conn_counter\n#define NF_CT_EXT_ECACHE_TYPE struct nf_conntrack_ecache\n#define NF_CT_EXT_ZONE_TYPE struct nf_conntrack_zone\n#define NF_CT_EXT_TSTAMP_TYPE struct nf_conn_tstamp\n#define NF_CT_EXT_TIMEOUT_TYPE struct nf_conn_timeout\n#define NF_CT_EXT_LABELS_TYPE struct nf_conn_labels\n#define NF_CT_EXT_SYNPROXY_TYPE struct nf_conn_synproxy\n\n/* Extensions: optional stuff which isn't permanently in struct. */\nstruct nf_ct_ext {\n\tstruct rcu_head rcu;\n\tu16 offset[NF_CT_EXT_NUM];\n\tu16 len;\n\tchar data[0];\n};\n\nstatic inline bool __nf_ct_ext_exist(const struct nf_ct_ext *ext, u8 id)\n{\n\treturn !!ext->offset[id];\n}\n\nstatic inline bool nf_ct_ext_exist(const struct nf_conn *ct, u8 id)\n{\n\treturn (ct->ext && __nf_ct_ext_exist(ct->ext, id));\n}\n\nstatic inline void *__nf_ct_ext_find(const struct nf_conn *ct, u8 id)\n{\n\tif (!nf_ct_ext_exist(ct, id))\n\t\treturn NULL;\n\n\treturn (void *)ct->ext + ct->ext->offset[id];\n}\n#define nf_ct_ext_find(ext, id)\t\\\n\t((id##_TYPE *)__nf_ct_ext_find((ext), (id)))\n\n/* Destroy all relationships */\nextern void __nf_ct_ext_destroy(struct nf_conn *ct);\nstatic inline void nf_ct_ext_destroy(struct nf_conn *ct)\n{\n\tif (ct->ext)\n\t\t__nf_ct_ext_destroy(ct);\n}\n\n/* Free operation. If you want to free a object referred from private area,\n * please implement __nf_ct_ext_free() and call it.\n */\nstatic inline void nf_ct_ext_free(struct nf_conn *ct)\n{\n\tif (ct->ext)\n\t\tkfree_rcu(ct->ext, rcu);\n}\n\n/* Add this type, returns pointer to data or NULL. */\nvoid *__nf_ct_ext_add_length(struct nf_conn *ct, enum nf_ct_ext_id id,\n\t\t\t     size_t var_alloc_len, gfp_t gfp);\n\n#define nf_ct_ext_add(ct, id, gfp) \\\n\t((id##_TYPE *)__nf_ct_ext_add_length((ct), (id), 0, (gfp)))\n#define nf_ct_ext_add_length(ct, id, len, gfp) \\\n\t((id##_TYPE *)__nf_ct_ext_add_length((ct), (id), (len), (gfp)))\n\n#define NF_CT_EXT_F_PREALLOC\t0x0001\n\nstruct nf_ct_ext_type {\n\t/* Destroys relationships (can be NULL). */\n\tvoid (*destroy)(struct nf_conn *ct);\n\t/* Called when realloacted (can be NULL).\n\t   Contents has already been moved. */\n\tvoid (*move)(void *new, void *old);\n\n\tenum nf_ct_ext_id id;\n\n\tunsigned int flags;\n\n\t/* Length and min alignment. */\n\tu8 len;\n\tu8 align;\n\t/* initial size of nf_ct_ext. */\n\tu8 alloc_size;\n};\n\nint nf_ct_extend_register(struct nf_ct_ext_type *type);\nvoid nf_ct_extend_unregister(struct nf_ct_ext_type *type);\n#endif /* _NF_CONNTRACK_EXTEND_H */", "/*\n * Flexible mmap layout support\n *\n * Based on code by <PERSON><PERSON> and <PERSON><PERSON>, copyrighted\n * as follows:\n *\n * Copyright 2003-2009 Red Hat Inc.\n * All Rights Reserved.\n * Copyright 2005 <PERSON><PERSON>, SUSE Labs.\n * Copyright 2007 <PERSON><PERSON>, SUSE Labs.\n *\n * This program is free software; you can redistribute it and/or modify\n * it under the terms of the GNU General Public License as published by\n * the Free Software Foundation; either version 2 of the License, or\n * (at your option) any later version.\n *\n * This program is distributed in the hope that it will be useful,\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n * GNU General Public License for more details.\n *\n * You should have received a copy of the GNU General Public License\n * along with this program; if not, write to the Free Software\n * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA\n */\n\n#include <linux/personality.h>\n#include <linux/mm.h>\n#include <linux/random.h>\n#include <linux/limits.h>\n#include <linux/sched.h>\n#include <asm/elf.h>\n\nstruct __read_mostly va_alignment va_align = {\n\t.flags = -1,\n};\n\nstatic unsigned long stack_maxrandom_size(void)\n{\n\tunsigned long max = 0;\n\tif ((current->flags & PF_RANDOMIZE) &&\n\t\t!(current->personality & ADDR_NO_RANDOMIZE)) {\n\t\tmax = ((-1UL) & STACK_RND_MASK) << PAGE_SHIFT;\n\t}\n\n\treturn max;\n}\n\n/*\n * Top of mmap area (just below the process stack).\n *\n * Leave an at least ~128 MB hole with possible stack randomization.\n */\n#define MIN_GAP (128*1024*1024UL + stack_maxrandom_size())\n#define MAX_GAP (TASK_SIZE/6*5)\n\nstatic int mmap_is_legacy(void)\n{\n\tif (current->personality & ADDR_COMPAT_LAYOUT)\n\t\treturn 1;\n\n\tif (rlimit(RLIMIT_STACK) == RLIM_INFINITY)\n\t\treturn 1;\n\n\treturn sysctl_legacy_va_layout;\n}\n\nstatic unsigned long mmap_rnd(void)\n{\n\tunsigned long rnd = 0;\n\n\t/*\n\t*  8 bits of randomness in 32bit mmaps, 20 address space bits\n\t* 28 bits of randomness in 64bit mmaps, 40 address space bits\n\t*/\n\tif (current->flags & PF_RANDOMIZE) {\n\t\tif (mmap_is_ia32())\n\t\t\trnd = get_random_int() % (1<<8);\n\t\telse\n\t\t\trnd = get_random_int() % (1<<28);\n\t}\n\treturn rnd << PAGE_SHIFT;\n}\n\nstatic unsigned long mmap_base(void)\n{\n\tunsigned long gap = rlimit(RLIMIT_STACK);\n\n\tif (gap < MIN_GAP)\n\t\tgap = MIN_GAP;\n\telse if (gap > MAX_GAP)\n\t\tgap = MAX_GAP;\n\n\treturn PAGE_ALIGN(TASK_SIZE - gap - mmap_rnd());\n}\n\n/*\n * Bottom-up (legacy) layout on X86_32 did not support randomization, X86_64\n * does, but not when emulating X86_32\n */\nstatic unsigned long mmap_legacy_base(void)\n{\n\tif (mmap_is_ia32())\n\t\treturn TASK_UNMAPPED_BASE;\n\telse\n\t\treturn TASK_UNMAPPED_BASE + mmap_rnd();\n}\n\n/*\n * This function, called very early during the creation of a new\n * process VM image, sets up which VM layout function to use:\n */\nvoid arch_pick_mmap_layout(struct mm_struct *mm)\n{\n\tmm->mmap_legacy_base = mmap_legacy_base();\n\tmm->mmap_base = mmap_base();\n\n\tif (mmap_is_legacy()) {\n\t\tmm->mmap_base = mm->mmap_legacy_base;\n\t\tmm->get_unmapped_area = arch_get_unmapped_area;\n\t\tmm->unmap_area = arch_unmap_area;\n\t} else {\n\t\tmm->get_unmapped_area = arch_get_unmapped_area_topdown;\n\t\tmm->unmap_area = arch_unmap_area_topdown;\n\t}\n}", "", "/*\n *  IP checksumming functions.\n *  (c) 2008 <NAME> <<EMAIL>>\n *\n *  This program is free software; you can redistribute it and/or modify\n *  it under the terms of the GNU General Public License as published by\n *  the Free Software Foundation; under version 2 or later of the License.\n *\n *  This program is distributed in the hope that it will be useful,\n *  but WITHOUT ANY WARRANTY; without even the implied warranty of\n *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n *  GNU General Public License for more details.\n *\n *  You should have received a copy of the GNU General Public License\n *  along with this program; if not, see <http://www.gnu.org/licenses/>.\n */\n\n#include \"qemu/osdep.h\"\n#include \"qemu-common.h\"\n#include \"net/checksum.h\"\n\n#define PROTO_TCP  6\n#define PROTO_UDP 17\n\nuint32_t net_checksum_add_cont(int len, uint8_t *buf, int seq)\n{\n    uint32_t sum = 0;\n    int i;\n\n    for (i = seq; i < seq + len; i++) {\n        if (i & 1) {\n            sum += (uint32_t)buf[i - seq];\n        } else {\n            sum += (uint32_t)buf[i - seq] << 8;\n        }\n    }\n    return sum;\n}\n\nuint16_t net_checksum_finish(uint32_t sum)\n{\n    while (sum>>16)\n\tsum = (sum & 0xFFFF)+(sum >> 16);\n    return ~sum;\n}\n\nuint16_t net_checksum_tcpudp(uint16_t length, uint16_t proto,\n                             uint8_t *addrs, uint8_t *buf)\n{\n    uint32_t sum = 0;\n\n    sum += net_checksum_add(length, buf);         // payload\n    sum += net_checksum_add(8, addrs);            // src + dst address\n    sum += proto + length;                        // protocol & length\n    return net_checksum_finish(sum);\n}\n\nvoid net_checksum_calculate(uint8_t *data, int length)\n{\n    int hlen, plen, proto, csum_offset;\n    uint16_t csum;\n\n    /* Ensure data has complete L2 & L3 headers. */\n    if (length < 14 + 20) {\n        return;\n    }\n\n    if ((data[14] & 0xf0) != 0x40)\n\treturn; /* not IPv4 */\n    hlen  = (data[14] & 0x0f) * 4;\n    plen  = (data[16] << 8 | data[17]) - hlen;\n    proto = data[23];\n\n    switch (proto) {\n    case PROTO_TCP:\n\tcsum_offset = 16;\n\tbreak;\n    case PROTO_UDP:\n\tcsum_offset = 6;\n\tbreak;\n    default:\n\treturn;\n    }\n\n    if (plen < csum_offset + 2 || 14 + hlen + plen > length) {\n        return;\n    }\n\n    data[14+hlen+csum_offset]   = 0;\n    data[14+hlen+csum_offset+1] = 0;\n    csum = net_checksum_tcpudp(plen, proto, data+14+12, data+14+hlen);\n    data[14+hlen+csum_offset]   = csum >> 8;\n    data[14+hlen+csum_offset+1] = csum & 0xff;\n}\n\nuint32_t\nnet_checksum_add_iov(const struct iovec *iov, const unsigned int iov_cnt,\n                     uint32_t iov_off, uint32_t size)\n{\n    size_t iovec_off, buf_off;\n    unsigned int i;\n    uint32_t res = 0;\n    uint32_t seq = 0;\n\n    iovec_off = 0;\n    buf_off = 0;\n    for (i = 0; i < iov_cnt && size; i++) {\n        if (iov_off < (iovec_off + iov[i].iov_len)) {\n            size_t len = MIN((iovec_off + iov[i].iov_len) - iov_off , size);\n            void *chunk_buf = iov[i].iov_base + (iov_off - iovec_off);\n\n            res += net_checksum_add_cont(len, chunk_buf, seq);\n            seq += len;\n\n            buf_off += len;\n            iov_off += len;\n            size -= len;\n        }\n        iovec_off += iov[i].iov_len;\n    }\n    return res;\n}", "#ifndef _ASM_X86_ASM_H\n#define _ASM_X86_ASM_H\n\n#ifdef __ASSEMBLY__\n# define __ASM_FORM(x)\tx\n# define __ASM_FORM_RAW(x)     x\n# define __ASM_FORM_COMMA(x) x,\n#else\n# define __ASM_FORM(x)\t\" \" #x \" \"\n# define __ASM_FORM_RAW(x)     #x\n# define __ASM_FORM_COMMA(x) \" \" #x \",\"\n#endif\n\n#ifdef CONFIG_X86_32\n# define __ASM_SEL(a,b) __ASM_FORM(a)\n# define __ASM_SEL_RAW(a,b) __ASM_FORM_RAW(a)\n#else\n# define __ASM_SEL(a,b) __ASM_FORM(b)\n# define __ASM_SEL_RAW(a,b) __ASM_FORM_RAW(b)\n#endif\n\n#define __ASM_SIZE(inst, ...)\t__ASM_SEL(inst##l##__VA_ARGS__, \\\n\t\t\t\t\t  inst##q##__VA_ARGS__)\n#define __ASM_REG(reg)         __ASM_SEL_RAW(e##reg, r##reg)\n\n#define _ASM_PTR\t__ASM_SEL(.long, .quad)\n#define _ASM_ALIGN\t__ASM_SEL(.balign 4, .balign 8)\n\n#define _ASM_MOV\t__ASM_SIZE(mov)\n#define _ASM_INC\t__ASM_SIZE(inc)\n#define _ASM_DEC\t__ASM_SIZE(dec)\n#define _ASM_ADD\t__ASM_SIZE(add)\n#define _ASM_SUB\t__ASM_SIZE(sub)\n#define _ASM_XADD\t__ASM_SIZE(xadd)\n\n#define _ASM_AX\t\t__ASM_REG(ax)\n#define _ASM_BX\t\t__ASM_REG(bx)\n#define _ASM_CX\t\t__ASM_REG(cx)\n#define _ASM_DX\t\t__ASM_REG(dx)\n#define _ASM_SP\t\t__ASM_REG(sp)\n#define _ASM_BP\t\t__ASM_REG(bp)\n#define _ASM_SI\t\t__ASM_REG(si)\n#define _ASM_DI\t\t__ASM_REG(di)\n\n/* Exception table entry */\n#ifdef __ASSEMBLY__\n# define _ASM_EXTABLE_HANDLE(from, to, handler)\t\t\t\\\n\t.pushsection \"__ex_table\",\"a\" ;\t\t\t\t\\\n\t.balign 4 ;\t\t\t\t\t\t\\\n\t.long (from) - . ;\t\t\t\t\t\\\n\t.long (to) - . ;\t\t\t\t\t\\\n\t.long (handler) - . ;\t\t\t\t\t\\\n\t.popsection\n\n# define _ASM_EXTABLE(from, to)\t\t\t\t\t\\\n\t_ASM_EXTABLE_HANDLE(from, to, ex_handler_default)\n\n# define _ASM_EXTABLE_FAULT(from, to)\t\t\t\t\\\n\t_ASM_EXTABLE_HANDLE(from, to, ex_handler_fault)\n\n# define _ASM_EXTABLE_EX(from, to)\t\t\t\t\\\n\t_ASM_EXTABLE_HANDLE(from, to, ex_handler_ext)\n\n# define _ASM_NOKPROBE(entry)\t\t\t\t\t\\\n\t.pushsection \"_kprobe_blacklist\",\"aw\" ;\t\t\t\\\n\t_ASM_ALIGN ;\t\t\t\t\t\t\\\n\t_ASM_PTR (entry);\t\t\t\t\t\\\n\t.popsection\n#else\n# define _EXPAND_EXTABLE_HANDLE(x) #x\n# define _ASM_EXTABLE_HANDLE(from, to, handler)\t\t\t\\\n\t\" .pushsection \\\"__ex_table\\\",\\\"a\\\"\\n\"\t\t\t\\\n\t\" .balign 4\\n\"\t\t\t\t\t\t\\\n\t\" .long (\" #from \") - .\\n\"\t\t\t\t\\\n// ... 函数内容截断 ...\n#define _ASM_XADD\t__ASM_SIZE(xadd)\n\n#define _ASM_AX\t\t__ASM_REG(ax)\n#define _ASM_BX\t\t__ASM_REG(bx)\n#define _ASM_CX\t\t__ASM_REG(cx)\n#define _ASM_DX\t\t__ASM_REG(dx)\n#define _ASM_SP\t\t__ASM_REG(sp)\n#define _ASM_BP\t\t__ASM_REG(bp)\n#define _ASM_SI\t\t__ASM_REG(si)\n#define _ASM_DI\t\t__ASM_REG(di)\n\n/* Exception table entry */\n#ifdef __ASSEMBLY__\n# define _ASM_EXTABLE(from,to)\t\t\t\t\t\\\n\t.pushsection \"__ex_table\",\"a\" ;\t\t\t\t\\\n\t.balign 8 ;\t\t\t\t\t\t\\\n\t.long (from) - . ;\t\t\t\t\t\\\n\t.long (to) - . ;\t\t\t\t\t\\\n\t.popsection\n\n# define _ASM_EXTABLE_EX(from,to)\t\t\t\t\\\n\t.pushsection \"__ex_table\",\"a\" ;\t\t\t\t\\\n\t.balign 8 ;\t\t\t\t\t\t\\\n\t.long (from) - . ;\t\t\t\t\t\\\n\t.long (to) - . + 0x7ffffff0 ;\t\t\t\t\\\n\t.popsection\n\n# define _ASM_NOKPROBE(entry)\t\t\t\t\t\\\n\t.pushsection \"_kprobe_blacklist\",\"aw\" ;\t\t\t\\\n\t_ASM_ALIGN ;\t\t\t\t\t\t\\\n\t_ASM_PTR (entry);\t\t\t\t\t\\\n\t.popsection\n\n.macro ALIGN_DESTINATION\n\t/* check for bad alignment of destination */\n\tmovl %edi,%ecx\n\tandl $7,%ecx\n\tjz 102f\t\t\t\t/* already aligned */\n\tsubl $8,%ecx\n\tnegl %ecx\n\tsubl %ecx,%edx\n100:\tmovb (%rsi),%al\n101:\tmovb %al,(%rdi)\n\tincq %rsi\n\tincq %rdi\n\tdecl %ecx\n\tjnz 100b\n102:\n\t.section .fixup,\"ax\"\n103:\taddl %ecx,%edx\t\t\t/* ecx is zerorest also */\n\tjmp copy_user_handle_tail\n\t.previous\n\n\t_ASM_EXTABLE(100b,103b)\n\t_ASM_EXTABLE(101b,103b)\n\t.endm\n\n#else\n# define _ASM_EXTABLE(from,to)\t\t\t\t\t\\\n\t\" .pushsection \\\"__ex_table\\\",\\\"a\\\"\\n\"\t\t\t\\\n\t\" .balign 8\\n\"\t\t\t\t\t\t\\\n\t\" .long (\" #from \") - .\\n\"\t\t\t\t\\\n\t\" .long (\" #to \") - .\\n\"\t\t\t\t\\\n\t\" .popsection\\n\"\n\n# define _ASM_EXTABLE_EX(from,to)\t\t\t\t\\\n\t\" .pushsection \\\"__ex_table\\\",\\\"a\\\"\\n\"\t\t\t\\\n\t\" .balign 8\\n\"\t\t\t\t\t\t\\\n\t\" .long (\" #from \") - .\\n\"\t\t\t\t\\\n\t\" .long (\" #to \") - . + 0x7ffffff0\\n\"\t\t\t\\\n\t\" .popsection\\n\"\n/* For C file, we already have NOKPROBE_SYMBOL macro */\n#endif\n\n#endif /* _ASM_X86_ASM_H */", "// SPDX-License-Identifier: GPL-2.0-only\n/*\n * Authors: <AUTHORS>\n */\n\n#include <net/ipv6.h>\n#include <net/rpl.h>\n\n#define IPV6_PFXTAIL_LEN(x) (sizeof(struct in6_addr) - (x))\n#define IPV6_RPL_BEST_ADDR_COMPRESSION 15\n\nstatic void ipv6_rpl_addr_decompress(struct in6_addr *dst,\n\t\t\t\t     const struct in6_addr *daddr,\n\t\t\t\t     const void *post, unsigned char pfx)\n{\n\tmemcpy(dst, daddr, pfx);\n\tmemcpy(&dst->s6_addr[pfx], post, IPV6_PFXTAIL_LEN(pfx));\n}\n\nstatic void ipv6_rpl_addr_compress(void *dst, const struct in6_addr *addr,\n\t\t\t\t   unsigned char pfx)\n{\n\tmemcpy(dst, &addr->s6_addr[pfx], IPV6_PFXTAIL_LEN(pfx));\n}\n\nstatic void *ipv6_rpl_segdata_pos(const struct ipv6_rpl_sr_hdr *hdr, int i)\n{\n\treturn (void *)&hdr->rpl_segdata[i * IPV6_PFXTAIL_LEN(hdr->cmpri)];\n}\n\nsize_t ipv6_rpl_srh_size(unsigned char n, unsigned char cmpri,\n\t\t\t unsigned char cmpre)\n{\n\treturn sizeof(struct ipv6_rpl_sr_hdr) + (n * IPV6_PFXTAIL_LEN(cmpri)) +\n\t\tIPV6_PFXTAIL_LEN(cmpre);\n}\n\nvoid ipv6_rpl_srh_decompress(struct ipv6_rpl_sr_hdr *outhdr,\n\t\t\t     const struct ipv6_rpl_sr_hdr *inhdr,\n\t\t\t     const struct in6_addr *daddr, unsigned char n)\n{\n\tint i;\n\n\touthdr->nexthdr = inhdr->nexthdr;\n\touthdr->hdrlen = (((n + 1) * sizeof(struct in6_addr)) >> 3);\n\touthdr->pad = 0;\n\touthdr->type = inhdr->type;\n\touthdr->segments_left = inhdr->segments_left;\n\touthdr->cmpri = 0;\n\touthdr->cmpre = 0;\n\n\tfor (i = 0; i < n; i++)\n\t\tipv6_rpl_addr_decompress(&outhdr->rpl_segaddr[i], daddr,\n\t\t\t\t\t ipv6_rpl_segdata_pos(inhdr, i),\n\t\t\t\t\t inhdr->cmpri);\n\n\tipv6_rpl_addr_decompress(&outhdr->rpl_segaddr[n], daddr,\n\t\t\t\t ipv6_rpl_segdata_pos(inhdr, n),\n\t\t\t\t inhdr->cmpre);\n}\n\nstatic unsigned char ipv6_rpl_srh_calc_cmpri(const struct ipv6_rpl_sr_hdr *inhdr,\n\t\t\t\t\t     const struct in6_addr *daddr,\n\t\t\t\t\t     unsigned char n)\n{\n\tunsigned char plen;\n\tint i;\n\n\tfor (plen = 0; plen < sizeof(*daddr); plen++) {\n\t\tfor (i = 0; i < n; i++) {\n\t\t\tif (daddr->s6_addr[plen] !=\n\t\t\t    inhdr->rpl_segaddr[i].s6_addr[plen])\n\t\t\t\treturn plen;\n\t\t}\n\t}\n\n\treturn IPV6_RPL_BEST_ADDR_COMPRESSION;\n}\n\nstatic unsigned char ipv6_rpl_srh_calc_cmpre(const struct in6_addr *daddr,\n\t\t\t\t\t     const struct in6_addr *last_segment)\n{\n\tunsigned int plen;\n\n\tfor (plen = 0; plen < sizeof(*daddr); plen++) {\n\t\tif (daddr->s6_addr[plen] != last_segment->s6_addr[plen])\n\t\t\treturn plen;\n\t}\n\n\treturn IPV6_RPL_BEST_ADDR_COMPRESSION;\n}\n\nvoid ipv6_rpl_srh_compress(struct ipv6_rpl_sr_hdr *outhdr,\n\t\t\t   const struct ipv6_rpl_sr_hdr *inhdr,\n\t\t\t   const struct in6_addr *daddr, unsigned char n)\n{\n\tunsigned char cmpri, cmpre;\n\tsize_t seglen;\n\tint i;\n\n\tcmpri = ipv6_rpl_srh_calc_cmpri(inhdr, daddr, n);\n\tcmpre = ipv6_rpl_srh_calc_cmpre(daddr, &inhdr->rpl_segaddr[n]);\n\n\touthdr->nexthdr = inhdr->nexthdr;\n\tseglen = (n * IPV6_PFXTAIL_LEN(cmpri)) + IPV6_PFXTAIL_LEN(cmpre);\n\touthdr->hdrlen = seglen >> 3;\n\tif (seglen & 0x7) {\n\t\touthdr->hdrlen++;\n\t\touthdr->pad = 8 - (seglen & 0x7);\n\t} else {\n\t\touthdr->pad = 0;\n\t}\n\touthdr->type = inhdr->type;\n\touthdr->segments_left = inhdr->segments_left;\n\touthdr->cmpri = cmpri;\n\touthdr->cmpre = cmpre;\n\n\tfor (i = 0; i < n; i++)\n\t\tipv6_rpl_addr_compress(ipv6_rpl_segdata_pos(outhdr, i),\n\t\t\t\t       &inhdr->rpl_segaddr[i], cmpri);\n\n\tipv6_rpl_addr_compress(ipv6_rpl_segdata_pos(outhdr, n),\n\t\t\t       &inhdr->rpl_segaddr[n], cmpre);\n}", "// SPDX-License-Identifier: GPL-2.0-or-later\n/*\n *   Copyright (C) 2018 Samsung Electronics Co., Ltd.\n */\n\n#include <linux/list.h>\n#include <linux/slab.h>\n#include <linux/xarray.h>\n\n#include \"../transport_ipc.h\"\n#include \"../connection.h\"\n\n#include \"tree_connect.h\"\n#include \"user_config.h\"\n#include \"share_config.h\"\n#include \"user_session.h\"\n\nstruct ksmbd_tree_conn_status\nksmbd_tree_conn_connect(struct ksmbd_conn *conn, struct ksmbd_session *sess,\n\t\t\tconst char *share_name)\n{\n\tstruct ksmbd_tree_conn_status status = {-ENOENT, NULL};\n\tstruct ksmbd_tree_connect_response *resp = NULL;\n\tstruct ksmbd_share_config *sc;\n\tstruct ksmbd_tree_connect *tree_conn = NULL;\n\tstruct sockaddr *peer_addr;\n\tint ret;\n\n\tsc = ksmbd_share_config_get(conn->um, share_name);\n\tif (!sc)\n\t\treturn status;\n\n\ttree_conn = kzalloc(sizeof(struct ksmbd_tree_connect), GFP_KERNEL);\n\tif (!tree_conn) {\n\t\tstatus.ret = -ENOMEM;\n\t\tgoto out_error;\n\t}\n\n\ttree_conn->id = ksmbd_acquire_tree_conn_id(sess);\n\tif (tree_conn->id < 0) {\n\t\tstatus.ret = -EINVAL;\n\t\tgoto out_error;\n\t}\n\n\tpeer_addr = KSMBD_TCP_PEER_SOCKADDR(conn);\n\tresp = ksmbd_ipc_tree_connect_request(sess,\n\t\t\t\t\t      sc,\n\t\t\t\t\t      tree_conn,\n\t\t\t\t\t      peer_addr);\n\tif (!resp) {\n\t\tstatus.ret = -EINVAL;\n\t\tgoto out_error;\n\t}\n\n\tstatus.ret = resp->status;\n\tif (status.ret != KSMBD_TREE_CONN_STATUS_OK)\n\t\tgoto out_error;\n\n\ttree_conn->flags = resp->connection_flags;\n\tif (test_tree_conn_flag(tree_conn, KSMBD_TREE_CONN_FLAG_UPDATE)) {\n\t\tstruct ksmbd_share_config *new_sc;\n\n\t\tksmbd_share_config_del(sc);\n\t\tnew_sc = ksmbd_share_config_get(conn->um, share_name);\n\t\tif (!new_sc) {\n\t\t\tpr_err(\"Failed to update stale share config\\n\");\n\t\t\tstatus.ret = -ESTALE;\n\t\t\tgoto out_error;\n\t\t}\n\t\tksmbd_share_config_put(sc);\n\t\tsc = new_sc;\n\t}\n\n\ttree_conn->user = sess->user;\n\ttree_conn->share_conf = sc;\n\tstatus.tree_conn = tree_conn;\n\n\tret = xa_err(xa_store(&sess->tree_conns, tree_conn->id, tree_conn,\n\t\t\t      GFP_KERNEL));\n\tif (ret) {\n\t\tstatus.ret = -ENOMEM;\n\t\tgoto out_error;\n\t}\n\tkvfree(resp);\n\treturn status;\n\nout_error:\n\tif (tree_conn)\n\t\tksmbd_release_tree_conn_id(sess, tree_conn->id);\n\tksmbd_share_config_put(sc);\n\tkfree(tree_conn);\n\tkvfree(resp);\n\treturn status;\n}\n\nint ksmbd_tree_conn_disconnect(struct ksmbd_session *sess,\n\t\t\t       struct ksmbd_tree_connect *tree_conn)\n{\n\tint ret;\n\n\tret = ksmbd_ipc_tree_disconnect_request(sess->id, tree_conn->id);\n\tksmbd_release_tree_conn_id(sess, tree_conn->id);\n\txa_erase(&sess->tree_conns, tree_conn->id);\n\tksmbd_share_config_put(tree_conn->share_conf);\n\tkfree(tree_conn);\n\treturn ret;\n}\n\nstruct ksmbd_tree_connect *ksmbd_tree_conn_lookup(struct ksmbd_session *sess,\n\t\t\t\t\t\t  unsigned int id)\n{\n\treturn xa_load(&sess->tree_conns, id);\n}\n\nstruct ksmbd_share_config *ksmbd_tree_conn_share(struct ksmbd_session *sess,\n\t\t\t\t\t\t unsigned int id)\n{\n\tstruct ksmbd_tree_connect *tc;\n\n\ttc = ksmbd_tree_conn_lookup(sess, id);\n\tif (tc)\n\t\treturn tc->share_conf;\n\treturn NULL;\n}\n\nint ksmbd_tree_conn_session_logoff(struct ksmbd_session *sess)\n{\n\tint ret = 0;\n\tstruct ksmbd_tree_connect *tc;\n\tunsigned long id;\n\n\tif (!sess)\n\t\treturn -EINVAL;\n\n\txa_for_each(&sess->tree_conns, id, tc)\n\t\tret |= ksmbd_tree_conn_disconnect(sess, tc);\n\txa_destroy(&sess->tree_conns);\n\treturn ret;\n}", "/*\n *\tcommon UDP/RAW code\n *\tLinux INET implementation\n *\n * Authors: <AUTHORS>\n *\n * \tThis program is free software; you can redistribute it and/or\n * \tmodify it under the terms of the GNU General Public License\n * \tas published by the Free Software Foundation; either version\n * \t2 of the License, or (at your option) any later version.\n */\n\n#include <linux/types.h>\n#include <linux/module.h>\n#include <linux/ip.h>\n#include <linux/in.h>\n#include <net/ip.h>\n#include <net/sock.h>\n#include <net/route.h>\n#include <net/tcp_states.h>\n\nint ip4_datagram_connect(struct sock *sk, struct sockaddr *uaddr, int addr_len)\n{\n\tstruct inet_sock *inet = inet_sk(sk);\n\tstruct sockaddr_in *usin = (struct sockaddr_in *) uaddr;\n\tstruct flowi4 *fl4;\n\tstruct rtable *rt;\n\t__be32 saddr;\n\tint oif;\n\tint err;\n\n\n\tif (addr_len < sizeof(*usin))\n\t\treturn -EINVAL;\n\n\tif (usin->sin_family != AF_INET)\n\t\treturn -EAFNOSUPPORT;\n\n\tsk_dst_reset(sk);\n\n\tlock_sock(sk);\n\n\toif = sk->sk_bound_dev_if;\n\tsaddr = inet->inet_saddr;\n\tif (ipv4_is_multicast(usin->sin_addr.s_addr)) {\n\t\tif (!oif)\n\t\t\toif = inet->mc_index;\n\t\tif (!saddr)\n\t\t\tsaddr = inet->mc_addr;\n\t}\n\tfl4 = &inet->cork.fl.u.ip4;\n\trt = ip_route_connect(fl4, usin->sin_addr.s_addr, saddr,\n\t\t\t      RT_CONN_FLAGS(sk), oif,\n\t\t\t      sk->sk_protocol,\n\t\t\t      inet->inet_sport, usin->sin_port, sk, true);\n\tif (IS_ERR(rt)) {\n\t\terr = PTR_ERR(rt);\n\t\tif (err == -ENETUNREACH)\n\t\t\tIP_INC_STATS(sock_net(sk), IPSTATS_MIB_OUTNOROUTES);\n\t\tgoto out;\n\t}\n\n\tif ((rt->rt_flags & RTCF_BROADCAST) && !sock_flag(sk, SOCK_BROADCAST)) {\n\t\tip_rt_put(rt);\n\t\terr = -EACCES;\n\t\tgoto out;\n\t}\n\tif (!inet->inet_saddr)\n\t\tinet->inet_saddr = fl4->saddr;\t/* Update source address */\n\tif (!inet->inet_rcv_saddr) {\n\t\tinet->inet_rcv_saddr = fl4->saddr;\n\t\tif (sk->sk_prot->rehash)\n\t\t\tsk->sk_prot->rehash(sk);\n\t}\n\tinet->inet_daddr = fl4->daddr;\n\tinet->inet_dport = usin->sin_port;\n\tsk->sk_state = TCP_ESTABLISHED;\n\tinet->inet_id = jiffies;\n\n\tsk_dst_set(sk, &rt->dst);\n\terr = 0;\nout:\n\trelease_sock(sk);\n\treturn err;\n}\nEXPORT_SYMBOL(ip4_datagram_connect);\n\n/* Because UDP xmit path can manipulate sk_dst_cache without holding\n * socket lock, we need to use sk_dst_set() here,\n * even if we own the socket lock.\n */\nvoid ip4_datagram_release_cb(struct sock *sk)\n{\n\tconst struct inet_sock *inet = inet_sk(sk);\n\tconst struct ip_options_rcu *inet_opt;\n\t__be32 daddr = inet->inet_daddr;\n\tstruct dst_entry *dst;\n\tstruct flowi4 fl4;\n\tstruct rtable *rt;\n\n\trcu_read_lock();\n\n// ... 函数内容截断 ...\n\n\tif ((rt->rt_flags & RTCF_BROADCAST) && !sock_flag(sk, SOCK_BROADCAST)) {\n\t\tip_rt_put(rt);\n\t\terr = -EACCES;\n\t\tgoto out;\n\t}\n\tif (!inet->inet_saddr)\n\t\tinet->inet_saddr = fl4->saddr;\t/* Update source address */\n\tif (!inet->inet_rcv_saddr) {\n\t\tinet->inet_rcv_saddr = fl4->saddr;\n\t\tif (sk->sk_prot->rehash)\n\t\t\tsk->sk_prot->rehash(sk);\n\t}\n\tinet->inet_daddr = fl4->daddr;\n\tinet->inet_dport = usin->sin_port;\n\tsk->sk_state = TCP_ESTABLISHED;\n\tinet->inet_id = jiffies;\n\n\tsk_dst_set(sk, &rt->dst);\n\terr = 0;\nout:\n\trelease_sock(sk);\n\treturn err;\n}\nEXPORT_SYMBOL(ip4_datagram_connect);\n\nvoid ip4_datagram_release_cb(struct sock *sk)\n{\n\tconst struct inet_sock *inet = inet_sk(sk);\n\tconst struct ip_options_rcu *inet_opt;\n\t__be32 daddr = inet->inet_daddr;\n\tstruct flowi4 fl4;\n\tstruct rtable *rt;\n\n\tif (! __sk_dst_get(sk) || __sk_dst_check(sk, 0))\n\t\treturn;\n\n\trcu_read_lock();\n\tinet_opt = rcu_dereference(inet->inet_opt);\n\tif (inet_opt && inet_opt->opt.srr)\n\t\tdaddr = inet_opt->opt.faddr;\n\trt = ip_route_output_ports(sock_net(sk), &fl4, sk, daddr,\n\t\t\t\t   inet->inet_saddr, inet->inet_dport,\n\t\t\t\t   inet->inet_sport, sk->sk_protocol,\n\t\t\t\t   RT_CONN_FLAGS(sk), sk->sk_bound_dev_if);\n\tif (!IS_ERR(rt))\n\t\t__sk_dst_set(sk, &rt->dst);\n\trcu_read_unlock();\n}\nEXPORT_SYMBOL_GPL(ip4_datagram_release_cb);", "// SPDX-License-Identifier: GPL-2.0\n/* Copyright (c) 2018, Linaro Ltd */\n\n#include <linux/miscdevice.h>\n#include <linux/module.h>\n#include <linux/poll.h>\n#include <linux/skbuff.h>\n#include <linux/uaccess.h>\n\n#include \"qrtr.h\"\n\nstruct qrtr_tun {\n\tstruct qrtr_endpoint ep;\n\n\tstruct sk_buff_head queue;\n\twait_queue_head_t readq;\n};\n\nstatic int qrtr_tun_send(struct qrtr_endpoint *ep, struct sk_buff *skb)\n{\n\tstruct qrtr_tun *tun = container_of(ep, struct qrtr_tun, ep);\n\n\tskb_queue_tail(&tun->queue, skb);\n\n\t/* wake up any blocking processes, waiting for new data */\n\twake_up_interruptible(&tun->readq);\n\n\treturn 0;\n}\n\nstatic int qrtr_tun_open(struct inode *inode, struct file *filp)\n{\n\tstruct qrtr_tun *tun;\n\n\ttun = kzalloc(sizeof(*tun), GFP_KERNEL);\n\tif (!tun)\n\t\treturn -ENOMEM;\n\n\tskb_queue_head_init(&tun->queue);\n\tinit_waitqueue_head(&tun->readq);\n\n\ttun->ep.xmit = qrtr_tun_send;\n\n\tfilp->private_data = tun;\n\n\treturn qrtr_endpoint_register(&tun->ep, QRTR_EP_NID_AUTO);\n}\n\nstatic ssize_t qrtr_tun_read_iter(struct kiocb *iocb, struct iov_iter *to)\n{\n\tstruct file *filp = iocb->ki_filp;\n\tstruct qrtr_tun *tun = filp->private_data;\n\tstruct sk_buff *skb;\n\tint count;\n\n\twhile (!(skb = skb_dequeue(&tun->queue))) {\n\t\tif (filp->f_flags & O_NONBLOCK)\n\t\t\treturn -EAGAIN;\n\n\t\t/* Wait until we get data or the endpoint goes away */\n\t\tif (wait_event_interruptible(tun->readq,\n\t\t\t\t\t     !skb_queue_empty(&tun->queue)))\n\t\t\treturn -ERESTARTSYS;\n\t}\n\n\tcount = min_t(size_t, iov_iter_count(to), skb->len);\n\tif (copy_to_iter(skb->data, count, to) != count)\n\t\tcount = -EFAULT;\n\n\tkfree_skb(skb);\n\n\treturn count;\n}\n\nstatic ssize_t qrtr_tun_write_iter(struct kiocb *iocb, struct iov_iter *from)\n{\n\tstruct file *filp = iocb->ki_filp;\n\tstruct qrtr_tun *tun = filp->private_data;\n\tsize_t len = iov_iter_count(from);\n\tssize_t ret;\n\tvoid *kbuf;\n\n\tkbuf = kzalloc(len, GFP_KERNEL);\n\tif (!kbuf)\n\t\treturn -ENOMEM;\n\n\tif (!copy_from_iter_full(kbuf, len, from)) {\n\t\tkfree(kbuf);\n\t\treturn -EFAULT;\n\t}\n\n\tret = qrtr_endpoint_post(&tun->ep, kbuf, len);\n\n\tkfree(kbuf);\n\treturn ret < 0 ? ret : len;\n}\n\nstatic __poll_t qrtr_tun_poll(struct file *filp, poll_table *wait)\n{\n\tstruct qrtr_tun *tun = filp->private_data;\n\t__poll_t mask = 0;\n\n\tpoll_wait(filp, &tun->readq, wait);\n\n\tif (!skb_queue_empty(&tun->queue))\n\t\tmask |= EPOLLIN | EPOLLRDNORM;\n\n\treturn mask;\n}\n\nstatic int qrtr_tun_release(struct inode *inode, struct file *filp)\n{\n\tstruct qrtr_tun *tun = filp->private_data;\n\tstruct sk_buff *skb;\n\n\tqrtr_endpoint_unregister(&tun->ep);\n\n\t/* Discard all SKBs */\n\twhile (!skb_queue_empty(&tun->queue)) {\n\t\tskb = skb_dequeue(&tun->queue);\n\t\tkfree_skb(skb);\n\t}\n\n\tkfree(tun);\n\n\treturn 0;\n}\n\nstatic const struct file_operations qrtr_tun_ops = {\n\t.owner = THIS_MODULE,\n\t.open = qrtr_tun_open,\n\t.poll = qrtr_tun_poll,\n\t.read_iter = qrtr_tun_read_iter,\n\t.write_iter = qrtr_tun_write_iter,\n\t.release = qrtr_tun_release,\n};\n\nstatic struct miscdevice qrtr_tun_miscdev = {\n\t.minor = MISC_DYNAMIC_MINOR,\n\t.name = \"qrtr-tun\",\n\t.fops = &qrtr_tun_ops,\n};\n\nstatic int __init qrtr_tun_init(void)\n{\n\tint ret;\n\n\tret = misc_register(&qrtr_tun_miscdev);\n\tif (ret)\n\t\tpr_err(\"failed to register Qualcomm IPC Router tun device\\n\");\n\n\treturn ret;\n}\n\nstatic void __exit qrtr_tun_exit(void)\n{\n\tmisc_deregister(&qrtr_tun_miscdev);\n}\n\nmodule_init(qrtr_tun_init);\nmodule_exit(qrtr_tun_exit);\n\nMODULE_DESCRIPTION(\"Qualcomm IPC Router TUN device\");\nMODULE_LICENSE(\"GPL v2\");", "// ... 函数内容截断 ...\n\treturn rnd << PAGE_SHIFT;\n}\n\nstatic unsigned long mmap_base(unsigned long rnd)\n{\n\tunsigned long gap = rlimit(RLIMIT_STACK);\n\n\tif (gap < MIN_GAP)\n\t\tgap = MIN_GAP;\n\telse if (gap > MAX_GAP)\n\t\tgap = MAX_GAP;\n\n\treturn PAGE_ALIGN(TASK_SIZE - gap - rnd);\n}\n\n/*\n * Bottom-up (legacy) layout on X86_32 did not support randomization, X86_64\n * does, but not when emulating X86_32\n */\nstatic unsigned long mmap_legacy_base(unsigned long rnd)\n{\n\tif (mmap_is_ia32())\n\t\treturn TASK_UNMAPPED_BASE;\n\telse\n\t\treturn TASK_UNMAPPED_BASE + rnd;\n}\n\n/*\n * This function, called very early during the creation of a new\n * process VM image, sets up which VM layout function to use:\n */\nvoid arch_pick_mmap_layout(struct mm_struct *mm)\n{\n\tunsigned long random_factor = 0UL;\n\n\tif (current->flags & PF_RANDOMIZE)\n\t\trandom_factor = arch_mmap_rnd();\n\n\tmm->mmap_legacy_base = mmap_legacy_base(random_factor);\n\n\tif (mmap_is_legacy()) {\n\t\tmm->mmap_base = mm->mmap_legacy_base;\n\t\tmm->get_unmapped_area = arch_get_unmapped_area;\n\t} else {\n\t\tmm->mmap_base = mmap_base(random_factor);\n\t\tmm->get_unmapped_area = arch_get_unmapped_area_topdown;\n\t}\n}\n\nconst char *arch_vma_name(struct vm_area_struct *vma)\n{\n\tif (vma->vm_flags & VM_MPX)\n\t\treturn \"[mpx]\";\n\treturn NULL;\n}", "/*\n * Copyright 2019-2020 The OpenSSL Project Authors. All Rights Reserved.\n *\n * Licensed under the Apache License 2.0 (the \"License\").  You may not use\n * this file except in compliance with the License.  You can obtain a copy\n * in the file LICENSE in the source distribution or at\n * https://www.openssl.org/source/license.html\n */\n\n#include \"internal/ffc.h\"\n\n/*\n * See SP800-56Ar3 Section 5.6.2.3.1 : FFC Partial public key validation.\n * To only be used with ephemeral FFC public keys generated using the approved\n * safe-prime groups. (Checks that the public key is in the range [2, p - 1]\n *\n * ret contains 0 on success, or error flags (see FFC_ERROR_PUBKEY_TOO_SMALL)\n */\nint ossl_ffc_validate_public_key_partial(const FFC_PARAMS *params,\n                                         const BIGNUM *pub_key, int *ret)\n{\n    int ok = 0;\n    BIGNUM *tmp = NULL;\n    BN_CTX *ctx = NULL;\n\n    *ret = 0;\n    if (params == NULL || pub_key == NULL || params->p == NULL) {\n        *ret = FFC_ERROR_PASSED_NULL_PARAM;\n        return 0;\n    }\n\n    ctx = BN_CTX_new_ex(NULL);\n    if (ctx == NULL)\n        goto err;\n\n    BN_CTX_start(ctx);\n    tmp = BN_CTX_get(ctx);\n    /* Step(1): Verify pub_key >= 2 */\n    if (tmp == NULL\n        || !BN_set_word(tmp, 1))\n        goto err;\n    if (BN_cmp(pub_key, tmp) <= 0) {\n        *ret |= FFC_ERROR_PUBKEY_TOO_SMALL;\n        goto err;\n    }\n    /* Step(1): Verify pub_key <=  p-2 */\n    if (BN_copy(tmp, params->p) == NULL\n        || !BN_sub_word(tmp, 1))\n        goto err;\n    if (BN_cmp(pub_key, tmp) >= 0) {\n        *ret |= FFC_ERROR_PUBKEY_TOO_LARGE;\n        goto err;\n    }\n    ok = 1;\n err:\n    if (ctx != NULL) {\n        BN_CTX_end(ctx);\n        BN_CTX_free(ctx);\n    }\n    return ok;\n}\n\n/*\n * See SP800-56Ar3 Section 5.6.2.3.1 : FFC Full public key validation.\n */\nint ossl_ffc_validate_public_key(const FFC_PARAMS *params,\n                                 const BIGNUM *pub_key, int *ret)\n{\n    int ok = 0;\n    BIGNUM *tmp = NULL;\n    BN_CTX *ctx = NULL;\n\n    if (!ossl_ffc_validate_public_key_partial(params, pub_key, ret))\n        return 0;\n\n    if (params->q != NULL) {\n        ctx = BN_CTX_new_ex(NULL);\n        if (ctx == NULL)\n            goto err;\n        BN_CTX_start(ctx);\n        tmp = BN_CTX_get(ctx);\n\n        /* Check pub_key^q == 1 mod p */\n        if (tmp == NULL\n            || !BN_mod_exp(tmp, pub_key, params->q, params->p, ctx))\n            goto err;\n        if (!BN_is_one(tmp)) {\n            *ret |= FFC_ERROR_PUBKEY_INVALID;\n            goto err;\n        }\n    }\n\n    ok = 1;\n err:\n    if (ctx != NULL) {\n        BN_CTX_end(ctx);\n        BN_CTX_free(ctx);\n    }\n    return ok;\n}\n\n/*\n * See SP800-56Ar3 Section 5.6.2.1.2: Owner assurance of Private key validity.\n * Verifies priv_key is in the range [1..upper-1]. The passed in value of upper\n * is normally params->q but can be 2^N for approved safe prime groups.\n * Note: This assumes that the domain parameters are valid.\n */\nint ossl_ffc_validate_private_key(const BIGNUM *upper, const BIGNUM *priv,\n                                  int *ret)\n{\n    int ok = 0;\n\n    *ret = 0;\n\n    if (BN_cmp(priv, BN_value_one()) < 0) {\n        *ret |= FFC_ERROR_PRIVKEY_TOO_SMALL;\n        goto err;\n    }\n    if (BN_cmp(priv, upper) >= 0) {\n        *ret |= FFC_ERROR_PRIVKEY_TOO_LARGE;\n        goto err;\n    }\n    ok = 1;\nerr:\n    return ok;\n}", "#include <linux/mount.h>\n#include <linux/seq_file.h>\n#include <linux/poll.h>\n#include <linux/ns_common.h>\n#include <linux/fs_pin.h>\n\nstruct mnt_namespace {\n\tatomic_t\t\tcount;\n\tstruct ns_common\tns;\n\tstruct mount *\troot;\n\tstruct list_head\tlist;\n\tstruct user_namespace\t*user_ns;\n\tu64\t\t\tseq;\t/* Sequence number to prevent loops */\n\twait_queue_head_t poll;\n\tu64 event;\n\tunsigned int\t\tmounts; /* # of mounts in the namespace */\n\tunsigned int\t\tpending_mounts;\n};\n\nstruct mnt_pcp {\n\tint mnt_count;\n\tint mnt_writers;\n};\n\nstruct mountpoint {\n\tstruct hlist_node m_hash;\n\tstruct dentry *m_dentry;\n\tstruct hlist_head m_list;\n\tint m_count;\n};\n\nstruct mount {\n\tstruct hlist_node mnt_hash;\n\tstruct mount *mnt_parent;\n\tstruct dentry *mnt_mountpoint;\n\tstruct vfsmount mnt;\n\tunion {\n\t\tstruct rcu_head mnt_rcu;\n\t\tstruct llist_node mnt_llist;\n\t};\n#ifdef CONFIG_SMP\n\tstruct mnt_pcp __percpu *mnt_pcp;\n#else\n\tint mnt_count;\n\tint mnt_writers;\n#endif\n\tstruct list_head mnt_mounts;\t/* list of children, anchored here */\n\tstruct list_head mnt_child;\t/* and going through their mnt_child */\n\tstruct list_head mnt_instance;\t/* mount instance on sb->s_mounts */\n\tconst char *mnt_devname;\t/* Name of device e.g. /dev/dsk/hda1 */\n\tstruct list_head mnt_list;\n\tstruct list_head mnt_expire;\t/* link in fs-specific expiry list */\n\tstruct list_head mnt_share;\t/* circular list of shared mounts */\n\tstruct list_head mnt_slave_list;/* list of slave mounts */\n\tstruct list_head mnt_slave;\t/* slave list entry */\n\tstruct mount *mnt_master;\t/* slave is on master->mnt_slave_list */\n\tstruct mnt_namespace *mnt_ns;\t/* containing namespace */\n\tstruct mountpoint *mnt_mp;\t/* where is it mounted */\n\tstruct hlist_node mnt_mp_list;\t/* list mounts with the same mountpoint */\n#ifdef CONFIG_FSNOTIFY\n\tstruct hlist_head mnt_fsnotify_marks;\n\t__u32 mnt_fsnotify_mask;\n#endif\n\tint mnt_id;\t\t\t/* mount identifier */\n\tint mnt_group_id;\t\t/* peer group identifier */\n\tint mnt_expiry_mark;\t\t/* true if marked for expiry */\n\tstruct hlist_head mnt_pins;\n\tstruct fs_pin mnt_umount;\n\tstruct dentry *mnt_ex_mountpoint;\n};\n\n#define MNT_NS_INTERNAL ERR_PTR(-EINVAL) /* distinct from any mnt_namespace */\n\nstatic inline struct mount *real_mount(struct vfsmount *mnt)\n{\n\treturn container_of(mnt, struct mount, mnt);\n}\n\nstatic inline int mnt_has_parent(struct mount *mnt)\n{\n\treturn mnt != mnt->mnt_parent;\n}\n\nstatic inline int is_mounted(struct vfsmount *mnt)\n{\n\t/* neither detached nor internal? */\n\treturn !IS_ERR_OR_NULL(real_mount(mnt)->mnt_ns);\n}\n\nextern struct mount *__lookup_mnt(struct vfsmount *, struct dentry *);\nextern struct mount *__lookup_mnt_last(struct vfsmount *, struct dentry *);\n\nextern bool legitimize_mnt(struct vfsmount *, unsigned);\n\nextern void __detach_mounts(struct dentry *dentry);\n\nstatic inline void detach_mounts(struct dentry *dentry)\n{\n\tif (!d_mountpoint(dentry))\n\t\treturn;\n\t__detach_mounts(dentry);\n}\n\nstatic inline void get_mnt_ns(struct mnt_namespace *ns)\n{\n\tatomic_inc(&ns->count);\n}\n\nextern seqlock_t mount_lock;\n\nstatic inline void lock_mount_hash(void)\n{\n\twrite_seqlock(&mount_lock);\n}\n\nstatic inline void unlock_mount_hash(void)\n{\n\twrite_sequnlock(&mount_lock);\n}\n\nstruct proc_mounts {\n\tstruct seq_file m;\n\tstruct mnt_namespace *ns;\n\tstruct path root;\n\tint (*show)(struct seq_file *, struct vfsmount *);\n\tvoid *cached_mount;\n\tu64 cached_event;\n\tloff_t cached_index;\n};\n\n#define proc_mounts(p) (container_of((p), struct proc_mounts, m))\n// ... 函数内容截断 ...\nextern bool legitimize_mnt(struct vfsmount *, unsigned);\n\nextern void __detach_mounts(struct dentry *dentry);\n\nstatic inline void detach_mounts(struct dentry *dentry)\n{\n\tif (!d_mountpoint(dentry))\n\t\treturn;\n\t__detach_mounts(dentry);\n}\n\nstatic inline void get_mnt_ns(struct mnt_namespace *ns)\n{\n\tatomic_inc(&ns->count);\n}\n\nextern seqlock_t mount_lock;\n\nstatic inline void lock_mount_hash(void)\n{\n\twrite_seqlock(&mount_lock);\n}\n\nstatic inline void unlock_mount_hash(void)\n{\n\twrite_sequnlock(&mount_lock);\n}\n\nstruct proc_mounts {\n\tstruct mnt_namespace *ns;\n\tstruct path root;\n\tint (*show)(struct seq_file *, struct vfsmount *);\n\tvoid *cached_mount;\n\tu64 cached_event;\n\tloff_t cached_index;\n};\n\nextern const struct seq_operations mounts_op;\n\nextern bool __is_local_mountpoint(struct dentry *dentry);\nstatic inline bool is_local_mountpoint(struct dentry *dentry)\n{\n\tif (!d_mountpoint(dentry))\n\t\treturn false;\n\n\treturn __is_local_mountpoint(dentry);\n}", "/*\n * Copyright (c) 2006 Oracle.  All rights reserved.\n *\n * This software is available to you under a choice of one of two\n * licenses.  You may choose to be licensed under the terms of the GNU\n * General Public License (GPL) Version 2, available from the file\n * COPYING in the main directory of this source tree, or the\n * OpenIB.org BSD license below:\n *\n *     Redistribution and use in source and binary forms, with or\n *     without modification, are permitted provided that the following\n *     conditions are met:\n *\n *      - Redistributions of source code must retain the above\n *        copyright notice, this list of conditions and the following\n *        disclaimer.\n *\n *      - Redistributions in binary form must reproduce the above\n *        copyright notice, this list of conditions and the following\n *        disclaimer in the documentation and/or other materials\n *        provided with the distribution.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n * BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n * ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n *\n */\n#include <linux/kernel.h>\n#include <linux/sysctl.h>\n#include <linux/proc_fs.h>\n\n#include \"rds.h\"\n\nstatic struct ctl_table_header *rds_sysctl_reg_table;\n\nstatic unsigned long rds_sysctl_reconnect_min = 1;\nstatic unsigned long rds_sysctl_reconnect_max = ~0UL;\n\nunsigned long rds_sysctl_reconnect_min_jiffies;\nunsigned long rds_sysctl_reconnect_max_jiffies = HZ;\n\nunsigned int  rds_sysctl_max_unacked_packets = 8;\nunsigned int  rds_sysctl_max_unacked_bytes = (16 << 20);\n\nunsigned int rds_sysctl_ping_enable = 1;\n\nstatic struct ctl_table rds_sysctl_rds_table[] = {\n\t{\n\t\t.procname       = \"reconnect_min_delay_ms\",\n\t\t.data\t\t= &rds_sysctl_reconnect_min_jiffies,\n\t\t.maxlen         = sizeof(unsigned long),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_doulongvec_ms_jiffies_minmax,\n\t\t.extra1\t\t= &rds_sysctl_reconnect_min,\n\t\t.extra2\t\t= &rds_sysctl_reconnect_max_jiffies,\n\t},\n\t{\n\t\t.procname       = \"reconnect_max_delay_ms\",\n\t\t.data\t\t= &rds_sysctl_reconnect_max_jiffies,\n\t\t.maxlen         = sizeof(unsigned long),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_doulongvec_ms_jiffies_minmax,\n\t\t.extra1\t\t= &rds_sysctl_reconnect_min_jiffies,\n\t\t.extra2\t\t= &rds_sysctl_reconnect_max,\n\t},\n\t{\n\t\t.procname\t= \"max_unacked_packets\",\n\t\t.data\t\t= &rds_sysctl_max_unacked_packets,\n\t\t.maxlen         = sizeof(unsigned long),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_dointvec,\n\t},\n\t{\n\t\t.procname\t= \"max_unacked_bytes\",\n\t\t.data\t\t= &rds_sysctl_max_unacked_bytes,\n\t\t.maxlen         = sizeof(unsigned long),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_dointvec,\n\t},\n\t{\n\t\t.procname\t= \"ping_enable\",\n\t\t.data\t\t= &rds_sysctl_ping_enable,\n\t\t.maxlen         = sizeof(int),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_dointvec,\n\t},\n\t{ }\n};\n\nvoid rds_sysctl_exit(void)\n{\n\tif (rds_sysctl_reg_table)\n\t\tunregister_net_sysctl_table(rds_sysctl_reg_table);\n}\n\nint rds_sysctl_init(void)\n// ... 函数内容截断 ...\n\t},\n\t{\n\t\t.procname       = \"reconnect_max_delay_ms\",\n\t\t.data\t\t= &rds_sysctl_reconnect_max_jiffies,\n\t\t.maxlen         = sizeof(unsigned long),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_doulongvec_ms_jiffies_minmax,\n\t\t.extra1\t\t= &rds_sysctl_reconnect_min_jiffies,\n\t\t.extra2\t\t= &rds_sysctl_reconnect_max,\n\t},\n\t{\n\t\t.procname\t= \"max_unacked_packets\",\n\t\t.data\t\t= &rds_sysctl_max_unacked_packets,\n\t\t.maxlen         = sizeof(unsigned long),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_dointvec,\n\t},\n\t{\n\t\t.procname\t= \"max_unacked_bytes\",\n\t\t.data\t\t= &rds_sysctl_max_unacked_bytes,\n\t\t.maxlen         = sizeof(unsigned long),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_dointvec,\n\t},\n\t{\n\t\t.procname\t= \"ping_enable\",\n\t\t.data\t\t= &rds_sysctl_ping_enable,\n\t\t.maxlen         = sizeof(int),\n\t\t.mode           = 0644,\n\t\t.proc_handler   = proc_dointvec,\n\t},\n\t{ }\n};\n\nvoid rds_sysctl_exit(void)\n{\n\tif (rds_sysctl_reg_table)\n\t\tunregister_net_sysctl_table(rds_sysctl_reg_table);\n}\n\nint rds_sysctl_init(void)\n{\n\trds_sysctl_reconnect_min = msecs_to_jiffies(1);\n\trds_sysctl_reconnect_min_jiffies = rds_sysctl_reconnect_min;\n\n\trds_sysctl_reg_table = register_net_sysctl(&init_net,\"net/rds\", rds_sysctl_rds_table);\n\tif (!rds_sysctl_reg_table)\n\t\treturn -ENOMEM;\n\treturn 0;\n}", "/*\n * Copyright 2006-2016 The OpenSSL Project Authors. All Rights Reserved.\n *\n * Licensed under the OpenSSL license (the \"License\").  You may not use\n * this file except in compliance with the License.  You can obtain a copy\n * in the file LICENSE in the source distribution or at\n * https://www.openssl.org/source/license.html\n */\n\n#include <stdio.h>\n#include \"internal/cryptlib.h\"\n#include <openssl/objects.h>\n#include <openssl/bn.h>\n#include <openssl/x509v3.h>\n#include <openssl/ts.h>\n#include \"ts_lcl.h\"\n\n/* Local function declarations. */\n\n/* Function definitions. */\n\nint TS_ASN1_INTEGER_print_bio(BIO *bio, const ASN1_INTEGER *num)\n{\n    BIGNUM num_bn;\n    int result = 0;\n    char *hex;\n\n    BN_init(&num_bn);\n    ASN1_INTEGER_to_BN(num, &num_bn);\n    if ((hex = BN_bn2hex(&num_bn))) {\n        result = BIO_write(bio, \"0x\", 2) > 0;\n        result = result && BIO_write(bio, hex, strlen(hex)) > 0;\n        OPENSSL_free(hex);\n    }\n    BN_free(&num_bn);\n\n    return result;\n}\n\nint TS_OBJ_print_bio(BIO *bio, const ASN1_OBJECT *obj)\n{\n    char obj_txt[128];\n\n    int len = OBJ_obj2txt(obj_txt, sizeof(obj_txt), obj, 0);\n    BIO_write(bio, obj_txt, len);\n    BIO_write(bio, \"\\n\", 1);\n\n    return 1;\n}\n\nint TS_ext_print_bio(BIO *bio, const STACK_OF(X509_EXTENSION) *extensions)\n{\n    int i, critical, n;\n    X509_EXTENSION *ex;\n    ASN1_OBJECT *obj;\n\n    BIO_printf(bio, \"Extensions:\\n\");\n    n = X509v3_get_ext_count(extensions);\n    for (i = 0; i < n; i++) {\n        ex = X509v3_get_ext(extensions, i);\n        obj = X509_EXTENSION_get_object(ex);\n        i2a_ASN1_OBJECT(bio, obj);\n        critical = X509_EXTENSION_get_critical(ex);\n        BIO_printf(bio, \": %s\\n\", critical ? \"critical\" : \"\");\n        if (!X509V3_EXT_print(bio, ex, 0, 4)) {\n            BIO_printf(bio, \"%4s\", \"\");\n            M_ASN1_OCTET_STRING_print(bio, ex->value);\n        }\n        BIO_write(bio, \"\\n\", 1);\n    }\n\n    return 1;\n}\n\nint TS_X509_ALGOR_print_bio(BIO *bio, const X509_ALGOR *alg)\n{\n    int i = OBJ_obj2nid(alg->algorithm);\n    return BIO_printf(bio, \"Hash Algorithm: %s\\n\",\n                      (i == NID_undef) ? \"UNKNOWN\" : OBJ_nid2ln(i));\n}\n\nint TS_MSG_IMPRINT_print_bio(BIO *bio, TS_MSG_IMPRINT *a)\n{\n    const ASN1_OCTET_STRING *msg;\n\n    TS_X509_ALGOR_print_bio(bio, TS_MSG_IMPRINT_get_algo(a));\n\n    BIO_printf(bio, \"Message data:\\n\");\n    msg = TS_MSG_IMPRINT_get_msg(a);\n    BIO_dump_indent(bio, (const char *)M_ASN1_STRING_data(msg),\n                    M_ASN1_STRING_length(msg), 4);\n\n    return 1;\n}", "#include <linux/mount.h>\n#include <linux/file.h>\n#include <linux/fs.h>\n#include <linux/proc_ns.h>\n#include <linux/magic.h>\n#include <linux/ktime.h>\n#include <linux/seq_file.h>\n\nstatic struct vfsmount *nsfs_mnt;\n\nstatic const struct file_operations ns_file_operations = {\n\t.llseek\t\t= no_llseek,\n};\n\nstatic char *ns_dname(struct dentry *dentry, char *buffer, int buflen)\n{\n\tstruct inode *inode = d_inode(dentry);\n\tconst struct proc_ns_operations *ns_ops = dentry->d_fsdata;\n\n\treturn dynamic_dname(dentry, buffer, buflen, \"%s:[%lu]\",\n\t\tns_ops->name, inode->i_ino);\n}\n\nstatic void ns_prune_dentry(struct dentry *dentry)\n{\n\tstruct inode *inode = d_inode(dentry);\n\tif (inode) {\n\t\tstruct ns_common *ns = inode->i_private;\n\t\tatomic_long_set(&ns->stashed, 0);\n\t}\n}\n\nconst struct dentry_operations ns_dentry_operations =\n{\n\t.d_prune\t= ns_prune_dentry,\n\t.d_delete\t= always_delete_dentry,\n\t.d_dname\t= ns_dname,\n};\n\nstatic void nsfs_evict(struct inode *inode)\n{\n\tstruct ns_common *ns = inode->i_private;\n\tclear_inode(inode);\n\tns->ops->put(ns);\n}\n\nvoid *ns_get_path(struct path *path, struct task_struct *task,\n\t\t\tconst struct proc_ns_operations *ns_ops)\n{\n\tstruct vfsmount *mnt = mntget(nsfs_mnt);\n\tstruct qstr qname = { .name = \"\", };\n\tstruct dentry *dentry;\n\tstruct inode *inode;\n\tstruct ns_common *ns;\n\tunsigned long d;\n\nagain:\n\tns = ns_ops->get(task);\n\tif (!ns) {\n\t\tmntput(mnt);\n\t\treturn ERR_PTR(-ENOENT);\n\t}\n\trcu_read_lock();\n\td = atomic_long_read(&ns->stashed);\n\tif (!d)\n\t\tgoto slow;\n\tdentry = (struct dentry *)d;\n\tif (!lockref_get_not_dead(&dentry->d_lockref))\n\t\tgoto slow;\n\trcu_read_unlock();\n\tns_ops->put(ns);\ngot_it:\n\tpath->mnt = mnt;\n\tpath->dentry = dentry;\n\treturn NULL;\nslow:\n\trcu_read_unlock();\n\tinode = new_inode_pseudo(mnt->mnt_sb);\n\tif (!inode) {\n\t\tns_ops->put(ns);\n\t\tmntput(mnt);\n\t\treturn ERR_PTR(-ENOMEM);\n\t}\n\tinode->i_ino = ns->inum;\n\tinode->i_mtime = inode->i_atime = inode->i_ctime = CURRENT_TIME;\n\tinode->i_flags |= S_IMMUTABLE;\n\tinode->i_mode = S_IFREG | S_IRUGO;\n\tinode->i_fop = &ns_file_operations;\n\tinode->i_private = ns;\n\n\tdentry = d_alloc_pseudo(mnt->mnt_sb, &qname);\n\tif (!dentry) {\n\t\tiput(inode);\n\t\tmntput(mnt);\n\t\treturn ERR_PTR(-ENOMEM);\n\t}\n\td_instantiate(dentry, inode);\n\tdentry->d_flags |= DCACHE_RCUACCESS;\n\tdentry->d_fsdata = (void *)ns_ops;\n\td = atomic_long_cmpxchg(&ns->stashed, 0, (unsigned long)dentry);\n\tif (d) {\n\t\td_delete(dentry);\t/* make sure ->d_prune() does nothing */\n\t\tdput(dentry);\n\t\tcpu_relax();\n\t\tgoto again;\n\t}\n\tgoto got_it;\n}\n\nint ns_get_name(char *buf, size_t size, struct task_struct *task,\n\t\t\tconst struct proc_ns_operations *ns_ops)\n{\n\tstruct ns_common *ns;\n\tint res = -ENOENT;\n\tns = ns_ops->get(task);\n\tif (ns) {\n\t\tres = snprintf(buf, size, \"%s:[%u]\", ns_ops->name, ns->inum);\n\t\tns_ops->put(ns);\n\t}\n\treturn res;\n}\n\nstruct file *proc_ns_fget(int fd)\n{\n\tstruct file *file;\n\n\tfile = fget(fd);\n\tif (!file)\n\t\treturn ERR_PTR(-EBADF);\n\n\tif (file->f_op != &ns_file_operations)\n\t\tgoto out_invalid;\n\n\treturn file;\n\nout_invalid:\n\tfput(file);\n\treturn ERR_PTR(-EINVAL);\n}\n\nstatic int nsfs_show_path(struct seq_file *seq, struct dentry *dentry)\n{\n\tstruct inode *inode = d_inode(dentry);\n\tconst struct proc_ns_operations *ns_ops = dentry->d_fsdata;\n\n\tseq_printf(seq, \"%s:[%lu]\", ns_ops->name, inode->i_ino);\n\treturn 0;\n}\n\nstatic const struct super_operations nsfs_ops = {\n\t.statfs = simple_statfs,\n\t.evict_inode = nsfs_evict,\n\t.show_path = nsfs_show_path,\n};\nstatic struct dentry *nsfs_mount(struct file_system_type *fs_type,\n\t\t\tint flags, const char *dev_name, void *data)\n{\n\treturn mount_pseudo(fs_type, \"nsfs:\", &nsfs_ops,\n\t\t\t&ns_dentry_operations, NSFS_MAGIC);\n}\nstatic struct file_system_type nsfs = {\n\t.name = \"nsfs\",\n\t.mount = nsfs_mount,\n\t.kill_sb = kill_anon_super,\n};\n\nvoid __init nsfs_init(void)\n{\n\tnsfs_mnt = kern_mount(&nsfs);\n\tif (IS_ERR(nsfs_mnt))\n\t\tpanic(\"can't set nsfs up\\n\");\n\tnsfs_mnt->mnt_sb->s_flags &= ~MS_NOUSER;\n}", "/*\n * symlink.c\n *\n * PURPOSE\n *\tSymlink handling routines for the OSTA-UDF(tm) filesystem.\n *\n * COPYRIGHT\n *\tThis file is distributed under the terms of the GNU General Public\n *\tLicense (GPL). Copies of the GPL can be obtained from:\n *\t\tftp://prep.ai.mit.edu/pub/gnu/GPL\n *\tEach contributing author retains all rights to their own work.\n *\n *  (C) 1998-2001 <NAME>\n *  (C) 1999 Stelias Computing Inc\n *\n * HISTORY\n *\n *  04/16/99 blf  Created.\n *\n */\n\n#include \"udfdecl.h\"\n#include <linux/uaccess.h>\n#include <linux/errno.h>\n#include <linux/fs.h>\n#include <linux/time.h>\n#include <linux/mm.h>\n#include <linux/stat.h>\n#include <linux/pagemap.h>\n#include <linux/buffer_head.h>\n#include \"udf_i.h\"\n\nstatic int udf_pc_to_char(struct super_block *sb, unsigned char *from,\n\t\t\t  int fromlen, unsigned char *to, int tolen)\n{\n\tstruct pathComponent *pc;\n\tint elen = 0;\n\tint comp_len;\n\tunsigned char *p = to;\n\n\t/* Reserve one byte for terminating \\0 */\n\ttolen--;\n\twhile (elen < fromlen) {\n\t\tpc = (struct pathComponent *)(from + elen);\n\t\telen += sizeof(struct pathComponent);\n\t\tswitch (pc->componentType) {\n\t\tcase 1:\n\t\t\t/*\n\t\t\t * Symlink points to some place which should be agreed\n \t\t\t * upon between originator and receiver of the media. Ignore.\n\t\t\t */\n\t\t\tif (pc->lengthComponentIdent > 0) {\n\t\t\t\telen += pc->lengthComponentIdent;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\t/* Fall through */\n\t\tcase 2:\n\t\t\tif (tolen == 0)\n\t\t\t\treturn -ENAMETOOLONG;\n\t\t\tp = to;\n\t\t\t*p++ = '/';\n\t\t\ttolen--;\n\t\t\tbreak;\n\t\tcase 3:\n\t\t\tif (tolen < 3)\n\t\t\t\treturn -ENAMETOOLONG;\n\t\t\tmemcpy(p, \"../\", 3);\n\t\t\tp += 3;\n\t\t\ttolen -= 3;\n\t\t\tbreak;\n\t\tcase 4:\n\t\t\tif (tolen < 2)\n\t\t\t\treturn -ENAMETOOLONG;\n\t\t\tmemcpy(p, \"./\", 2);\n\t\t\tp += 2;\n\t\t\ttolen -= 2;\n\t\t\t/* that would be . - just ignore */\n\t\t\tbreak;\n\t\tcase 5:\n\t\t\telen += pc->lengthComponentIdent;\n\t\t\tif (elen > fromlen)\n\t\t\t\treturn -EIO;\n\t\t\tcomp_len = udf_get_filename(sb, pc->componentIdent,\n\t\t\t\t\t\t    pc->lengthComponentIdent,\n\t\t\t\t\t\t    p, tolen);\n\t\t\tp += comp_len;\n\t\t\ttolen -= comp_len;\n\t\t\tif (tolen == 0)\n\t\t\t\treturn -ENAMETOOLONG;\n\t\t\t*p++ = '/';\n\t\t\ttolen--;\n\t\t\tbreak;\n\t\t}\n\t}\n\tif (p > to + 1)\n\t\tp[-1] = '\\0';\n\telse\n\t\tp[0] = '\\0';\n\treturn 0;\n}\n\nstatic int udf_symlink_filler(struct file *file, struct page *page)\n{\n\tstruct inode *inode = page->mapping->host;\n\tstruct buffer_head *bh = NULL;\n\tunsigned char *symlink;\n\tint err;\n\tunsigned char *p = kmap(page);\n\tstruct udf_inode_info *iinfo;\n\tuint32_t pos;\n\n\t/* We don't support symlinks longer than one block */\n\tif (inode->i_size > inode->i_sb->s_blocksize) {\n\t\terr = -ENAMETOOLONG;\n\t\tgoto out_unmap;\n\t}\n\n\tiinfo = UDF_I(inode);\n\tpos = udf_block_map(inode, 0);\n\n\tdown_read(&iinfo->i_data_sem);\n\tif (iinfo->i_alloc_type == ICBTAG_FLAG_AD_IN_ICB) {\n\t\tsymlink = iinfo->i_ext.i_data + iinfo->i_lenEAttr;\n\t} else {\n\t\tbh = sb_bread(inode->i_sb, pos);\n\n\t\tif (!bh) {\n\t\t\terr = -EIO;\n\t\t\tgoto out_unlock_inode;\n\t\t}\n\n\t\tsymlink = bh->b_data;\n\t}\n\n\terr = udf_pc_to_char(inode->i_sb, symlink, inode->i_size, p, PAGE_SIZE);\n\tbrelse(bh);\n\tif (err)\n\t\tgoto out_unlock_inode;\n\n\tup_read(&iinfo->i_data_sem);\n\tSetPageUptodate(page);\n\tkunmap(page);\n\tunlock_page(page);\n\treturn 0;\n\nout_unlock_inode:\n\tup_read(&iinfo->i_data_sem);\n\tSetPageError(page);\nout_unmap:\n\tkunmap(page);\n\tunlock_page(page);\n\treturn err;\n}\n\n/*\n * symlinks can't do much...\n */\nconst struct address_space_operations udf_symlink_aops = {\n\t.readpage\t\t= udf_symlink_filler,\n};", "#ifndef _LINUX_USER_NAMESPACE_H\n#define _LINUX_USER_NAMESPACE_H\n\n#include <linux/kref.h>\n#include <linux/nsproxy.h>\n#include <linux/ns_common.h>\n#include <linux/sched.h>\n#include <linux/err.h>\n\n#define UID_GID_MAP_MAX_EXTENTS 5\n\nstruct uid_gid_map {\t/* 64 bytes -- 1 cache line */\n\tu32 nr_extents;\n\tstruct uid_gid_extent {\n\t\tu32 first;\n\t\tu32 lower_first;\n\t\tu32 count;\n\t} extent[UID_GID_MAP_MAX_EXTENTS];\n};\n\n#define USERNS_SETGROUPS_ALLOWED 1UL\n\n#define USERNS_INIT_FLAGS USERNS_SETGROUPS_ALLOWED\n\nstruct ucounts;\n\nenum ucount_type {\n\tUCOUNT_USER_NAMESPACES,\n\tUCOUNT_PID_NAMESPACES,\n\tUCOUNT_UTS_NAMESPACES,\n\tUCOUNT_IPC_NAMESPACES,\n\tUCOUNT_NET_NAMESPACES,\n\tUCOUNT_MNT_NAMESPACES,\n\tUCOUNT_CGROUP_NAMESPACES,\n\tUCOUNT_COUNTS,\n};\n\nstruct user_namespace {\n\tstruct uid_gid_map\tuid_map;\n\tstruct uid_gid_map\tgid_map;\n\tstruct uid_gid_map\tprojid_map;\n\tatomic_t\t\tcount;\n\tstruct user_namespace\t*parent;\n\tint\t\t\tlevel;\n\tkuid_t\t\t\towner;\n\tkgid_t\t\t\tgroup;\n\tstruct ns_common\tns;\n\tunsigned long\t\tflags;\n\n\t/* Register of per-UID persistent keyrings for this namespace */\n#ifdef CONFIG_PERSISTENT_KEYRINGS\n\tstruct key\t\t*persistent_keyring_register;\n\tstruct rw_semaphore\tpersistent_keyring_register_sem;\n#endif\n\tstruct work_struct\twork;\n#ifdef CONFIG_SYSCTL\n\tstruct ctl_table_set\tset;\n\tstruct ctl_table_header *sysctls;\n#endif\n\tstruct ucounts\t\t*ucounts;\n\tint ucount_max[UCOUNT_COUNTS];\n};\n\nstruct ucounts {\n\tstruct hlist_node node;\n\tstruct user_namespace *ns;\n\tkuid_t uid;\n\tint count;\n\tatomic_t ucount[UCOUNT_COUNTS];\n};\n\nextern struct user_namespace init_user_ns;\n\nbool setup_userns_sysctls(struct user_namespace *ns);\nvoid retire_userns_sysctls(struct user_namespace *ns);\nstruct ucounts *inc_ucount(struct user_namespace *ns, kuid_t uid, enum ucount_type type);\nvoid dec_ucount(struct ucounts *ucounts, enum ucount_type type);\n\n#ifdef CONFIG_USER_NS\n\nstatic inline struct user_namespace *get_user_ns(struct user_namespace *ns)\n{\n\tif (ns)\n\t\tatomic_inc(&ns->count);\n\treturn ns;\n}\n\nextern int create_user_ns(struct cred *new);\nextern int unshare_userns(unsigned long unshare_flags, struct cred **new_cred);\nextern void __put_user_ns(struct user_namespace *ns);\n\nstatic inline void put_user_ns(struct user_namespace *ns)\n{\n\tif (ns && atomic_dec_and_test(&ns->count))\n\t\t__put_user_ns(ns);\n}\n\nstruct seq_operations;\nextern const struct seq_operations proc_uid_seq_operations;\nextern const struct seq_operations proc_gid_seq_operations;\nextern const struct seq_operations proc_projid_seq_operations;\nextern ssize_t proc_uid_map_write(struct file *, const char __user *, size_t, loff_t *);\nextern ssize_t proc_gid_map_write(struct file *, const char __user *, size_t, loff_t *);\nextern ssize_t proc_projid_map_write(struct file *, const char __user *, size_t, loff_t *);\nextern ssize_t proc_setgroups_write(struct file *, const char __user *, size_t, loff_t *);\nextern int proc_setgroups_show(struct seq_file *m, void *v);\nextern bool userns_may_setgroups(const struct user_namespace *ns);\nextern bool current_in_userns(const struct user_namespace *target_ns);\n\nstruct ns_common *ns_get_owner(struct ns_common *ns);\n#else\n\nstatic inline struct user_namespace *get_user_ns(struct user_namespace *ns)\n{\n\treturn &init_user_ns;\n}\n\nstatic inline int create_user_ns(struct cred *new)\n{\n\treturn -EINVAL;\n}\n\nstatic inline int unshare_userns(unsigned long unshare_flags,\n\t\t\t\t struct cred **new_cred)\n{\n\tif (unshare_flags & CLONE_NEWUSER)\n\t\treturn -EINVAL;\n\treturn 0;\n}\n\nstatic inline void put_user_ns(struct user_namespace *ns)\n{\n}\n\nstatic inline bool userns_may_setgroups(const struct user_namespace *ns)\n{\n\treturn true;\n}\n\nstatic inline bool current_in_userns(const struct user_namespace *target_ns)\n{\n\treturn true;\n}\n\nstatic inline struct ns_common *ns_get_owner(struct ns_common *ns)\n{\n\treturn ERR_PTR(-EPERM);\n}\n#endif\n\n#endif /* _LINUX_USER_H */", "/*\n * Fushicai USBTV007 Audio-Video Grabber Driver\n *\n * Product web site:\n * http://www.fushicai.com/products_detail/&productId=d05449ee-b690-42f9-a661-aa7353894bed.html\n *\n * Following LWN articles were very useful in construction of this driver:\n * Video4Linux2 API series: http://lwn.net/Articles/203924/\n * videobuf2 API explanation: http://lwn.net/Articles/447435/\n * Thanks go to <NAME> for providing this quality documentation.\n * He is awesome.\n *\n * Copyright (c) 2013 Lubomir Rintel\n * All rights reserved.\n * No physical hardware was harmed running Windows during the\n * reverse-engineering activity\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n * 1. Redistributions of source code must retain the above copyright\n *    notice, this list of conditions, and the following disclaimer,\n *    without modification.\n * 2. The name of the author may not be used to endorse or promote products\n *    derived from this software without specific prior written permission.\n *\n * Alternatively, this software may be distributed under the terms of the\n * GNU General Public License (\"GPL\").\n */\n\n#include \"usbtv.h\"\n\nint usbtv_set_regs(struct usbtv *usbtv, const u16 regs[][2], int size)\n{\n\tint ret;\n\tint pipe = usb_rcvctrlpipe(usbtv->udev, 0);\n\tint i;\n\n\tfor (i = 0; i < size; i++) {\n\t\tu16 index = regs[i][0];\n\t\tu16 value = regs[i][1];\n\n\t\tret = usb_control_msg(usbtv->udev, pipe, USBTV_REQUEST_REG,\n\t\t\tUSB_DIR_OUT | USB_TYPE_VENDOR | USB_RECIP_DEVICE,\n\t\t\tvalue, index, NULL, 0, 0);\n\t\tif (ret < 0)\n\t\t\treturn ret;\n\t}\n\n\treturn 0;\n}\n\nstatic int usbtv_probe(struct usb_interface *intf,\n\tconst struct usb_device_id *id)\n{\n\tint ret;\n\tint size;\n\tstruct device *dev = &intf->dev;\n\tstruct usbtv *usbtv;\n\n\t/* Checks that the device is what we think it is. */\n\tif (intf->num_altsetting != 2)\n\t\treturn -ENODEV;\n\tif (intf->altsetting[1].desc.bNumEndpoints != 4)\n\t\treturn -ENODEV;\n\n\t/* Packet size is split into 11 bits of base size and count of\n\t * extra multiplies of it.*/\n\tsize = usb_endpoint_maxp(&intf->altsetting[1].endpoint[0].desc);\n\tsize = (size & 0x07ff) * (((size & 0x1800) >> 11) + 1);\n\n\t/* Device structure */\n\tusbtv = kzalloc(sizeof(struct usbtv), GFP_KERNEL);\n\tif (usbtv == NULL)\n\t\treturn -ENOMEM;\n\tusbtv->dev = dev;\n\tusbtv->udev = usb_get_dev(interface_to_usbdev(intf));\n\n\tusbtv->iso_size = size;\n\n\tusb_set_intfdata(intf, usbtv);\n\n\tret = usbtv_video_init(usbtv);\n\tif (ret < 0)\n\t\tgoto usbtv_video_fail;\n\n\tret = usbtv_audio_init(usbtv);\n\tif (ret < 0)\n\t\tgoto usbtv_audio_fail;\n\n\t/* for simplicity we exploit the v4l2_device reference counting */\n\tv4l2_device_get(&usbtv->v4l2_dev);\n\n\tdev_info(dev, \"Fushicai USBTV007 Audio-Video Grabber\\n\");\n\treturn 0;\n\nusbtv_audio_fail:\n\t/* we must not free at this point */\n\tusb_get_dev(usbtv->udev);\n\tusbtv_video_free(usbtv);\n\nusbtv_video_fail:\n\tusb_set_intfdata(intf, NULL);\n\tusb_put_dev(usbtv->udev);\n\tkfree(usbtv);\n\n\treturn ret;\n}\n\nstatic void usbtv_disconnect(struct usb_interface *intf)\n{\n\tstruct usbtv *usbtv = usb_get_intfdata(intf);\n\n\tusb_set_intfdata(intf, NULL);\n\n\tif (!usbtv)\n\t\treturn;\n\n\tusbtv_audio_free(usbtv);\n\tusbtv_video_free(usbtv);\n\n\tusb_put_dev(usbtv->udev);\n\tusbtv->udev = NULL;\n\n\t/* the usbtv structure will be deallocated when v4l2 will be\n\t   done using it */\n\tv4l2_device_put(&usbtv->v4l2_dev);\n}\n\nstatic struct usb_device_id usbtv_id_table[] = {\n\t{ USB_DEVICE(0x1b71, 0x3002) },\n\t{ USB_DEVICE(0x1f71, 0x3301) },\n\t{}\n};\nMODULE_DEVICE_TABLE(usb, usbtv_id_table);\n\nMODULE_AUTHOR(\"<NAME>, <NAME>\");\n// ... 函数内容截断 ...\nusbtv_audio_fail:\n\tusbtv_video_free(usbtv);\n\nusbtv_video_fail:\n\tusb_set_intfdata(intf, NULL);\n\tusb_put_dev(usbtv->udev);\n\tkfree(usbtv);\n\n\treturn ret;\n}\n\nstatic void usbtv_disconnect(struct usb_interface *intf)\n{\n\tstruct usbtv *usbtv = usb_get_intfdata(intf);\n\n\tusb_set_intfdata(intf, NULL);\n\n\tif (!usbtv)\n\t\treturn;\n\n\tusbtv_audio_free(usbtv);\n\tusbtv_video_free(usbtv);\n\n\tusb_put_dev(usbtv->udev);\n\tusbtv->udev = NULL;\n\n\t/* the usbtv structure will be deallocated when v4l2 will be\n\t   done using it */\n\tv4l2_device_put(&usbtv->v4l2_dev);\n}\n\nstatic struct usb_driver usbtv_usb_driver = {\n\t.name = \"usbtv\",\n\t.id_table = usbtv_id_table,\n\t.probe = usbtv_probe,\n\t.disconnect = usbtv_disconnect,\n};\n\nmodule_usb_driver(usbtv_usb_driver);", "/*\n *  HID driver for some cypress \"special\" devices\n *\n *  Copyright (c) 1999 <PERSON>\n *  Copyright (c) 2000-2005 Voj<PERSON> <<EMAIL>>\n *  Copyright (c) 2005 <PERSON> <<EMAIL>> for Concept2, Inc\n *  Copyright (c) 2006-2007 <PERSON><PERSON>\n *  Copyright (c) 2008 <PERSON><PERSON>\n */\n\n/*\n * This program is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License as published by the Free\n * Software Foundation; either version 2 of the License, or (at your option)\n * any later version.\n */\n\n#include <linux/device.h>\n#include <linux/hid.h>\n#include <linux/input.h>\n#include <linux/module.h>\n\n#include \"hid-ids.h\"\n\n#define CP_RDESC_SWAPPED_MIN_MAX\t0x01\n#define CP_2WHEEL_MOUSE_HACK\t\t0x02\n#define CP_2WHEEL_MOUSE_HACK_ON\t\t0x04\n\n/*\n * Some USB barcode readers from cypress have usage min and usage max in\n * the wrong order\n */\nstatic __u8 *cp_report_fixup(struct hid_device *hdev, __u8 *rdesc,\n\t\tunsigned int *rsize)\n{\n\tunsigned long quirks = (unsigned long)hid_get_drvdata(hdev);\n\tunsigned int i;\n\n\tif (!(quirks & CP_RDESC_SWAPPED_MIN_MAX))\n\t\treturn rdesc;\n\n\tif (*rsize < 4)\n\t\treturn rdesc;\n\n\tfor (i = 0; i < *rsize - 4; i++)\n\t\tif (rdesc[i] == 0x29 && rdesc[i + 2] == 0x19) {\n\t\t\trdesc[i] = 0x19;\n\t\t\trdesc[i + 2] = 0x29;\n\t\t\tswap(rdesc[i + 3], rdesc[i + 1]);\n\t\t}\n\treturn rdesc;\n}\n\nstatic int cp_input_mapped(struct hid_device *hdev, struct hid_input *hi,\n\t\tstruct hid_field *field, struct hid_usage *usage,\n\t\tunsigned long **bit, int *max)\n{\n\tunsigned long quirks = (unsigned long)hid_get_drvdata(hdev);\n\n\tif (!(quirks & CP_2WHEEL_MOUSE_HACK))\n\t\treturn 0;\n\n\tif (usage->type == EV_REL && usage->code == REL_WHEEL)\n\t\tset_bit(REL_HWHEEL, *bit);\n\tif (usage->hid == 0x00090005)\n\t\treturn -1;\n\n\treturn 0;\n}\n\nstatic int cp_event(struct hid_device *hdev, struct hid_field *field,\n\t\tstruct hid_usage *usage, __s32 value)\n{\n\tunsigned long quirks = (unsigned long)hid_get_drvdata(hdev);\n\n\tif (!(hdev->claimed & HID_CLAIMED_INPUT) || !field->hidinput ||\n\t\t\t!usage->type || !(quirks & CP_2WHEEL_MOUSE_HACK))\n\t\treturn 0;\n\n\tif (usage->hid == 0x00090005) {\n\t\tif (value)\n\t\t\tquirks |=  CP_2WHEEL_MOUSE_HACK_ON;\n\t\telse\n\t\t\tquirks &= ~CP_2WHEEL_MOUSE_HACK_ON;\n\t\thid_set_drvdata(hdev, (void *)quirks);\n\t\treturn 1;\n\t}\n\n\tif (usage->code == REL_WHEEL && (quirks & CP_2WHEEL_MOUSE_HACK_ON)) {\n\t\tstruct input_dev *input = field->hidinput->input;\n\n\t\tinput_event(input, usage->type, REL_HWHEEL, value);\n\t\treturn 1;\n\t}\n\n\treturn 0;\n}\n\nstatic int cp_probe(struct hid_device *hdev, const struct hid_device_id *id)\n{\n\tunsigned long quirks = id->driver_data;\n\tint ret;\n\n\thid_set_drvdata(hdev, (void *)quirks);\n\n\tret = hid_parse(hdev);\n\tif (ret) {\n\t\thid_err(hdev, \"parse failed\\n\");\n\t\tgoto err_free;\n\t}\n\n\tret = hid_hw_start(hdev, HID_CONNECT_DEFAULT);\n\tif (ret) {\n\t\thid_err(hdev, \"hw start failed\\n\");\n\t\tgoto err_free;\n\t}\n\n\treturn 0;\nerr_free:\n\treturn ret;\n}\n\nstatic const struct hid_device_id cp_devices[] = {\n\t{ HID_USB_DEVICE(USB_VENDOR_ID_CYPRESS, USB_DEVICE_ID_CYPRESS_BARCODE_1),\n\t\t.driver_data = CP_RDESC_SWAPPED_MIN_MAX },\n\t{ HID_USB_DEVICE(USB_VENDOR_ID_CYPRESS, USB_DEVICE_ID_CYPRESS_BARCODE_2),\n\t\t.driver_data = CP_RDESC_SWAPPED_MIN_MAX },\n\t{ HID_USB_DEVICE(USB_VENDOR_ID_CYPRESS, USB_DEVICE_ID_CYPRESS_BARCODE_3),\n\t\t.driver_data = CP_RDESC_SWAPPED_MIN_MAX },\n\t{ HID_USB_DEVICE(USB_VENDOR_ID_CYPRESS, USB_DEVICE_ID_CYPRESS_BARCODE_4),\n\t\t.driver_data = CP_RDESC_SWAPPED_MIN_MAX },\n\t{ HID_USB_DEVICE(USB_VENDOR_ID_CYPRESS, USB_DEVICE_ID_CYPRESS_MOUSE),\n\t\t.driver_data = CP_2WHEEL_MOUSE_HACK },\n\t{ }\n};\nMODULE_DEVICE_TABLE(hid, cp_devices);\n\nstatic struct hid_driver cp_driver = {\n\t.name = \"cypress\",\n\t.id_table = cp_devices,\n\t.report_fixup = cp_report_fixup,\n\t.input_mapped = cp_input_mapped,\n\t.event = cp_event,\n\t.probe = cp_probe,\n};\nmodule_hid_driver(cp_driver);\n\nMODULE_LICENSE(\"GPL\");", "// SPDX-License-Identifier: GPL-2.0+\n/*\n * file.c - NILFS regular file handling primitives including fsync().\n *\n * Copyright (C) 2005-2008 Nippon Telegraph and Telephone Corporation.\n *\n * Written by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n */\n\n#include <linux/fs.h>\n#include <linux/mm.h>\n#include <linux/writeback.h>\n#include \"nilfs.h\"\n#include \"segment.h\"\n\nint nilfs_sync_file(struct file *file, loff_t start, loff_t end, int datasync)\n{\n\t/*\n\t * Called from fsync() system call\n\t * This is the only entry point that can catch write and synch\n\t * timing for both data blocks and intermediate blocks.\n\t *\n\t * This function should be implemented when the writeback function\n\t * will be implemented.\n\t */\n\tstruct the_nilfs *nilfs;\n\tstruct inode *inode = file->f_mapping->host;\n\tint err = 0;\n\n\tif (nilfs_inode_dirty(inode)) {\n\t\tif (datasync)\n\t\t\terr = nilfs_construct_dsync_segment(inode->i_sb, inode,\n\t\t\t\t\t\t\t    start, end);\n\t\telse\n\t\t\terr = nilfs_construct_segment(inode->i_sb);\n\t}\n\n\tnilfs = inode->i_sb->s_fs_info;\n\tif (!err)\n\t\terr = nilfs_flush_device(nilfs);\n\n\treturn err;\n}\n\nstatic vm_fault_t nilfs_page_mkwrite(struct vm_fault *vmf)\n{\n\tstruct vm_area_struct *vma = vmf->vma;\n\tstruct page *page = vmf->page;\n\tstruct inode *inode = file_inode(vma->vm_file);\n\tstruct nilfs_transaction_info ti;\n\tint ret = 0;\n\n\tif (unlikely(nilfs_near_disk_full(inode->i_sb->s_fs_info)))\n\t\treturn VM_FAULT_SIGBUS; /* -ENOSPC */\n\n\tsb_start_pagefault(inode->i_sb);\n\tlock_page(page);\n\tif (page->mapping != inode->i_mapping ||\n\t    page_offset(page) >= i_size_read(inode) || !PageUptodate(page)) {\n\t\tunlock_page(page);\n\t\tret = -EFAULT;\t/* make the VM retry the fault */\n\t\tgoto out;\n\t}\n\n\t/*\n\t * check to see if the page is mapped already (no holes)\n\t */\n\tif (PageMappedToDisk(page))\n\t\tgoto mapped;\n\n\tif (page_has_buffers(page)) {\n\t\tstruct buffer_head *bh, *head;\n\t\tint fully_mapped = 1;\n\n\t\tbh = head = page_buffers(page);\n\t\tdo {\n\t\t\tif (!buffer_mapped(bh)) {\n\t\t\t\tfully_mapped = 0;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (bh = bh->b_this_page, bh != head);\n\n\t\tif (fully_mapped) {\n\t\t\tSetPageMappedToDisk(page);\n\t\t\tgoto mapped;\n\t\t}\n\t}\n\tunlock_page(page);\n\n\t/*\n\t * fill hole blocks\n\t */\n\tret = nilfs_transaction_begin(inode->i_sb, &ti, 1);\n\t/* never returns -ENOMEM, but may return -ENOSPC */\n\tif (unlikely(ret))\n\t\tgoto out;\n\n\tfile_update_time(vma->vm_file);\n\tret = block_page_mkwrite(vma, vmf, nilfs_get_block);\n\tif (ret) {\n\t\tnilfs_transaction_abort(inode->i_sb);\n\t\tgoto out;\n\t}\n\tnilfs_set_file_dirty(inode, 1 << (PAGE_SHIFT - inode->i_blkbits));\n\tnilfs_transaction_commit(inode->i_sb);\n\n mapped:\n\t/*\n\t * Since checksumming including data blocks is performed to determine\n\t * the validity of the log to be written and used for recovery, it is\n\t * necessary to wait for writeback to finish here, regardless of the\n\t * stable write requirement of the backing device.\n\t */\n\twait_on_page_writeback(page);\n out:\n\tsb_end_pagefault(inode->i_sb);\n\treturn block_page_mkwrite_return(ret);\n}\n\nstatic const struct vm_operations_struct nilfs_file_vm_ops = {\n\t.fault\t\t= filemap_fault,\n\t.map_pages\t= filemap_map_pages,\n\t.page_mkwrite\t= nilfs_page_mkwrite,\n};\n\nstatic int nilfs_file_mmap(struct file *file, struct vm_area_struct *vma)\n{\n\tfile_accessed(file);\n\tvma->vm_ops = &nilfs_file_vm_ops;\n\treturn 0;\n}\n\n/*\n * We have mostly NULL's here: the current defaults are ok for\n * the nilfs filesystem.\n */\nconst struct file_operations nilfs_file_operations = {\n\t.llseek\t\t= generic_file_llseek,\n\t.read_iter\t= generic_file_read_iter,\n\t.write_iter\t= generic_file_write_iter,\n\t.unlocked_ioctl\t= nilfs_ioctl,\n#ifdef CONFIG_COMPAT\n\t.compat_ioctl\t= nilfs_compat_ioctl,\n#endif\t/* CONFIG_COMPAT */\n\t.mmap\t\t= nilfs_file_mmap,\n\t.open\t\t= generic_file_open,\n\t/* .release\t= nilfs_release_file, */\n\t.fsync\t\t= nilfs_sync_file,\n\t.splice_read\t= filemap_splice_read,\n\t.splice_write   = iter_file_splice_write,\n};\n\nconst struct inode_operations nilfs_file_inode_operations = {\n\t.setattr\t= nilfs_setattr,\n\t.permission     = nilfs_permission,\n\t.fiemap\t\t= nilfs_fiemap,\n\t.fileattr_get\t= nilfs_fileattr_get,\n\t.fileattr_set\t= nilfs_fileattr_set,\n};\n\n/* end of file */", "/*\n * QLogic iSCSI Offload Driver\n * Copyright (c) 2016 Cavium Inc.\n *\n// ... 函数内容截断 ...\nvoid\nqedi_dbg_info(struct qedi_dbg_ctx *qedi, const char *func, u32 line,\n\t      u32 level, const char *fmt, ...)\n{\n\tva_list va;\n\tstruct va_format vaf;\n\n\tva_start(va, fmt);\n\n\tvaf.fmt = fmt;\n\tvaf.va = &va;\n\n\tif (!(qedi_dbg_log & level))\n\t\tgoto ret;\n\n\tif (likely(qedi) && likely(qedi->pdev))\n\t\tpr_info(\"[%s]:[%s:%d]:%d: %pV\", dev_name(&qedi->pdev->dev),\n\t\t\tfunc, line, qedi->host_no, &vaf);\n\telse\n\t\tpr_info(\"[0000:00:00.0]:[%s:%d]: %pV\", func, line, &vaf);\n\nret:\n\tva_end(va);\n}\n\nint\nqedi_create_sysfs_attr(struct Scsi_Host *shost, struct sysfs_bin_attrs *iter)\n{\n\tint ret = 0;\n\n\tfor (; iter->name; iter++) {\n\t\tret = sysfs_create_bin_file(&shost->shost_gendev.kobj,\n\t\t\t\t\t    iter->attr);\n\t\tif (ret)\n\t\t\tpr_err(\"Unable to create sysfs %s attr, err(%d).\\n\",\n\t\t\t       iter->name, ret);\n\t}\n\treturn ret;\n}\n\nvoid\nqedi_remove_sysfs_attr(struct Scsi_Host *shost, struct sysfs_bin_attrs *iter)\n{\n\tfor (; iter->name; iter++)\n\t\tsysfs_remove_bin_file(&shost->shost_gendev.kobj, iter->attr);\n}", "", "/* SPDX-License-Identifier: GPL-2.0 */\n#ifndef _linux_POSIX_TIMERS_H\n#define _linux_POSIX_TIMERS_H\n\n#include <linux/spinlock.h>\n#include <linux/list.h>\n#include <linux/sched.h>\n#include <linux/timex.h>\n#include <linux/alarmtimer.h>\n\nstruct siginfo;\n\nstruct cpu_timer_list {\n\tstruct list_head entry;\n\tu64 expires, incr;\n\tstruct task_struct *task;\n\tint firing;\n};\n\n/*\n * Bit fields within a clockid:\n *\n * The most significant 29 bits hold either a pid or a file descriptor.\n *\n * Bit 2 indicates whether a cpu clock refers to a thread or a process.\n *\n * Bits 1 and 0 give the type: PROF=0, VIRT=1, SCHED=2, or FD=3.\n *\n * A clockid is invalid if bits 2, 1, and 0 are all set.\n */\n#define CPUCLOCK_PID(clock)\t\t((pid_t) ~((clock) >> 3))\n#define CPUCLOCK_PERTHREAD(clock) \\\n\t(((clock) & (clockid_t) CPUCLOCK_PERTHREAD_MASK) != 0)\n\n#define CPUCLOCK_PERTHREAD_MASK\t4\n#define CPUCLOCK_WHICH(clock)\t((clock) & (clockid_t) CPUCLOCK_CLOCK_MASK)\n#define CPUCLOCK_CLOCK_MASK\t3\n#define CPUCLOCK_PROF\t\t0\n#define CPUCLOCK_VIRT\t\t1\n#define CPUCLOCK_SCHED\t\t2\n#define CPUCLOCK_MAX\t\t3\n#define CLOCKFD\t\t\tCPUCLOCK_MAX\n#define CLOCKFD_MASK\t\t(CPUCLOCK_PERTHREAD_MASK|CPUCLOCK_CLOCK_MASK)\n\nstatic inline clockid_t make_process_cpuclock(const unsigned int pid,\n\t\tconst clockid_t clock)\n{\n\treturn ((~pid) << 3) | clock;\n}\nstatic inline clockid_t make_thread_cpuclock(const unsigned int tid,\n\t\tconst clockid_t clock)\n{\n\treturn make_process_cpuclock(tid, clock | CPUCLOCK_PERTHREAD_MASK);\n}\n\nstatic inline clockid_t fd_to_clockid(const int fd)\n{\n\treturn make_process_cpuclock((unsigned int) fd, CLOCKFD);\n}\n\nstatic inline int clockid_to_fd(const clockid_t clk)\n{\n\treturn ~(clk >> 3);\n}\n\n#define REQUEUE_PENDING 1\n\n/**\n * struct k_itimer - POSIX.1b interval timer structure.\n * @list:\t\tList head for binding the timer to signals->posix_timers\n * @t_hash:\t\tEntry in the posix timer hash table\n * @it_lock:\t\tLock protecting the timer\n * @kclock:\t\tPointer to the k_clock struct handling this timer\n * @it_clock:\t\tThe posix timer clock id\n * @it_id:\t\tThe posix timer id for identifying the timer\n * @it_active:\t\tMarker that timer is active\n * @it_overrun:\t\tThe overrun counter for pending signals\n * @it_overrun_last:\tThe overrun at the time of the last delivered signal\n * @it_requeue_pending:\tIndicator that timer waits for being requeued on\n *\t\t\tsignal delivery\n * @it_sigev_notify:\tThe notify word of sigevent struct for signal delivery\n * @it_interval:\tThe interval for periodic timers\n * @it_signal:\t\tPointer to the creators signal struct\n * @it_pid:\t\tThe pid of the process/task targeted by the signal\n * @it_process:\t\tThe task to wakeup on clock_nanosleep (CPU timers)\n * @sigq:\t\tPointer to preallocated sigqueue\n * @it:\t\t\tUnion representing the various posix timer type\n *\t\t\tinternals. Also used for rcu freeing the timer.\n */\nstruct k_itimer {\n\tstruct list_head\tlist;\n\tstruct hlist_node\tt_hash;\n\tspinlock_t\t\tit_lock;\n\tconst struct k_clock\t*kclock;\n\tclockid_t\t\tit_clock;\n\ttimer_t\t\t\tit_id;\n\tint\t\t\tit_active;\n\ts64\t\t\tit_overrun;\n\ts64\t\t\tit_overrun_last;\n\tint\t\t\tit_requeue_pending;\n\tint\t\t\tit_sigev_notify;\n\tktime_t\t\t\tit_interval;\n\tstruct signal_struct\t*it_signal;\n\tunion {\n\t\tstruct pid\t\t*it_pid;\n\t\tstruct task_struct\t*it_process;\n\t};\n\tstruct sigqueue\t\t*sigq;\n\tunion {\n\t\tstruct {\n\t\t\tstruct hrtimer\ttimer;\n\t\t} real;\n\t\tstruct cpu_timer_list\tcpu;\n\t\tstruct {\n\t\t\tstruct alarm\talarmtimer;\n\t\t} alarm;\n\t\tstruct rcu_head\t\trcu;\n\t} it;\n};\n\nvoid run_posix_cpu_timers(struct task_struct *task);\n// ... 函数内容截断 ...\n\t\t\tstruct hrtimer timer;\n\t\t\tktime_t interval;\n\t\t} real;\n\t\tstruct cpu_timer_list cpu;\n\t\tstruct {\n\t\t\tunsigned int clock;\n\t\t\tunsigned int node;\n\t\t\tunsigned long incr;\n\t\t\tunsigned long expires;\n\t\t} mmtimer;\n\t\tstruct {\n\t\t\tstruct alarm alarmtimer;\n\t\t\tktime_t interval;\n\t\t} alarm;\n\t\tstruct rcu_head rcu;\n\t} it;\n};\n\nstruct k_clock {\n\tint (*clock_getres) (const clockid_t which_clock, struct timespec *tp);\n\tint (*clock_set) (const clockid_t which_clock,\n\t\t\t  const struct timespec *tp);\n\tint (*clock_get) (const clockid_t which_clock, struct timespec * tp);\n\tint (*clock_adj) (const clockid_t which_clock, struct timex *tx);\n\tint (*timer_create) (struct k_itimer *timer);\n\tint (*nsleep) (const clockid_t which_clock, int flags,\n\t\t       struct timespec *, struct timespec __user *);\n\tlong (*nsleep_restart) (struct restart_block *restart_block);\n\tint (*timer_set) (struct k_itimer * timr, int flags,\n\t\t\t  struct itimerspec * new_setting,\n\t\t\t  struct itimerspec * old_setting);\n\tint (*timer_del) (struct k_itimer * timr);\n#define TIMER_RETRY 1\n\tvoid (*timer_get) (struct k_itimer * timr,\n\t\t\t   struct itimerspec * cur_setting);\n};\n\nextern struct k_clock clock_posix_cpu;\nextern struct k_clock clock_posix_dynamic;\n\nvoid posix_timers_register_clock(const clockid_t clock_id, struct k_clock *new_clock);\n\n/* function to call to trigger timer event */\nint posix_timer_event(struct k_itimer *timr, int si_private);\n\nvoid posix_cpu_timer_schedule(struct k_itimer *timer);\n\nvoid run_posix_cpu_timers(struct task_struct *task);\nvoid posix_cpu_timers_exit(struct task_struct *task);\nvoid posix_cpu_timers_exit_group(struct task_struct *task);\n\nbool posix_cpu_timers_can_stop_tick(struct task_struct *tsk);\n\nvoid set_process_cpu_timer(struct task_struct *task, unsigned int clock_idx,\n\t\t\t   cputime_t *newval, cputime_t *oldval);\n\nlong clock_nanosleep_restart(struct restart_block *restart_block);\n\nvoid update_rlimit_cpu(struct task_struct *task, unsigned long rlim_new);\n\n#endif", "/*\n * IPv6 library code, needed by static components when full IPv6 support is\n * not configured or static.  These functions are needed by GSO/GRO implementation.\n */\n#include <linux/export.h>\n#include <net/ip.h>\n// ... 函数内容截断 ...\n\t\trcu_read_lock();\n\t\tidev = __in6_dev_get(dev);\n\t\tif (idev)\n\t\t\thoplimit = idev->cnf.hop_limit;\n\t\telse\n\t\t\thoplimit = dev_net(dev)->ipv6.devconf_all->hop_limit;\n\t\trcu_read_unlock();\n\t}\n\treturn hoplimit;\n}\nEXPORT_SYMBOL(ip6_dst_hoplimit);\n#endif\n\nint __ip6_local_out(struct net *net, struct sock *sk, struct sk_buff *skb)\n{\n\tint len;\n\n\tlen = skb->len - sizeof(struct ipv6hdr);\n\tif (len > IPV6_MAXPLEN)\n\t\tlen = 0;\n\tipv6_hdr(skb)->payload_len = htons(len);\n\tIP6CB(skb)->nhoff = offsetof(struct ipv6hdr, nexthdr);\n\n\tskb->protocol = htons(ETH_P_IPV6);\n\n\treturn nf_hook(NFPROTO_IPV6, NF_INET_LOCAL_OUT,\n\t\t       net, sk, skb, NULL, skb_dst(skb)->dev,\n\t\t       dst_output);\n}\nEXPORT_SYMBOL_GPL(__ip6_local_out);\n\nint ip6_local_out(struct net *net, struct sock *sk, struct sk_buff *skb)\n{\n\tint err;\n\n\terr = __ip6_local_out(net, sk, skb);\n\tif (likely(err == 1))\n\t\terr = dst_output(net, sk, skb);\n\n\treturn err;\n}\nEXPORT_SYMBOL_GPL(ip6_local_out);", "/*\n * ALSA timer back-end using hrtimer\n * Copyright (C) 2008 <PERSON><PERSON><PERSON>\n *\n *   This program is free software; you can redistribute it and/or modify\n *   it under the terms of the GNU General Public License as published by\n *   the Free Software Foundation; either version 2 of the License, or\n *   (at your option) any later version.\n *\n *   This program is distributed in the hope that it will be useful,\n *   but WITHOUT ANY WARRANTY; without even the implied warranty of\n *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n *   GNU General Public License for more details.\n *\n *   You should have received a copy of the GNU General Public License\n *   along with this program; if not, write to the Free Software\n *   Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307 USA\n *\n */\n\n#include <linux/init.h>\n#include <linux/slab.h>\n#include <linux/module.h>\n#include <linux/moduleparam.h>\n#include <linux/hrtimer.h>\n#include <sound/core.h>\n#include <sound/timer.h>\n\nMODULE_AUTHOR(\"<NAME> <<EMAIL>>\");\nMODULE_DESCRIPTION(\"ALSA hrtimer backend\");\nMODULE_LICENSE(\"GPL\");\n\nMODULE_ALIAS(\"snd-timer-\" __stringify(SNDRV_TIMER_GLOBAL_HRTIMER));\n\n#define NANO_SEC\t1000000000UL\t/* 10^9 in sec */\nstatic unsigned int resolution;\n\nstruct snd_hrtimer {\n\tstruct snd_timer *timer;\n\tstruct hrtimer hrt;\n\tatomic_t running;\n};\n\nstatic enum hrtimer_restart snd_hrtimer_callback(struct hrtimer *hrt)\n{\n\tstruct snd_hrtimer *stime = container_of(hrt, struct snd_hrtimer, hrt);\n\tstruct snd_timer *t = stime->timer;\n\tunsigned long oruns;\n\n\tif (!atomic_read(&stime->running))\n\t\treturn HRTIMER_NORESTART;\n\n\toruns = hrtimer_forward_now(hrt, ns_to_ktime(t->sticks * resolution));\n\tsnd_timer_interrupt(stime->timer, t->sticks * oruns);\n\n\tif (!atomic_read(&stime->running))\n\t\treturn HRTIMER_NORESTART;\n\treturn HRTIMER_RESTART;\n}\n\nstatic int snd_hrtimer_open(struct snd_timer *t)\n{\n\tstruct snd_hrtimer *stime;\n\n\tstime = kmalloc(sizeof(*stime), GFP_KERNEL);\n\tif (!stime)\n\t\treturn -ENOMEM;\n\thrtimer_init(&stime->hrt, CLOCK_MONOTONIC, HRTIMER_MODE_REL);\n\tstime->timer = t;\n\tstime->hrt.function = snd_hrtimer_callback;\n\tatomic_set(&stime->running, 0);\n\tt->private_data = stime;\n\treturn 0;\n}\n\nstatic int snd_hrtimer_close(struct snd_timer *t)\n{\n\tstruct snd_hrtimer *stime = t->private_data;\n\n\tif (stime) {\n\t\thrtimer_cancel(&stime->hrt);\n\t\tkfree(stime);\n\t\tt->private_data = NULL;\n\t}\n\treturn 0;\n}\n\nstatic int snd_hrtimer_start(struct snd_timer *t)\n{\n\tstruct snd_hrtimer *stime = t->private_data;\n\n\tatomic_set(&stime->running, 0);\n\thrtimer_try_to_cancel(&stime->hrt);\n\thrtimer_start(&stime->hrt, ns_to_ktime(t->sticks * resolution),\n\t\t      HRTIMER_MODE_REL);\n\tatomic_set(&stime->running, 1);\n\treturn 0;\n}\n\nstatic int snd_hrtimer_stop(struct snd_timer *t)\n{\n\tstruct snd_hrtimer *stime = t->private_data;\n\tatomic_set(&stime->running, 0);\n\thrtimer_try_to_cancel(&stime->hrt);\n\treturn 0;\n}\n\nstatic struct snd_timer_hardware hrtimer_hw = {\n\t.flags =\tSNDRV_TIMER_HW_AUTO | SNDRV_TIMER_HW_TASKLET,\n\t.open =\t\tsnd_hrtimer_open,\n\t.close =\tsnd_hrtimer_close,\n\t.start =\tsnd_hrtimer_start,\n\t.stop =\t\tsnd_hrtimer_stop,\n};\n\n/*\n * entry functions\n */\n\nstatic struct snd_timer *mytimer;\n\nstatic int __init snd_hrtimer_init(void)\n{\n\tstruct snd_timer *timer;\n\tstruct timespec tp;\n\tint err;\n\n\thrtimer_get_res(CLOCK_MONOTONIC, &tp);\n\tif (tp.tv_sec > 0 || !tp.tv_nsec) {\n\t\tsnd_printk(KERN_ERR\n\t\t\t   \"snd-hrtimer: Invalid resolution %u.%09u\",\n\t\t\t   (unsigned)tp.tv_sec, (unsigned)tp.tv_nsec);\n\t\treturn -EINVAL;\n\t}\n\tresolution = tp.tv_nsec;\n\n\t/* Create a new timer and set up the fields */\n\terr = snd_timer_global_new(\"hrtimer\", SNDRV_TIMER_GLOBAL_HRTIMER,\n\t\t\t\t   &timer);\n\tif (err < 0)\n\t\treturn err;\n\n\ttimer->module = THIS_MODULE;\n\tstrcpy(timer->name, \"HR timer\");\n\ttimer->hw = hrtimer_hw;\n\ttimer->hw.resolution = resolution;\n\ttimer->hw.ticks = NANO_SEC / resolution;\n\n\terr = snd_timer_global_register(timer);\n\tif (err < 0) {\n\t\tsnd_timer_global_free(timer);\n\t\treturn err;\n\t}\n\tmytimer = timer; /* remember this */\n\n\treturn 0;\n}\n\nstatic void __exit snd_hrtimer_exit(void)\n{\n\tif (mytimer) {\n\t\tsnd_timer_global_free(mytimer);\n\t\tmytimer = NULL;\n\t}\n}\n\nmodule_init(snd_hrtimer_init);\nmodule_exit(snd_hrtimer_exit);", "/*\n * Common library for ADIS16XXX devices\n *\n * Copyright 2012 Analog Devices Inc.\n *   Author: <PERSON><PERSON><PERSON> <<EMAIL>>\n *\n * Licensed under the GPL-2 or later.\n */\n\n#include <linux/export.h>\n#include <linux/interrupt.h>\n#include <linux/mutex.h>\n#include <linux/kernel.h>\n#include <linux/spi/spi.h>\n#include <linux/slab.h>\n\n#include <linux/iio/iio.h>\n#include <linux/iio/buffer.h>\n#include <linux/iio/trigger_consumer.h>\n#include <linux/iio/triggered_buffer.h>\n#include <linux/iio/imu/adis.h>\n\nint adis_update_scan_mode(struct iio_dev *indio_dev,\n\tconst unsigned long *scan_mask)\n{\n\tstruct adis *adis = iio_device_get_drvdata(indio_dev);\n\tconst struct iio_chan_spec *chan;\n\tunsigned int scan_count;\n\tunsigned int i, j;\n\t__be16 *tx, *rx;\n\n\tkfree(adis->xfer);\n\tkfree(adis->buffer);\n\n\tscan_count = indio_dev->scan_bytes / 2;\n\n\tadis->xfer = kcalloc(scan_count + 1, sizeof(*adis->xfer), GFP_KERNEL);\n\tif (!adis->xfer)\n\t\treturn -ENOMEM;\n\n\tadis->buffer = kcalloc(indio_dev->scan_bytes, 2, GFP_KERNEL);\n\tif (!adis->buffer) {\n\t\tkfree(adis->xfer);\n\t\tadis->xfer = NULL;\n\t\treturn -ENOMEM;\n\t}\n\n\trx = adis->buffer;\n\ttx = rx + scan_count;\n\n\tspi_message_init(&adis->msg);\n\n\tfor (j = 0; j <= scan_count; j++) {\n\t\tadis->xfer[j].bits_per_word = 8;\n\t\tif (j != scan_count)\n\t\t\tadis->xfer[j].cs_change = 1;\n\t\tadis->xfer[j].len = 2;\n\t\tadis->xfer[j].delay_usecs = adis->data->read_delay;\n\t\tif (j < scan_count)\n\t\t\tadis->xfer[j].tx_buf = &tx[j];\n\t\tif (j >= 1)\n\t\t\tadis->xfer[j].rx_buf = &rx[j - 1];\n\t\tspi_message_add_tail(&adis->xfer[j], &adis->msg);\n\t}\n\n\tchan = indio_dev->channels;\n\tfor (i = 0; i < indio_dev->num_channels; i++, chan++) {\n\t\tif (!test_bit(chan->scan_index, scan_mask))\n\t\t\tcontinue;\n\t\tif (chan->scan_type.storagebits == 32)\n\t\t\t*tx++ = cpu_to_be16((chan->address + 2) << 8);\n\t\t*tx++ = cpu_to_be16(chan->address << 8);\n\t}\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(adis_update_scan_mode);\n\nstatic irqreturn_t adis_trigger_handler(int irq, void *p)\n{\n\tstruct iio_poll_func *pf = p;\n\tstruct iio_dev *indio_dev = pf->indio_dev;\n\tstruct adis *adis = iio_device_get_drvdata(indio_dev);\n\tint ret;\n\n\tif (!adis->buffer)\n\t\treturn -ENOMEM;\n\n\tif (adis->data->has_paging) {\n\t\tmutex_lock(&adis->txrx_lock);\n\t\tif (adis->current_page != 0) {\n\t\t\tadis->tx[0] = ADIS_WRITE_REG(ADIS_REG_PAGE_ID);\n\t\t\tadis->tx[1] = 0;\n\t\t\tspi_write(adis->spi, adis->tx, 2);\n\t\t}\n\t}\n\n\tret = spi_sync(adis->spi, &adis->msg);\n\tif (ret)\n\t\tdev_err(&adis->spi->dev, \"Failed to read data: %d\", ret);\n\n\n\tif (adis->data->has_paging) {\n\t\tadis->current_page = 0;\n\t\tmutex_unlock(&adis->txrx_lock);\n\t}\n\n\tiio_push_to_buffers_with_timestamp(indio_dev, adis->buffer,\n\t\tpf->timestamp);\n\n\tiio_trigger_notify_done(indio_dev->trig);\n\n\treturn IRQ_HANDLED;\n}\n\n/**\n * adis_setup_buffer_and_trigger() - Sets up buffer and trigger for the adis device\n * @adis: The adis device.\n * @indio_dev: The IIO device.\n * @trigger_handler: Optional trigger handler, may be NULL.\n *\n * Returns 0 on success, a negative error code otherwise.\n *\n * This function sets up the buffer and trigger for a adis devices.  If\n * 'trigger_handler' is NULL the default trigger handler will be used. The\n * default trigger handler will simply read the registers assigned to the\n * currently active channels.\n *\n * adis_cleanup_buffer_and_trigger() should be called to free the resources\n * allocated by this function.\n */\nint adis_setup_buffer_and_trigger(struct adis *adis, struct iio_dev *indio_dev,\n\tirqreturn_t (*trigger_handler)(int, void *))\n{\n\tint ret;\n\n\tif (!trigger_handler)\n\t\ttrigger_handler = adis_trigger_handler;\n\n\tret = iio_triggered_buffer_setup(indio_dev, &iio_pollfunc_store_time,\n\t\ttrigger_handler, NULL);\n\tif (ret)\n\t\treturn ret;\n\n\tif (adis->spi->irq) {\n\t\tret = adis_probe_trigger(adis, indio_dev);\n\t\tif (ret)\n\t\t\tgoto error_buffer_cleanup;\n\t}\n\treturn 0;\n\nerror_buffer_cleanup:\n\tiio_triggered_buffer_cleanup(indio_dev);\n\treturn ret;\n}\nEXPORT_SYMBOL_GPL(adis_setup_buffer_and_trigger);\n\n/**\n * adis_cleanup_buffer_and_trigger() - Free buffer and trigger resources\n * @adis: The adis device.\n * @indio_dev: The IIO device.\n *\n * Frees resources allocated by adis_setup_buffer_and_trigger()\n */\nvoid adis_cleanup_buffer_and_trigger(struct adis *adis,\n\tstruct iio_dev *indio_dev)\n{\n\tif (adis->spi->irq)\n\t\tadis_remove_trigger(adis);\n\tkfree(adis->buffer);\n\tkfree(adis->xfer);\n\tiio_triggered_buffer_cleanup(indio_dev);\n}\nEXPORT_SYMBOL_GPL(adis_cleanup_buffer_and_trigger);", "#ifndef __LINUX_ATALK_H__\n#define __LINUX_ATALK_H__\n\n\n#include <net/sock.h>\n#include <uapi/linux/atalk.h>\n\nstruct atalk_route {\n\tstruct net_device  *dev;\n\tstruct atalk_addr  target;\n\tstruct atalk_addr  gateway;\n\tint\t\t   flags;\n\tstruct atalk_route *next;\n};\n\n/**\n *\tstruct atalk_iface - AppleTalk Interface\n *\t@dev - Network device associated with this interface\n *\t@address - Our address\n *\t@status - What are we doing?\n *\t@nets - Associated direct netrange\n *\t@next - next element in the list of interfaces\n */\nstruct atalk_iface {\n\tstruct net_device\t*dev;\n\tstruct atalk_addr\taddress;\n\tint\t\t\tstatus;\n#define ATIF_PROBE\t1\t\t/* Probing for an address */\n#define ATIF_PROBE_FAIL\t2\t\t/* Probe collided */\n\tstruct atalk_netrange\tnets;\n\tstruct atalk_iface\t*next;\n};\n\t\nstruct atalk_sock {\n\t/* struct sock has to be the first member of atalk_sock */\n\tstruct sock\tsk;\n\t__be16\t\tdest_net;\n\t__be16\t\tsrc_net;\n\tunsigned char\tdest_node;\n\tunsigned char\tsrc_node;\n\tunsigned char\tdest_port;\n\tunsigned char\tsrc_port;\n};\n\nstatic inline struct atalk_sock *at_sk(struct sock *sk)\n{\n\treturn (struct atalk_sock *)sk;\n}\n\nstruct ddpehdr {\n\t__be16\tdeh_len_hops;\t/* lower 10 bits are length, next 4 - hops */\n\t__be16\tdeh_sum;\n\t__be16\tdeh_dnet;\n\t__be16\tdeh_snet;\n\t__u8\tdeh_dnode;\n\t__u8\tdeh_snode;\n\t__u8\tdeh_dport;\n\t__u8\tdeh_sport;\n\t/* And netatalk apps expect to stick the type in themselves */\n};\n\nstatic __inline__ struct ddpehdr *ddp_hdr(struct sk_buff *skb)\n{\n\treturn (struct ddpehdr *)skb_transport_header(skb);\n}\n\n/* AppleTalk AARP headers */\nstruct elapaarp {\n\t__be16\thw_type;\n#define AARP_HW_TYPE_ETHERNET\t\t1\n#define AARP_HW_TYPE_TOKENRING\t\t2\n\t__be16\tpa_type;\n\t__u8\thw_len;\n\t__u8\tpa_len;\n#define AARP_PA_ALEN\t\t\t4\n\t__be16\tfunction;\n#define AARP_REQUEST\t\t\t1\n#define AARP_REPLY\t\t\t2\n#define AARP_PROBE\t\t\t3\n\t__u8\thw_src[ETH_ALEN];\n\t__u8\tpa_src_zero;\n\t__be16\tpa_src_net;\n\t__u8\tpa_src_node;\n\t__u8\thw_dst[ETH_ALEN];\n\t__u8\tpa_dst_zero;\n\t__be16\tpa_dst_net;\n\t__u8\tpa_dst_node;\n} __attribute__ ((packed));\n\nstatic __inline__ struct elapaarp *aarp_hdr(struct sk_buff *skb)\n{\n\treturn (struct elapaarp *)skb_transport_header(skb);\n}\n\n/* Not specified - how long till we drop a resolved entry */\n#define AARP_EXPIRY_TIME\t(5 * 60 * HZ)\n/* Size of hash table */\n#define AARP_HASH_SIZE\t\t16\n/* Fast retransmission timer when resolving */\n#define AARP_TICK_TIME\t\t(HZ / 5)\n/* Send 10 requests then give up (2 seconds) */\n#define AARP_RETRANSMIT_LIMIT\t10\n/*\n * Some value bigger than total retransmit time + a bit for last reply to\n * appear and to stop continual requests\n */\n#define AARP_RESOLVE_TIME\t(10 * HZ)\n\nextern struct datalink_proto *ddp_dl, *aarp_dl;\nextern int aarp_proto_init(void);\n\n/* Inter module exports */\n\n/* Give a device find its atif control structure */\nstatic inline struct atalk_iface *atalk_find_dev(struct net_device *dev)\n{\n\treturn dev->atalk_ptr;\n}\n\nextern struct atalk_addr *atalk_find_dev_addr(struct net_device *dev);\nextern struct net_device *atrtr_get_dev(struct atalk_addr *sa);\nextern int\t\t aarp_send_ddp(struct net_device *dev,\n\t\t\t\t       struct sk_buff *skb,\n\t\t\t\t       struct atalk_addr *sa, void *hwaddr);\nextern void\t\t aarp_device_down(struct net_device *dev);\nextern void\t\t aarp_probe_network(struct atalk_iface *atif);\nextern int \t\t aarp_proxy_probe_network(struct atalk_iface *atif,\n\t\t\t\t     struct atalk_addr *sa);\nextern void\t\t aarp_proxy_remove(struct net_device *dev,\n\t\t\t\t\t   struct atalk_addr *sa);\n\nextern void\t\taarp_cleanup_module(void);\n\nextern struct hlist_head atalk_sockets;\nextern rwlock_t atalk_sockets_lock;\n\nextern struct atalk_route *atalk_routes;\nextern rwlock_t atalk_routes_lock;\n\nextern struct atalk_iface *atalk_interfaces;\nextern rwlock_t atalk_interfaces_lock;\n\nextern struct atalk_route atrtr_default;\n\nextern const struct file_operations atalk_seq_arp_fops;\n\nextern int sysctl_aarp_expiry_time;\nextern int sysctl_aarp_tick_time;\nextern int sysctl_aarp_retransmit_limit;\nextern int sysctl_aarp_resolve_time;\n\n#ifdef CONFIG_SYSCTL\nextern int atalk_register_sysctl(void);\nextern void atalk_unregister_sysctl(void);\n#else\nstatic inline int atalk_register_sysctl(void)\n{\n\treturn 0;\n}\nstatic inline void atalk_unregister_sysctl(void)\n{\n}\n#endif\n\n#ifdef CONFIG_PROC_FS\nextern int atalk_proc_init(void);\nextern void atalk_proc_exit(void);\n#else\nstatic inline int atalk_proc_init(void)\n{\n\treturn 0;\n}\nstatic inline void atalk_proc_exit(void)\n{\n}\n#endif /* CONFIG_PROC_FS */\n\n#endif /* __LINUX_ATALK_H__ */", "// SPDX-License-Identifier: GPL-2.0\n#ifndef IOU_RSRC_H\n#define IOU_RSRC_H\n\n#include <net/af_unix.h>\n\n#define IO_RSRC_TAG_TABLE_SHIFT\t(PAGE_SHIFT - 3)\n#define IO_RSRC_TAG_TABLE_MAX\t(1U << IO_RSRC_TAG_TABLE_SHIFT)\n#define IO_RSRC_TAG_TABLE_MASK\t(IO_RSRC_TAG_TABLE_MAX - 1)\n\nenum {\n\tIORING_RSRC_FILE\t\t= 0,\n\tIORING_RSRC_BUFFER\t\t= 1,\n};\n\nstruct io_rsrc_put {\n\tstruct list_head list;\n\tu64 tag;\n\tunion {\n\t\tvoid *rsrc;\n\t\tstruct file *file;\n\t\tstruct io_mapped_ubuf *buf;\n\t};\n};\n\ntypedef void (rsrc_put_fn)(struct io_ring_ctx *ctx, struct io_rsrc_put *prsrc);\n\nstruct io_rsrc_data {\n\tstruct io_ring_ctx\t\t*ctx;\n\n\tu64\t\t\t\t**tags;\n\tunsigned int\t\t\tnr;\n\trsrc_put_fn\t\t\t*do_put;\n\tatomic_t\t\t\trefs;\n\tstruct completion\t\tdone;\n\tbool\t\t\t\tquiesce;\n};\n\nstruct io_rsrc_node {\n\tstruct percpu_ref\t\trefs;\n\tstruct list_head\t\tnode;\n\tstruct list_head\t\trsrc_list;\n\tstruct io_rsrc_data\t\t*rsrc_data;\n\tstruct llist_node\t\tllist;\n\tbool\t\t\t\tdone;\n};\n\nstruct io_mapped_ubuf {\n\tu64\t\tubuf;\n\tu64\t\tubuf_end;\n\tunsigned int\tnr_bvecs;\n\tunsigned long\tacct_pages;\n\tstruct bio_vec\tbvec[];\n};\n\nvoid io_rsrc_put_work(struct work_struct *work);\nvoid io_rsrc_refs_refill(struct io_ring_ctx *ctx);\nvoid io_wait_rsrc_data(struct io_rsrc_data *data);\nvoid io_rsrc_node_destroy(struct io_rsrc_node *ref_node);\nvoid io_rsrc_refs_drop(struct io_ring_ctx *ctx);\nint io_rsrc_node_switch_start(struct io_ring_ctx *ctx);\nint io_queue_rsrc_removal(struct io_rsrc_data *data, unsigned idx,\n\t\t\t  struct io_rsrc_node *node, void *rsrc);\nvoid io_rsrc_node_switch(struct io_ring_ctx *ctx,\n\t\t\t struct io_rsrc_data *data_to_kill);\n\nint io_import_fixed(int ddir, struct iov_iter *iter,\n\t\t\t   struct io_mapped_ubuf *imu,\n\t\t\t   u64 buf_addr, size_t len);\n\nvoid __io_sqe_buffers_unregister(struct io_ring_ctx *ctx);\nint io_sqe_buffers_unregister(struct io_ring_ctx *ctx);\nint io_sqe_buffers_register(struct io_ring_ctx *ctx, void __user *arg,\n\t\t\t    unsigned int nr_args, u64 __user *tags);\nvoid __io_sqe_files_unregister(struct io_ring_ctx *ctx);\nint io_sqe_files_unregister(struct io_ring_ctx *ctx);\nint io_sqe_files_register(struct io_ring_ctx *ctx, void __user *arg,\n\t\t\t  unsigned nr_args, u64 __user *tags);\n\nint __io_scm_file_account(struct io_ring_ctx *ctx, struct file *file);\n\nstatic inline bool io_file_need_scm(struct file *filp)\n{\n\treturn false;\n}\n\nstatic inline int io_scm_file_account(struct io_ring_ctx *ctx,\n\t\t\t\t      struct file *file)\n{\n\tif (likely(!io_file_need_scm(file)))\n\t\treturn 0;\n\treturn __io_scm_file_account(ctx, file);\n}\n\nint io_register_files_update(struct io_ring_ctx *ctx, void __user *arg,\n\t\t\t     unsigned nr_args);\nint io_register_rsrc_update(struct io_ring_ctx *ctx, void __user *arg,\n\t\t\t    unsigned size, unsigned type);\nint io_register_rsrc(struct io_ring_ctx *ctx, void __user *arg,\n\t\t\tunsigned int size, unsigned int type);\n\nstatic inline void io_rsrc_put_node(struct io_rsrc_node *node, int nr)\n{\n\tpercpu_ref_put_many(&node->refs, nr);\n}\n\nstatic inline void io_req_put_rsrc(struct io_kiocb *req)\n{\n\tif (req->rsrc_node)\n\t\tio_rsrc_put_node(req->rsrc_node, 1);\n}\n\nstatic inline void io_req_put_rsrc_locked(struct io_kiocb *req,\n\t\t\t\t\t  struct io_ring_ctx *ctx)\n\t__must_hold(&ctx->uring_lock)\n{\n\tstruct io_rsrc_node *node = req->rsrc_node;\n\n\tif (node) {\n\t\tif (node == ctx->rsrc_node)\n\t\t\tctx->rsrc_cached_refs++;\n\t\telse\n\t\t\tio_rsrc_put_node(node, 1);\n\t}\n}\n\nstatic inline void io_charge_rsrc_node(struct io_ring_ctx *ctx)\n{\n\tctx->rsrc_cached_refs--;\n\tif (unlikely(ctx->rsrc_cached_refs < 0))\n\t\tio_rsrc_refs_refill(ctx);\n}\n\nstatic inline void io_req_set_rsrc_node(struct io_kiocb *req,\n\t\t\t\t\tstruct io_ring_ctx *ctx,\n\t\t\t\t\tunsigned int issue_flags)\n{\n\tif (!req->rsrc_node) {\n\t\tio_ring_submit_lock(ctx, issue_flags);\n\n\t\tlockdep_assert_held(&ctx->uring_lock);\n\n\t\treq->rsrc_node = ctx->rsrc_node;\n\t\tio_charge_rsrc_node(ctx);\n\t\tio_ring_submit_unlock(ctx, issue_flags);\n\t}\n}\n\nstatic inline u64 *io_get_tag_slot(struct io_rsrc_data *data, unsigned int idx)\n{\n\tunsigned int off = idx & IO_RSRC_TAG_TABLE_MASK;\n\tunsigned int table_idx = idx >> IO_RSRC_TAG_TABLE_SHIFT;\n\n\treturn &data->tags[table_idx][off];\n}\n\nint io_files_update(struct io_kiocb *req, unsigned int issue_flags);\nint io_files_update_prep(struct io_kiocb *req, const struct io_uring_sqe *sqe);\n\nint __io_account_mem(struct user_struct *user, unsigned long nr_pages);\n\nstatic inline void __io_unaccount_mem(struct user_struct *user,\n\t\t\t\t      unsigned long nr_pages)\n{\n\tatomic_long_sub(nr_pages, &user->locked_vm);\n}\n\n#endif", "/* SPDX-License-Identifier: GPL-2.0 */\n/*\n * bvec iterator\n *\n * Copyright (C) 2001 <PERSON> Le<PERSON> <<EMAIL>>\n */\n#ifndef __LINUX_BVEC_ITER_H\n#define __LINUX_BVEC_ITER_H\n\n#include <linux/kernel.h>\n#include <linux/bug.h>\n#include <linux/errno.h>\n#include <linux/mm.h>\n\n/*\n * was unsigned short, but we might as well be ready for > 64kB I/O pages\n */\nstruct bio_vec {\n\tstruct page\t*bv_page;\n\tunsigned int\tbv_len;\n\tunsigned int\tbv_offset;\n};\n\nstruct bvec_iter {\n\tsector_t\t\tbi_sector;\t/* device address in 512 byte\n\t\t\t\t\t\t   sectors */\n\tunsigned int\t\tbi_size;\t/* residual I/O count */\n\n\tunsigned int\t\tbi_idx;\t\t/* current index into bvl_vec */\n\n\tunsigned int            bi_bvec_done;\t/* number of bytes completed in\n\t\t\t\t\t\t   current bvec */\n};\n\nstruct bvec_iter_all {\n\tstruct bio_vec\tbv;\n\tint\t\tidx;\n\tunsigned\tdone;\n};\n\n/*\n * various member access, note that bio_data should of course not be used\n * on highmem page vectors\n */\n#define __bvec_iter_bvec(bvec, iter)\t(&(bvec)[(iter).bi_idx])\n\n/* multi-page (mp_bvec) helpers */\n#define mp_bvec_iter_page(bvec, iter)\t\t\t\t\\\n\t(__bvec_iter_bvec((bvec), (iter))->bv_page)\n\n#define mp_bvec_iter_len(bvec, iter)\t\t\t\t\\\n\tmin((iter).bi_size,\t\t\t\t\t\\\n\t    __bvec_iter_bvec((bvec), (iter))->bv_len - (iter).bi_bvec_done)\n\n#define mp_bvec_iter_offset(bvec, iter)\t\t\t\t\\\n\t(__bvec_iter_bvec((bvec), (iter))->bv_offset + (iter).bi_bvec_done)\n\n#define mp_bvec_iter_page_idx(bvec, iter)\t\t\t\\\n\t(mp_bvec_iter_offset((bvec), (iter)) / PAGE_SIZE)\n\n#define mp_bvec_iter_bvec(bvec, iter)\t\t\t\t\\\n((struct bio_vec) {\t\t\t\t\t\t\\\n\t.bv_page\t= mp_bvec_iter_page((bvec), (iter)),\t\\\n\t.bv_len\t\t= mp_bvec_iter_len((bvec), (iter)),\t\\\n\t.bv_offset\t= mp_bvec_iter_offset((bvec), (iter)),\t\\\n})\n\n/* For building single-page bvec in flight */\n #define bvec_iter_offset(bvec, iter)\t\t\t\t\\\n\t(mp_bvec_iter_offset((bvec), (iter)) % PAGE_SIZE)\n\n#define bvec_iter_len(bvec, iter)\t\t\t\t\\\n\tmin_t(unsigned, mp_bvec_iter_len((bvec), (iter)),\t\t\\\n\t      PAGE_SIZE - bvec_iter_offset((bvec), (iter)))\n\n#define bvec_iter_page(bvec, iter)\t\t\t\t\\\n\t(mp_bvec_iter_page((bvec), (iter)) +\t\t\t\\\n\t mp_bvec_iter_page_idx((bvec), (iter)))\n\n#define bvec_iter_bvec(bvec, iter)\t\t\t\t\\\n((struct bio_vec) {\t\t\t\t\t\t\\\n\t.bv_page\t= bvec_iter_page((bvec), (iter)),\t\\\n\t.bv_len\t\t= bvec_iter_len((bvec), (iter)),\t\\\n\t.bv_offset\t= bvec_iter_offset((bvec), (iter)),\t\\\n})\n\nstatic inline bool bvec_iter_advance(const struct bio_vec *bv,\n\t\tstruct bvec_iter *iter, unsigned bytes)\n{\n\tif (WARN_ONCE(bytes > iter->bi_size,\n\t\t     \"Attempted to advance past end of bvec iter\\n\")) {\n\t\titer->bi_size = 0;\n\t\treturn false;\n\t}\n\n\twhile (bytes) {\n\t\tconst struct bio_vec *cur = bv + iter->bi_idx;\n\t\tunsigned len = min3(bytes, iter->bi_size,\n\t\t\t\t    cur->bv_len - iter->bi_bvec_done);\n\n\t\tbytes -= len;\n\t\titer->bi_size -= len;\n\t\titer->bi_bvec_done += len;\n\n\t\tif (iter->bi_bvec_done == cur->bv_len) {\n\t\t\titer->bi_bvec_done = 0;\n\t\t\titer->bi_idx++;\n\t\t}\n\t}\n\treturn true;\n}\n\nstatic inline void bvec_iter_skip_zero_bvec(struct bvec_iter *iter)\n{\n\titer->bi_bvec_done = 0;\n\titer->bi_idx++;\n}\n\n#define for_each_bvec(bvl, bio_vec, iter, start)\t\t\t\\\n\tfor (iter = (start);\t\t\t\t\t\t\\\n\t     (iter).bi_size &&\t\t\t\t\t\t\\\n\t\t((bvl = bvec_iter_bvec((bio_vec), (iter))), 1);\t\\\n\t     (bvl).bv_len ? (void)bvec_iter_advance((bio_vec), &(iter),\t\\\n\t\t     (bvl).bv_len) : bvec_iter_skip_zero_bvec(&(iter)))\n\n/* for iterating one bio from start to end */\n#define BVEC_ITER_ALL_INIT (struct bvec_iter)\t\t\t\t\\\n{\t\t\t\t\t\t\t\t\t\\\n\t.bi_sector\t= 0,\t\t\t\t\t\t\\\n\t.bi_size\t= UINT_MAX,\t\t\t\t\t\\\n\t.bi_idx\t\t= 0,\t\t\t\t\t\t\\\n\t.bi_bvec_done\t= 0,\t\t\t\t\t\t\\\n}\n\nstatic inline struct bio_vec *bvec_init_iter_all(struct bvec_iter_all *iter_all)\n{\n\titer_all->done = 0;\n\titer_all->idx = 0;\n\n\treturn &iter_all->bv;\n}\n\nstatic inline void bvec_advance(const struct bio_vec *bvec,\n\t\t\t\tstruct bvec_iter_all *iter_all)\n{\n\tstruct bio_vec *bv = &iter_all->bv;\n\n\tif (iter_all->done) {\n\t\tbv->bv_page++;\n\t\tbv->bv_offset = 0;\n\t} else {\n\t\tbv->bv_page = bvec->bv_page + (bvec->bv_offset >> PAGE_SHIFT);\n\t\tbv->bv_offset = bvec->bv_offset & ~PAGE_MASK;\n\t}\n\tbv->bv_len = min_t(unsigned int, PAGE_SIZE - bv->bv_offset,\n\t\t\t   bvec->bv_len - iter_all->done);\n\titer_all->done += bv->bv_len;\n\n\tif (iter_all->done == bvec->bv_len) {\n\t\titer_all->idx++;\n\t\titer_all->done = 0;\n\t}\n}\n\n/*\n * Get the last single-page segment from the multi-page bvec and store it\n * in @seg\n */\nstatic inline void mp_bvec_last_segment(const struct bio_vec *bvec,\n\t\t\t\t\tstruct bio_vec *seg)\n{\n\tconst struct bio_vec *bvec_end = bvec + bvec->bv_len;\n\tconst struct bio_vec *bvec_last = bvec_end - 1;\n\n\tseg->bv_page = bvec_last->bv_page;\n\tseg->bv_offset = bvec_last->bv_offset + bvec_last->bv_len;\n\tseg->bv_len = bvec_end->bv_offset;\n}\n\n#endif /* __LINUX_BVEC_ITER_H */", "/*\n * FPU: Wrapper for blkcipher touching fpu\n *\n * Copyright (c) Intel Corp.\n *   Author: <PERSON> <<EMAIL>>\n *\n * This program is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License as published by the Free\n * Software Foundation; either version 2 of the License, or (at your option)\n * any later version.\n *\n */\n\n#include <crypto/algapi.h>\n#include <linux/err.h>\n#include <linux/init.h>\n#include <linux/kernel.h>\n#include <linux/module.h>\n#include <linux/slab.h>\n#include <linux/crypto.h>\n#include <asm/i387.h>\n\nstruct crypto_fpu_ctx {\n\tstruct crypto_blkcipher *child;\n};\n\nstatic int crypto_fpu_setkey(struct crypto_tfm *parent, const u8 *key,\n\t\t\t     unsigned int keylen)\n{\n\tstruct crypto_fpu_ctx *ctx = crypto_tfm_ctx(parent);\n\tstruct crypto_blkcipher *child = ctx->child;\n\tint err;\n\n\tcrypto_blkcipher_clear_flags(child, CRYPTO_TFM_REQ_MASK);\n\tcrypto_blkcipher_set_flags(child, crypto_tfm_get_flags(parent) &\n\t\t\t\t   CRYPTO_TFM_REQ_MASK);\n\terr = crypto_blkcipher_setkey(child, key, keylen);\n\tcrypto_tfm_set_flags(parent, crypto_blkcipher_get_flags(child) &\n\t\t\t\t     CRYPTO_TFM_RES_MASK);\n\treturn err;\n}\n\nstatic int crypto_fpu_encrypt(struct blkcipher_desc *desc_in,\n\t\t\t      struct scatterlist *dst, struct scatterlist *src,\n\t\t\t      unsigned int nbytes)\n{\n\tint err;\n\tstruct crypto_fpu_ctx *ctx = crypto_blkcipher_ctx(desc_in->tfm);\n\tstruct crypto_blkcipher *child = ctx->child;\n\tstruct blkcipher_desc desc = {\n\t\t.tfm = child,\n\t\t.info = desc_in->info,\n\t\t.flags = desc_in->flags & ~CRYPTO_TFM_REQ_MAY_SLEEP,\n\t};\n\n\tkernel_fpu_begin();\n\terr = crypto_blkcipher_crt(desc.tfm)->encrypt(&desc, dst, src, nbytes);\n\tkernel_fpu_end();\n\treturn err;\n}\n\nstatic int crypto_fpu_decrypt(struct blkcipher_desc *desc_in,\n\t\t\t      struct scatterlist *dst, struct scatterlist *src,\n\t\t\t      unsigned int nbytes)\n{\n\tint err;\n\tstruct crypto_fpu_ctx *ctx = crypto_blkcipher_ctx(desc_in->tfm);\n\tstruct crypto_blkcipher *child = ctx->child;\n\tstruct blkcipher_desc desc = {\n\t\t.tfm = child,\n\t\t.info = desc_in->info,\n\t\t.flags = desc_in->flags & ~CRYPTO_TFM_REQ_MAY_SLEEP,\n\t};\n\n\tkernel_fpu_begin();\n\terr = crypto_blkcipher_crt(desc.tfm)->decrypt(&desc, dst, src, nbytes);\n\tkernel_fpu_end();\n\treturn err;\n}\n\nstatic int crypto_fpu_init_tfm(struct crypto_tfm *tfm)\n{\n\tstruct crypto_instance *inst = crypto_tfm_alg_instance(tfm);\n\tstruct crypto_spawn *spawn = crypto_instance_ctx(inst);\n\tstruct crypto_fpu_ctx *ctx = crypto_tfm_ctx(tfm);\n\tstruct crypto_blkcipher *cipher;\n\n\tcipher = crypto_spawn_blkcipher(spawn);\n\tif (IS_ERR(cipher))\n\t\treturn PTR_ERR(cipher);\n\n\tctx->child = cipher;\n\treturn 0;\n}\n\nstatic void crypto_fpu_exit_tfm(struct crypto_tfm *tfm)\n{\n\tstruct crypto_fpu_ctx *ctx = crypto_tfm_ctx(tfm);\n\tcrypto_free_blkcipher(ctx->child);\n}\n\nstatic struct crypto_instance *crypto_fpu_alloc(struct rtattr **tb)\n{\n\tstruct crypto_instance *inst;\n\tstruct crypto_alg *alg;\n\tint err;\n\n\terr = crypto_check_attr_type(tb, CRYPTO_ALG_TYPE_BLKCIPHER);\n\tif (err)\n\t\treturn ERR_PTR(err);\n\n\talg = crypto_get_attr_alg(tb, CRYPTO_ALG_TYPE_BLKCIPHER,\n\t\t\t\t  CRYPTO_ALG_TYPE_MASK);\n\tif (IS_ERR(alg))\n\t\treturn ERR_CAST(alg);\n\n\tinst = crypto_alloc_instance(\"fpu\", alg);\n\tif (IS_ERR(inst))\n\t\tgoto out_put_alg;\n\n\tinst->alg.cra_flags = alg->cra_flags;\n\tinst->alg.cra_priority = alg->cra_priority;\n\tinst->alg.cra_blocksize = alg->cra_blocksize;\n\tinst->alg.cra_alignmask = alg->cra_alignmask;\n\tinst->alg.cra_type = alg->cra_type;\n\tinst->alg.cra_blkcipher.ivsize = alg->cra_blkcipher.ivsize;\n\tinst->alg.cra_blkcipher.min_keysize = alg->cra_blkcipher.min_keysize;\n\tinst->alg.cra_blkcipher.max_keysize = alg->cra_blkcipher.max_keysize;\n\tinst->alg.cra_ctxsize = sizeof(struct crypto_fpu_ctx);\n\tinst->alg.cra_init = crypto_fpu_init_tfm;\n\tinst->alg.cra_exit = crypto_fpu_exit_tfm;\n\tinst->alg.cra_blkcipher.setkey = crypto_fpu_setkey;\n\tinst->alg.cra_blkcipher.encrypt = crypto_fpu_encrypt;\n\tinst->alg.cra_blkcipher.decrypt = crypto_fpu_decrypt;\n\nout_put_alg:\n\tcrypto_mod_put(alg);\n\treturn inst;\n}\n\nstatic void crypto_fpu_free(struct crypto_instance *inst)\n{\n\tcrypto_drop_spawn(crypto_instance_ctx(inst));\n\tkfree(inst);\n}\n\nstatic struct crypto_template crypto_fpu_tmpl = {\n\t.name = \"fpu\",\n\t.alloc = crypto_fpu_alloc,\n\t.free = crypto_fpu_free,\n\t.module = THIS_MODULE,\n};\n\nint __init crypto_fpu_init(void)\n{\n\treturn crypto_register_template(&crypto_fpu_tmpl);\n}\n\nvoid __exit crypto_fpu_exit(void)\n{\n\tcrypto_unregister_template(&crypto_fpu_tmpl);\n}", "", "", "/*\n * Copyright (C) 2015 Red Hat, Inc.\n * All Rights Reserved.\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice (including the\n * next paragraph) shall be included in all copies or substantial\n * portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n * IN NO EVENT SHALL THE COPYRIGHT OWNER(S) AND/OR ITS SUPPLIERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n#include \"virtgpu_drv.h\"\n\nstatic void virtio_gpu_ttm_bo_destroy(struct ttm_buffer_object *tbo)\n{\n\tstruct virtio_gpu_object *bo;\n\tstruct virtio_gpu_device *vgdev;\n\n\tbo = container_of(tbo, struct virtio_gpu_object, tbo);\n\tvgdev = (struct virtio_gpu_device *)bo->gem_base.dev->dev_private;\n\n\tif (bo->hw_res_handle)\n\t\tvirtio_gpu_cmd_unref_resource(vgdev, bo->hw_res_handle);\n\tif (bo->pages)\n\t\tvirtio_gpu_object_free_sg_table(bo);\n\tdrm_gem_object_release(&bo->gem_base);\n\tkfree(bo);\n}\n\nstatic void virtio_gpu_init_ttm_placement(struct virtio_gpu_object *vgbo,\n\t\t\t\t\t  bool pinned)\n{\n\tu32 c = 1;\n\tu32 pflag = pinned ? TTM_PL_FLAG_NO_EVICT : 0;\n\n\tvgbo->placement.placement = &vgbo->placement_code;\n\tvgbo->placement.busy_placement = &vgbo->placement_code;\n\tvgbo->placement_code.fpfn = 0;\n\tvgbo->placement_code.lpfn = 0;\n\tvgbo->placement_code.flags =\n\t\tTTM_PL_MASK_CACHING | TTM_PL_FLAG_TT | pflag;\n\tvgbo->placement.num_placement = c;\n\tvgbo->placement.num_busy_placement = c;\n\n}\n\nint virtio_gpu_object_create(struct virtio_gpu_device *vgdev,\n\t\t\t     unsigned long size, bool kernel, bool pinned,\n\t\t\t     struct virtio_gpu_object **bo_ptr)\n{\n\tstruct virtio_gpu_object *bo;\n\tenum ttm_bo_type type;\n\tsize_t acc_size;\n\tint ret;\n\n\tif (kernel)\n\t\ttype = ttm_bo_type_kernel;\n\telse\n\t\ttype = ttm_bo_type_device;\n\t*bo_ptr = NULL;\n\n\tacc_size = ttm_bo_dma_acc_size(&vgdev->mman.bdev, size,\n\t\t\t\t       sizeof(struct virtio_gpu_object));\n\n\tbo = kzalloc(sizeof(struct virtio_gpu_object), GFP_KERNEL);\n\tif (bo == NULL)\n\t\treturn -ENOMEM;\n\tsize = roundup(size, PAGE_SIZE);\n\tret = drm_gem_object_init(vgdev->ddev, &bo->gem_base, size);\n\tif (ret != 0) {\n\t\tkfree(bo);\n\t\treturn ret;\n\t}\n\tbo->dumb = false;\n\tvirtio_gpu_init_ttm_placement(bo, pinned);\n\n\tret = ttm_bo_init(&vgdev->mman.bdev, &bo->tbo, size, type,\n\t\t\t  &bo->placement, 0, !kernel, NULL, acc_size,\n\t\t\t  NULL, NULL, &virtio_gpu_ttm_bo_destroy);\n\t/* ttm_bo_init failure will call the destroy */\n\tif (ret != 0)\n\t\treturn ret;\n\n\t*bo_ptr = bo;\n\treturn 0;\n}\n\nint virtio_gpu_object_kmap(struct virtio_gpu_object *bo, void **ptr)\n{\n\tbool is_iomem;\n\tint r;\n\n\tif (bo->vmap) {\n\t\tif (ptr)\n\t\t\t*ptr = bo->vmap;\n\t\treturn 0;\n\t}\n\tr = ttm_bo_kmap(&bo->tbo, 0, bo->tbo.num_pages, &bo->kmap);\n\tif (r)\n\t\treturn r;\n\tbo->vmap = ttm_kmap_obj_virtual(&bo->kmap, &is_iomem);\n\tif (ptr)\n\t\t*ptr = bo->vmap;\n\treturn 0;\n}\n\nint virtio_gpu_object_get_sg_table(struct virtio_gpu_device *qdev,\n\t\t\t\t   struct virtio_gpu_object *bo)\n{\n\tint ret;\n\tstruct page **pages = bo->tbo.ttm->pages;\n\tint nr_pages = bo->tbo.num_pages;\n\n\t/* wtf swapping */\n\tif (bo->pages)\n\t\treturn 0;\n\n\tif (bo->tbo.ttm->state == tt_unpopulated)\n\t\tbo->tbo.ttm->bdev->driver->ttm_tt_populate(bo->tbo.ttm);\n\tbo->pages = kmalloc(sizeof(struct sg_table), GFP_KERNEL);\n\tif (!bo->pages)\n\t\tgoto out;\n\n\tret = sg_alloc_table_from_pages(bo->pages, pages, nr_pages, 0,\n\t\t\t\t\tnr_pages << PAGE_SHIFT, GFP_KERNEL);\n\tif (ret)\n\t\tgoto out;\n\treturn 0;\nout:\n\tkfree(bo->pages);\n\tbo->pages = NULL;\n\treturn -ENOMEM;\n}\n\nvoid virtio_gpu_object_free_sg_table(struct virtio_gpu_object *bo)\n{\n\tsg_free_table(bo->pages);\n\tkfree(bo->pages);\n\tbo->pages = NULL;\n}\n\nint virtio_gpu_object_wait(struct virtio_gpu_object *bo, bool no_wait)\n{\n\tint r;\n\n\tr = ttm_bo_reserve(&bo->tbo, true, no_wait, false, NULL);\n\tif (unlikely(r != 0))\n\t\treturn r;\n\tr = ttm_bo_wait(&bo->tbo, true, true, no_wait);\n\tttm_bo_unreserve(&bo->tbo);\n\treturn r;\n}", "", "/*\n * V9FS VFS extensions.\n *\n *  Copyright (C) 2004 by <NAME> <<EMAIL>>\n *  Copyright (C) 2002 by <PERSON> <<EMAIL>>\n *\n *  This program is free software; you can redistribute it and/or modify\n *  it under the terms of the GNU General Public License version 2\n *  as published by the Free Software Foundation.\n *\n *  This program is distributed in the hope that it will be useful,\n *  but WITHOUT ANY WARRANTY; without even the implied warranty of\n *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n *  GNU General Public License for more details.\n *\n *  You should have received a copy of the GNU General Public License\n *  along with this program; if not, write to:\n *  Free Software Foundation\n *  51 Franklin Street, Fifth Floor\n *  Boston, MA  02111-1301  USA\n *\n */\n#ifndef FS_9P_V9FS_VFS_H\n#define FS_9P_V9FS_VFS_H\n\n/* plan9 semantics are that created files are implicitly opened.\n * But linux semantics are that you call create, then open.\n * the plan9 approach is superior as it provides an atomic\n * open.\n * we track the create fid here. When the file is opened, if fidopen is\n * non-zero, we use the fid and can skip some steps.\n * there may be a better way to do this, but I don't know it.\n * one BAD way is to clunk the fid on create, then open it again:\n * you lose the atomicity of file open\n */\n\n/* special case:\n * unlink calls remove, which is an implicit clunk. So we have to track\n * that kind of thing so that we don't try to clunk a dead fid.\n */\n#define P9_LOCK_TIMEOUT (30*HZ)\n\n/* flags for v9fs_stat2inode() & v9fs_stat2inode_dotl() */\n#define V9FS_STAT2INODE_KEEP_ISIZE 1\n\nextern struct file_system_type v9fs_fs_type;\nextern const struct address_space_operations v9fs_addr_operations;\nextern const struct file_operations v9fs_file_operations;\nextern const struct file_operations v9fs_file_operations_dotl;\nextern const struct file_operations v9fs_dir_operations;\nextern const struct file_operations v9fs_dir_operations_dotl;\nextern const struct dentry_operations v9fs_dentry_operations;\nextern const struct dentry_operations v9fs_cached_dentry_operations;\nextern const struct file_operations v9fs_cached_file_operations;\nextern const struct file_operations v9fs_cached_file_operations_dotl;\nextern const struct file_operations v9fs_mmap_file_operations;\nextern const struct file_operations v9fs_mmap_file_operations_dotl;\nextern struct kmem_cache *v9fs_inode_cache;\n\nstruct inode *v9fs_alloc_inode(struct super_block *sb);\nvoid v9fs_destroy_inode(struct inode *inode);\nstruct inode *v9fs_get_inode(struct super_block *sb, umode_t mode, dev_t);\nint v9fs_init_inode(struct v9fs_session_info *v9ses,\n\t\t    struct inode *inode, umode_t mode, dev_t);\nvoid v9fs_evict_inode(struct inode *inode);\nino_t v9fs_qid2ino(struct p9_qid *qid);\nvoid v9fs_stat2inode(struct p9_wstat *, struct inode *, struct super_block *);\nvoid v9fs_stat2inode_dotl(struct p9_stat_dotl *, struct inode *);\nint v9fs_dir_release(struct inode *inode, struct file *filp);\nint v9fs_file_open(struct inode *inode, struct file *file);\nvoid v9fs_inode2stat(struct inode *inode, struct p9_wstat *stat);\nint v9fs_uflags2omode(int uflags, int extended);\n\nvoid v9fs_blank_wstat(struct p9_wstat *wstat);\nint v9fs_vfs_setattr_dotl(struct dentry *, struct iattr *);\nint v9fs_file_fsync_dotl(struct file *filp, loff_t start, loff_t end,\n\t\t\t int datasync);\nint v9fs_refresh_inode(struct p9_fid *fid, struct inode *inode);\nint v9fs_refresh_inode_dotl(struct p9_fid *fid, struct inode *inode);\nstatic inline void v9fs_invalidate_inode_attr(struct inode *inode)\n{\n\tstruct v9fs_inode *v9inode;\n\tv9inode = V9FS_I(inode);\n\tv9inode->cache_validity |= V9FS_INO_INVALID_ATTR;\n\treturn;\n}\n\nint v9fs_open_to_dotl_flags(int flags);\n#endif", "// SPDX-License-Identifier: GPL-2.0+\n/*\n *  Copyright IBM Corp. 2001, 2012\n *  Author(s): <NAME>\n *\t       <NAME> (<EMAIL>)\n *\t       <NAME> <<EMAIL>>\n *\n *  Hotplug & misc device support: <NAME> (r<PERSON><PERSON><PERSON>@de.ibm.com)\n *  Major cleanup & driver split: <NAME> <sch<PERSON><PERSON><EMAIL>>\n *\t\t\t\t  <NAME> <<EMAIL>>\n *  MSGTYPE restruct:\t\t  <NAME> <<EMAIL>>\n */\n\n#include <linux/module.h>\n#include <linux/init.h>\n#include <linux/interrupt.h>\n#include <linux/miscdevice.h>\n#include <linux/fs.h>\n#include <linux/proc_fs.h>\n#include <linux/seq_file.h>\n#include <linux/compat.h>\n#include <linux/slab.h>\n#include <linux/atomic.h>\n#include <linux/uaccess.h>\n#include <linux/hw_random.h>\n#include <linux/debugfs.h>\n#include <asm/debug.h>\n\n#include \"zcrypt_debug.h\"\n#include \"zcrypt_api.h\"\n\n#include \"zcrypt_msgtype6.h\"\n#include \"zcrypt_msgtype50.h\"\n\n/*\n * Device attributes common for all crypto card devices.\n */\n\nstatic ssize_t type_show(struct device *dev,\n\t\t\t struct device_attribute *attr, char *buf)\n{\n\tstruct zcrypt_card *zc = to_ap_card(dev)->private;\n\n\treturn scnprintf(buf, PAGE_SIZE, \"%s\\n\", zc->type_string);\n}\n\nstatic DEVICE_ATTR_RO(type);\n\nstatic ssize_t online_show(struct device *dev,\n\t\t\t   struct device_attribute *attr,\n\t\t\t   char *buf)\n{\n\tstruct ap_card *ac = to_ap_card(dev);\n\tstruct zcrypt_card *zc = ac->private;\n\tint online = ac->config && zc->online ? 1 : 0;\n\n\treturn scnprintf(buf, PAGE_SIZE, \"%d\\n\", online);\n}\n\nstatic ssize_t online_store(struct device *dev,\n\t\t\t    struct device_attribute *attr,\n\t\t\t    const char *buf, size_t count)\n{\n\tstruct ap_card *ac = to_ap_card(dev);\n\tstruct zcrypt_card *zc = ac->private;\n\tstruct zcrypt_queue *zq;\n\tint online, id;\n\n\tif (sscanf(buf, \"%d\\n\", &online) != 1 || online < 0 || online > 1)\n\t\treturn -EINVAL;\n\n\tif (online && !ac->config)\n\t\treturn -ENODEV;\n\n\tzc->online = online;\n\tid = zc->card->id;\n\n\tZCRYPT_DBF(DBF_INFO, \"card=%02x online=%d\\n\", id, online);\n\n\tspin_lock(&zcrypt_list_lock);\n\tlist_for_each_entry(zq, &zc->zqueues, list)\n\t\tzcrypt_queue_force_online(zq, online);\n\tspin_unlock(&zcrypt_list_lock);\n\treturn count;\n}\n\nstatic DEVICE_ATTR_RW(online);\n\nstatic ssize_t load_show(struct device *dev,\n\t\t\t struct device_attribute *attr,\n\t\t\t char *buf)\n{\n\tstruct zcrypt_card *zc = to_ap_card(dev)->private;\n\n\treturn scnprintf(buf, PAGE_SIZE, \"%d\\n\", atomic_read(&zc->load));\n}\n\nstatic DEVICE_ATTR_RO(load);\n\nstatic struct attribute *zcrypt_card_attrs[] = {\n\t&dev_attr_type.attr,\n\t&dev_attr_online.attr,\n\t&dev_attr_load.attr,\n\tNULL,\n};\n\nstatic const struct attribute_group zcrypt_card_attr_group = {\n\t.attrs = zcrypt_card_attrs,\n};\n\nstruct zcrypt_card *zcrypt_card_alloc(void)\n{\n\tstruct zcrypt_card *zc;\n\n\tzc = kzalloc(sizeof(struct zcrypt_card), GFP_KERNEL);\n\tif (!zc)\n\t\treturn NULL;\n\tINIT_LIST_HEAD(&zc->list);\n\tINIT_LIST_HEAD(&zc->zqueues);\n\tkref_init(&zc->refcount);\n\treturn zc;\n}\nEXPORT_SYMBOL(zcrypt_card_alloc);\n\nvoid zcrypt_card_free(struct zcrypt_card *zc)\n{\n\tkfree(zc);\n}\nEXPORT_SYMBOL(zcrypt_card_free);\n\nstatic void zcrypt_card_release(struct kref *kref)\n{\n\tstruct zcrypt_card *zdev =\n\t\tcontainer_of(kref, struct zcrypt_card, refcount);\n\tzcrypt_card_free(zdev);\n}\n\nvoid zcrypt_card_get(struct zcrypt_card *zc)\n{\n\tkref_get(&zc->refcount);\n}\nEXPORT_SYMBOL(zcrypt_card_get);\n\nint zcrypt_card_put(struct zcrypt_card *zc)\n{\n\treturn kref_put(&zc->refcount, zcrypt_card_release);\n}\nEXPORT_SYMBOL(zcrypt_card_put);\n\n/**\n * zcrypt_card_register() - Register a crypto card device.\n * @zc: Pointer to a crypto card device\n *\n * Register a crypto card device. Returns 0 if successful.\n */\nint zcrypt_card_register(struct zcrypt_card *zc)\n{\n\tint rc;\n\n\tspin_lock(&zcrypt_list_lock);\n\tlist_add_tail(&zc->list, &zcrypt_card_list);\n\tspin_unlock(&zcrypt_list_lock);\n\n\tzc->online = 1;\n\n\tZCRYPT_DBF(DBF_INFO, \"card=%02x register online=1\\n\", zc->card->id);\n\n\trc = sysfs_create_group(&zc->card->ap_dev.device.kobj,\n\t\t\t\t&zcrypt_card_attr_group);\n\tif (rc) {\n\t\tspin_lock(&zcrypt_list_lock);\n\t\tlist_del_init(&zc->list);\n\t\tspin_unlock(&zcrypt_list_lock);\n\t}\n\n\treturn rc;\n}\nEXPORT_SYMBOL(zcrypt_card_register);\n\n/**\n * zcrypt_card_unregister(): Unregister a crypto card device.\n * @zc: Pointer to crypto card device\n *\n * Unregister a crypto card device.\n */\nvoid zcrypt_card_unregister(struct zcrypt_card *zc)\n{\n\tZCRYPT_DBF(DBF_INFO, \"card=%02x unregister\\n\", zc->card->id);\n\n\tspin_lock(&zcrypt_list_lock);\n\tlist_del_init(&zc->list);\n\tspin_unlock(&zcrypt_list_lock);\n\tsysfs_remove_group(&zc->card->ap_dev.device.kobj,\n\t\t\t   &zcrypt_card_attr_group);\n}\nEXPORT_SYMBOL(zcrypt_card_unregister);", "/* dvb-usb-firmware.c is part of the DVB USB library.\n *\n * Copyright (C) 2004-6 <NAME> (patrick.boett<PERSON>@posteo.de)\n * see dvb-usb-init.c for copyright information.\n *\n * This file contains functions for downloading the firmware to Cypress FX 1 and 2 based devices.\n *\n * FIXME: This part does actually not belong to dvb-usb, but to the usb-subsystem.\n */\n#include \"dvb-usb-common.h\"\n\n#include <linux/usb.h>\n\nstruct usb_cypress_controller {\n\tint id;\n\tconst char *name;       /* name of the usb controller */\n\tu16 cpu_cs_register;    /* needs to be restarted, when the firmware has been downloaded. */\n};\n\nstatic struct usb_cypress_controller cypress[] = {\n\t{ .id = DEVICE_SPECIFIC, .name = \"Device specific\", .cpu_cs_register = 0 },\n\t{ .id = CYPRESS_AN2135,  .name = \"Cypress AN2135\",  .cpu_cs_register = 0x7f92 },\n\t{ .id = CYPRESS_AN2235,  .name = \"Cypress AN2235\",  .cpu_cs_register = 0x7f92 },\n\t{ .id = CYPRESS_FX2,     .name = \"Cypress FX2\",     .cpu_cs_register = 0xe600 },\n};\n\n/*\n * load a firmware packet to the device\n */\nstatic int usb_cypress_writemem(struct usb_device *udev,u16 addr,u8 *data, u8 len)\n{\n\treturn usb_control_msg(udev, usb_sndctrlpipe(udev,0),\n\t\t\t0xa0, USB_TYPE_VENDOR, addr, 0x00, data, len, 5000);\n}\n\nint usb_cypress_load_firmware(struct usb_device *udev, const struct firmware *fw, int type)\n{\n\tstruct hexline *hx;\n\tu8 *buf;\n\tint ret, pos = 0;\n\tu16 cpu_cs_register = cypress[type].cpu_cs_register;\n\n\tbuf = kmalloc(sizeof(*hx), GFP_KERNEL);\n\tif (!buf)\n\t\treturn -ENOMEM;\n\thx = (struct hexline *)buf;\n\n\t/* stop the CPU */\n\tbuf[0] = 1;\n\tif (usb_cypress_writemem(udev, cpu_cs_register, buf, 1) != 1)\n\t\terr(\"could not stop the USB controller CPU.\");\n\n\twhile ((ret = dvb_usb_get_hexline(fw, hx, &pos)) > 0) {\n\t\tdeb_fw(\"writing to address 0x%04x (buffer: 0x%02x %02x)\\n\", hx->addr, hx->len, hx->chk);\n\t\tret = usb_cypress_writemem(udev, hx->addr, hx->data, hx->len);\n\n\t\tif (ret != hx->len) {\n\t\t\terr(\"error while transferring firmware (transferred size: %d, block size: %d)\",\n\t\t\t\tret, hx->len);\n\t\t\tret = -EINVAL;\n\t\t\tbreak;\n\t\t}\n\t}\n\tif (ret < 0) {\n\t\terr(\"firmware download failed at %d with %d\",pos,ret);\n\t\tkfree(buf);\n\t\treturn ret;\n\t}\n\n\tif (ret == 0) {\n\t\t/* restart the CPU */\n\t\tbuf[0] = 0;\n\t\tif (usb_cypress_writemem(udev, cpu_cs_register, buf, 1) != 1) {\n\t\t\terr(\"could not restart the USB controller CPU.\");\n\t\t\tret = -EINVAL;\n\t\t}\n\t} else\n\t\tret = -EIO;\n\n\tkfree(buf);\n\n\treturn ret;\n}\nEXPORT_SYMBOL(usb_cypress_load_firmware);\n\nint dvb_usb_download_firmware(struct usb_device *udev, struct dvb_usb_device_properties *props)\n{\n\tint ret;\n\tconst struct firmware *fw = NULL;\n\n\tif ((ret = request_firmware(&fw, props->firmware, &udev->dev)) != 0) {\n\t\terr(\"did not find the firmware file. (%s) Please see linux/Documentation/dvb/ for more details on firmware-problems. (%d)\",\n\t\t\tprops->firmware,ret);\n\t\treturn ret;\n\t}\n\n\tinfo(\"downloading firmware from file '%s'\",props->firmware);\n\n\tswitch (props->usb_ctrl) {\n\t\tcase CYPRESS_AN2135:\n\t\tcase CYPRESS_AN2235:\n\t\tcase CYPRESS_FX2:\n\t\t\tret = usb_cypress_load_firmware(udev, fw, props->usb_ctrl);\n\t\t\tbreak;\n\t\tcase DEVICE_SPECIFIC:\n\t\t\tif (props->download_firmware)\n\t\t\t\tret = props->download_firmware(udev,fw);\n\t\t\telse {\n\t\t\t\terr(\"BUG: driver didn't specified a download_firmware-callback, although it claims to have a DEVICE_SPECIFIC one.\");\n\t\t\t\tret = -EINVAL;\n\t\t\t}\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tret = -EINVAL;\n\t\t\tbreak;\n\t}\n\n\trelease_firmware(fw);\n\treturn ret;\n}\n\nint dvb_usb_get_hexline(const struct firmware *fw, struct hexline *hx,\n\t\t\t       int *pos)\n{\n\tu8 *b = (u8 *) &fw->data[*pos];\n\tint data_offs = 4;\n\tif (*pos >= fw->size)\n\t\treturn 0;\n\n\tmemset(hx,0,sizeof(struct hexline));\n\n\thx->len  = b[0];\n\n\tif ((*pos + hx->len + 4) >= fw->size)\n\t\treturn -EINVAL;\n\n\thx->addr = b[1] | (b[2] << 8);\n\thx->type = b[3];\n\n\tif (hx->type == 0x04) {\n\t\t/* b[4] and b[5] are the Extended linear address record data field */\n\t\thx->addr |= (b[4] << 24) | (b[5] << 16);\n// ... 函数内容截断 ...\n\t\tcase CYPRESS_FX2:\n\t\t\tret = usb_cypress_load_firmware(udev, fw, props->usb_ctrl);\n\t\t\tbreak;\n\t\tcase DEVICE_SPECIFIC:\n\t\t\tif (props->download_firmware)\n\t\t\t\tret = props->download_firmware(udev,fw);\n\t\t\telse {\n\t\t\t\terr(\"BUG: driver didn't specified a download_firmware-callback, although it claims to have a DEVICE_SPECIFIC one.\");\n\t\t\t\tret = -EINVAL;\n\t\t\t}\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tret = -EINVAL;\n\t\t\tbreak;\n\t}\n\n\trelease_firmware(fw);\n\treturn ret;\n}\n\nint dvb_usb_get_hexline(const struct firmware *fw, struct hexline *hx,\n\t\t\t       int *pos)\n{\n\tu8 *b = (u8 *) &fw->data[*pos];\n\tint data_offs = 4;\n\tif (*pos >= fw->size)\n\t\treturn 0;\n\n\tmemset(hx,0,sizeof(struct hexline));\n\n\thx->len  = b[0];\n\n\tif ((*pos + hx->len + 4) >= fw->size)\n\t\treturn -EINVAL;\n\n\thx->addr = b[1] | (b[2] << 8);\n\thx->type = b[3];\n\n\tif (hx->type == 0x04) {\n\t\t/* b[4] and b[5] are the Extended linear address record data field */\n\t\thx->addr |= (b[4] << 24) | (b[5] << 16);\n/*\t\thx->len -= 2;\n\t\tdata_offs += 2; */\n\t}\n\tmemcpy(hx->data,&b[data_offs],hx->len);\n\thx->chk = b[hx->len + data_offs];\n\n\t*pos += hx->len + 5;\n\n\treturn *pos;\n}\nEXPORT_SYMBOL(dvb_usb_get_hexline);", "", "// SPDX-License-Identifier: GPL-2.0\n/*\n * sun8i-ss-prng.c - hardware cryptographic offloader for\n * Allwinner A80/A83T SoC\n *\n * Copyright (C) 2015-2020 <PERSON><PERSON> <<EMAIL>>\n *\n * This file handle the PRNG found in the SS\n *\n * You could find a link for the datasheet in Documentation/arm/sunxi.rst\n */\n#include \"sun8i-ss.h\"\n#include <linux/dma-mapping.h>\n#include <linux/pm_runtime.h>\n#include <crypto/internal/rng.h>\n\nint sun8i_ss_prng_seed(struct crypto_rng *tfm, const u8 *seed,\n\t\t       unsigned int slen)\n{\n\tstruct sun8i_ss_rng_tfm_ctx *ctx = crypto_rng_ctx(tfm);\n\n\tif (ctx->seed && ctx->slen != slen) {\n\t\tmemzero_explicit(ctx->seed, ctx->slen);\n\t\tkfree(ctx->seed);\n\t\tctx->slen = 0;\n\t\tctx->seed = NULL;\n\t}\n\tif (!ctx->seed)\n\t\tctx->seed = kmalloc(slen, GFP_KERNEL | GFP_DMA);\n\tif (!ctx->seed)\n\t\treturn -ENOMEM;\n\n\tmemcpy(ctx->seed, seed, slen);\n\tctx->slen = slen;\n\n\treturn 0;\n}\n\nint sun8i_ss_prng_init(struct crypto_tfm *tfm)\n{\n\tstruct sun8i_ss_rng_tfm_ctx *ctx = crypto_tfm_ctx(tfm);\n\n\tmemset(ctx, 0, sizeof(struct sun8i_ss_rng_tfm_ctx));\n\treturn 0;\n}\n\nvoid sun8i_ss_prng_exit(struct crypto_tfm *tfm)\n{\n\tstruct sun8i_ss_rng_tfm_ctx *ctx = crypto_tfm_ctx(tfm);\n\n\tmemzero_explicit(ctx->seed, ctx->slen);\n\tkfree(ctx->seed);\n\tctx->seed = NULL;\n\tctx->slen = 0;\n}\n\nint sun8i_ss_prng_generate(struct crypto_rng *tfm, const u8 *src,\n\t\t\t   unsigned int slen, u8 *dst, unsigned int dlen)\n{\n\tstruct sun8i_ss_rng_tfm_ctx *ctx = crypto_rng_ctx(tfm);\n\tstruct rng_alg *alg = crypto_rng_alg(tfm);\n\tstruct sun8i_ss_alg_template *algt;\n\tstruct sun8i_ss_dev *ss;\n\tdma_addr_t dma_iv, dma_dst;\n\tunsigned int todo;\n\tint err = 0;\n\tint flow;\n\tvoid *d;\n\tu32 v;\n\n\talgt = container_of(alg, struct sun8i_ss_alg_template, alg.rng);\n\tss = algt->ss;\n\n\tif (ctx->slen == 0) {\n\t\tdev_err(ss->dev, \"The PRNG is not seeded\\n\");\n\t\treturn -EINVAL;\n\t}\n\n\t/* The SS does not give an updated seed, so we need to get a new one.\n\t * So we will ask for an extra PRNG_SEED_SIZE data.\n\t * We want dlen + seedsize rounded up to a multiple of PRNG_DATA_SIZE\n\t */\n\ttodo = dlen + PRNG_SEED_SIZE + PRNG_DATA_SIZE;\n\ttodo -= todo % PRNG_DATA_SIZE;\n\n\td = kzalloc(todo, GFP_KERNEL | GFP_DMA);\n\tif (!d)\n\t\treturn -ENOMEM;\n\n\tflow = sun8i_ss_get_engine_number(ss);\n\n#ifdef CONFIG_CRYPTO_DEV_SUN8I_SS_DEBUG\n\talgt->stat_req++;\n\talgt->stat_bytes += todo;\n#endif\n\n\tv = SS_ALG_PRNG | SS_PRNG_CONTINUE | SS_START;\n\tif (flow)\n\t\tv |= SS_FLOW1;\n\telse\n\t\tv |= SS_FLOW0;\n\n\tdma_iv = dma_map_single(ss->dev, ctx->seed, ctx->slen, DMA_TO_DEVICE);\n\tif (dma_mapping_error(ss->dev, dma_iv)) {\n\t\tdev_err(ss->dev, \"Cannot DMA MAP IV\\n\");\n\t\terr = -EFAULT;\n\t\tgoto err_free;\n\t}\n\n\tdma_dst = dma_map_single(ss->dev, d, todo, DMA_FROM_DEVICE);\n\tif (dma_mapping_error(ss->dev, dma_dst)) {\n\t\tdev_err(ss->dev, \"Cannot DMA MAP DST\\n\");\n\t\terr = -EFAULT;\n\t\tgoto err_iv;\n\t}\n\n\terr = pm_runtime_get_sync(ss->dev);\n\tif (err < 0) {\n\t\tpm_runtime_put_noidle(ss->dev);\n\t\tgoto err_pm;\n\t}\n\terr = 0;\n\n\tmutex_lock(&ss->mlock);\n\twritel(dma_iv, ss->base + SS_IV_ADR_REG);\n\t/* the PRNG act badly (failing rngtest) without SS_KEY_ADR_REG set */\n\twritel(dma_iv, ss->base + SS_KEY_ADR_REG);\n\twritel(dma_dst, ss->base + SS_DST_ADR_REG);\n\twritel(todo / 4, ss->base + SS_LEN_ADR_REG);\n\n\treinit_completion(&ss->flows[flow].complete);\n\tss->flows[flow].status = 0;\n\t/* Be sure all data is written before enabling the task */\n\twmb();\n\n\twritel(v, ss->base + SS_CTL_REG);\n\n\twait_for_completion_interruptible_timeout(&ss->flows[flow].complete,\n\t\t\t\t\t\t  msecs_to_jiffies(todo));\n\tif (ss->flows[flow].status == 0) {\n\t\tdev_err(ss->dev, \"DMA timeout for PRNG (size=%u)\\n\", todo);\n\t\terr = -EFAULT;\n\t}\n\t/* Since cipher and hash use the linux/cryptoengine and that we have\n\t * a cryptoengine per flow, we are sure that they will issue only one\n\t * request per flow.\n\t * Since the cryptoengine wait for completion before submitting a new\n\t * one, the mlock could be left just after the final writel.\n\t * But cryptoengine cannot handle crypto_rng, so we need to be sure\n\t * nothing will use our flow.\n\t * The easiest way is to grab mlock until the hardware end our requests.\n\t * We could have used a per flow lock, but this would increase\n\t * complexity.\n\t * The drawback is that no request could be handled for the other flow.\n\t */\n\tmutex_unlock(&ss->mlock);\n\n\tpm_runtime_put(ss->dev);\n\nerr_pm:\n\tdma_unmap_single(ss->dev, dma_dst, todo, DMA_FROM_DEVICE);\nerr_iv:\n\tdma_unmap_single(ss->dev, dma_iv, ctx->slen, DMA_TO_DEVICE);\n\n\tif (!err) {\n\t\tmemcpy(dst, d, dlen);\n\t\t/* Update seed */\n\t\tmemcpy(ctx->seed, d + dlen, ctx->slen);\n\t}\n\tmemzero_explicit(d, todo);\n\tkfree(d);\n\n\treturn err;\n}", "", "", "// SPDX-License-Identifier: GPL-2.0\n/*\n * AMD Encrypted Register State Support\n *\n * Author: <NAME> <<EMAIL>>\n */\n\n/*\n * misc.h needs to be first because it knows how to include the other kernel\n * headers in the pre-decompression code in a way that does not break\n * compilation.\n */\n#include \"misc.h\"\n\n#include <asm/pgtable_types.h>\n#include <asm/sev.h>\n#include <asm/trapnr.h>\n#include <asm/trap_pf.h>\n#include <asm/msr-index.h>\n#include <asm/fpu/xcr.h>\n#include <asm/ptrace.h>\n#include <asm/svm.h>\n\n#include \"error.h\"\n\nstruct ghcb boot_ghcb_page __aligned(PAGE_SIZE);\nstruct ghcb *boot_ghcb;\n\n/*\n * Copy a version of this function here - insn-eval.c can't be used in\n * pre-decompression code.\n */\nstatic bool insn_has_rep_prefix(struct insn *insn)\n{\n\tinsn_byte_t p;\n\tint i;\n\n\tinsn_get_prefixes(insn);\n\n\tfor_each_insn_prefix(insn, i, p) {\n\t\tif (p == 0xf2 || p == 0xf3)\n\t\t\treturn true;\n\t}\n\n\treturn false;\n}\n\n/*\n * Only a dummy for insn_get_seg_base() - Early boot-code is 64bit only and\n * doesn't use segments.\n */\nstatic unsigned long insn_get_seg_base(struct pt_regs *regs, int seg_reg_idx)\n{\n\treturn 0UL;\n}\n\nstatic inline u64 sev_es_rd_ghcb_msr(void)\n{\n\tunsigned long low, high;\n\n\tasm volatile(\"rdmsr\" : \"=a\" (low), \"=d\" (high) :\n\t\t\t\"c\" (MSR_AMD64_SEV_ES_GHCB));\n\n\treturn ((high << 32) | low);\n}\n\nstatic inline void sev_es_wr_ghcb_msr(u64 val)\n{\n\tu32 low, high;\n\n\tlow  = val & 0xffffffffUL;\n\thigh = val >> 32;\n\n\tasm volatile(\"wrmsr\" : : \"c\" (MSR_AMD64_SEV_ES_GHCB),\n\t\t\t\"a\"(low), \"d\" (high) : \"memory\");\n}\n\nstatic enum es_result vc_decode_insn(struct es_em_ctxt *ctxt)\n{\n\tchar buffer[MAX_INSN_SIZE];\n\tint ret;\n\n\tmemcpy(buffer, (unsigned char *)ctxt->regs->ip, MAX_INSN_SIZE);\n\n\tret = insn_decode(&ctxt->insn, buffer, MAX_INSN_SIZE, INSN_MODE_64);\n\tif (ret < 0)\n\t\treturn ES_DECODE_FAILED;\n\n\treturn ES_OK;\n}\n\nstatic enum es_result vc_write_mem(struct es_em_ctxt *ctxt,\n\t\t\t\t   void *dst, char *buf, size_t size)\n{\n\tmemcpy(dst, buf, size);\n\n\treturn ES_OK;\n}\n\nstatic enum es_result vc_read_mem(struct es_em_ctxt *ctxt,\n\t\t\t\t  void *src, char *buf, size_t size)\n{\n\tmemcpy(buf, src, size);\n\n\treturn ES_OK;\n}\n\nstatic enum es_result vc_ioio_check(struct es_em_ctxt *ctxt, u16 port, size_t size)\n{\n\treturn ES_OK;\n}\n\nstatic bool fault_in_kernel_space(unsigned long address)\n{\n\treturn false;\n}\n\n#undef __init\n#undef __pa\n#define __init\n#define __pa(x)\t((unsigned long)(x))\n\n#define __BOOT_COMPRESSED\n\n/* Basic instruction decoding support needed */\n#include \"../../lib/inat.c\"\n#include \"../../lib/insn.c\"\n\n/* Include code for early handlers */\n#include \"../../kernel/sev-shared.c\"\n\nstatic bool early_setup_sev_es(void)\n{\n\tif (!sev_es_negotiate_protocol())\n\t\tsev_es_terminate(GHCB_SEV_ES_REASON_PROTOCOL_UNSUPPORTED);\n\n\tif (set_page_decrypted((unsigned long)&boot_ghcb_page))\n\t\treturn false;\n\n\t/* Page is now mapped decrypted, clear it */\n\tmemset(&boot_ghcb_page, 0, sizeof(boot_ghcb_page));\n\n\tboot_ghcb = &boot_ghcb_page;\n\n\t/* Initialize lookup tables for the instruction decoder */\n\tinat_init_tables();\n\n\treturn true;\n}\n\nvoid sev_es_shutdown_ghcb(void)\n{\n\tif (!boot_ghcb)\n\t\treturn;\n\n\tif (!sev_es_check_cpu_features())\n\t\terror(\"SEV-ES CPU Features missing.\");\n\n\t/*\n\t * GHCB Page must be flushed from the cache and mapped encrypted again.\n\t * Otherwise the running kernel will see strange cache effects when\n\t * trying to use that page.\n\t */\n\tif (set_page_encrypted((unsigned long)&boot_ghcb_page))\n\t\terror(\"Can't map GHCB page encrypted\");\n\n\t/*\n\t * GHCB page is mapped encrypted again and flushed from the cache.\n\t * Mark it non-present now to catch bugs when #VC exceptions trigger\n\t * after this point.\n\t */\n\tif (set_page_non_present((unsigned long)&boot_ghcb_page))\n\t\terror(\"Can't unmap GHCB page\");\n}\n\nbool sev_es_check_ghcb_fault(unsigned long address)\n{\n\t/* Check whether the fault was on the GHCB page */\n\treturn ((address & PAGE_MASK) == (unsigned long)&boot_ghcb_page);\n}\n\nvoid do_boot_stage2_vc(struct pt_regs *regs, unsigned long exit_code)\n{\n\tstruct es_em_ctxt ctxt;\n\tenum es_result result;\n\n\tif (!boot_ghcb && !early_setup_sev_es())\n\t\tsev_es_terminate(GHCB_SEV_ES_REASON_GENERAL_REQUEST);\n\n\tvc_ghcb_invalidate(boot_ghcb);\n\tresult = vc_init_em_ctxt(&ctxt, regs, exit_code);\n\tif (result != ES_OK)\n\t\tgoto finish;\n\n\tswitch (exit_code) {\n\tcase SVM_EXIT_RDTSC:\n\tcase SVM_EXIT_RDTSCP:\n\t\tresult = vc_handle_rdtsc(boot_ghcb, &ctxt, exit_code);\n\t\tbreak;\n\tcase SVM_EXIT_IOIO:\n\t\tresult = vc_handle_ioio(boot_ghcb, &ctxt);\n\t\tbreak;\n\tcase SVM_EXIT_CPUID:\n\t\tresult = vc_handle_cpuid(boot_ghcb, &ctxt);\n\t\tbreak;\n\tdefault:\n\t\tresult = ES_UNSUPPORTED;\n\t\tbreak;\n\t}\n\nfinish:\n\tif (result == ES_OK) {\n\t\tvc_finish_insn(&ctxt);\n\t} else if (result != ES_RETRY) {\n\t\t/*\n\t\t * For now, just halt the machine. That makes debugging easier,\n\t\t * later we just call sev_es_terminate() here.\n\t\t */\n\t\twhile (true)\n\t\t\tasm volatile(\"hlt\\n\");\n\t}\n}", "/* packet-rgmp.c\n * Routines for IGMP/RGMP packet disassembly\n * Copyright 2006 Jaap <PERSON>\n *\n * Wireshark - Network traffic analyzer\n * By <PERSON> <<EMAIL>>\n * Copyright 1998 <PERSON>\n *\n * This program is free software; you can redistribute it and/or\n * modify it under the terms of the GNU General Public License\n * as published by the Free Software Foundation; either version 2\n * of the License, or (at your option) any later version.\n *\n * This program is distributed in the hope that it will be useful,\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n * GNU General Public License for more details.\n *\n * You should have received a copy of the GNU General Public License\n * along with this program; if not, write to the Free Software\n * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.\n */\n\n/*\n Based on RFC3488\n\n This is a setup for RGMP dissection, a simple protocol bolted on IGMP.\n */\n\n#include \"config.h\"\n\n#include <epan/packet.h>\n#include <epan/expert.h>\n#include \"packet-igmp.h\"\n\nvoid proto_register_rgmp(void);\nvoid proto_reg_handoff_rgmp(void);\n\nstatic int proto_rgmp      = -1;\nstatic int hf_type         = -1;\nstatic int hf_reserved     = -1;\nstatic int hf_checksum     = -1;\nstatic int hf_checksum_status = -1;\nstatic int hf_maddr        = -1;\n\nstatic int ett_rgmp = -1;\n\nstatic expert_field ei_checksum = EI_INIT;\n\nstatic dissector_handle_t rgmp_handle;\n\n#define MC_RGMP 0xe0000019\n\nstatic const value_string rgmp_types[] = {\n    {IGMP_RGMP_LEAVE, \"Leave\"},\n    {IGMP_RGMP_JOIN,  \"Join\"},\n    {IGMP_RGMP_BYE,   \"Bye\"},\n    {IGMP_RGMP_HELLO, \"Hello\"},\n    {0, NULL}\n};\n\n/* This function is only called from the IGMP dissector */\nstatic int\ndissect_rgmp(tvbuff_t *tvb, packet_info *pinfo, proto_tree *parent_tree, void* data _U_)\n{\n    proto_tree *tree;\n    proto_item *item;\n    guint8 type;\n    int offset = 0;\n    guint32 dst = g_htonl(MC_RGMP);\n\n    /* Shouldn't be destined for us */\n    if ((pinfo->dst.type != AT_IPv4) || memcmp(pinfo->dst.data, &dst, 4))\n        return 0;\n\n    col_set_str(pinfo->cinfo, COL_PROTOCOL, \"RGMP\");\n    col_clear(pinfo->cinfo, COL_INFO);\n\n    item = proto_tree_add_item(parent_tree, proto_rgmp, tvb, offset, -1, ENC_NA);\n    tree = proto_item_add_subtree(item, ett_rgmp);\n\n    type = tvb_get_guint8(tvb, offset);\n    col_add_str(pinfo->cinfo, COL_INFO,\n                val_to_str(type, rgmp_types, \"Unknown Type: 0x%02x\"));\n    proto_tree_add_uint(tree, hf_type, tvb, offset, 1, type);\n    offset += 1;\n\n    /* reserved */\n    proto_tree_add_item(tree, hf_reserved, tvb, offset, 1, ENC_NA);\n    offset += 1;\n\n    igmp_checksum(tree, tvb, hf_checksum, hf_checksum_status, &ei_checksum, pinfo, 0);\n    offset += 2;\n\n    proto_tree_add_item(tree, hf_maddr, tvb, offset, 4, ENC_BIG_ENDIAN);\n    offset += 4;\n\n    return offset;\n}\n\n\nvoid\nproto_register_rgmp(void)\n{\n    static hf_register_info hf[] = {\n        { &hf_type,\n          { \"Type\", \"rgmp.type\", FT_UINT8, BASE_HEX,\n            VALS(rgmp_types), 0, \"RGMP Packet Type\", HFILL }\n        },\n\n        { &hf_reserved,\n          { \"Reserved\", \"rgmp.reserved\", FT_UINT8, BASE_HEX,\n            NULL, 0, \"RGMP Reserved\", HFILL }\n        },\n\n        { &hf_checksum,\n          { \"Checksum\", \"rgmp.checksum\", FT_UINT16, BASE_HEX,\n            NULL, 0, NULL, HFILL }\n        },\n\n        { &hf_checksum_status,\n          { \"Checksum Status\", \"rgmp.checksum.status\", FT_UINT8, BASE_NONE,\n            VALS(proto_checksum_vals), 0x0, NULL, HFILL }\n        },\n\n        { &hf_maddr,\n          { \"Multicast group address\", \"rgmp.maddr\", FT_IPv4, BASE_NONE,\n            NULL, 0, NULL, HFILL }\n        }\n    };\n\n    static gint *ett[] = {\n        &ett_rgmp\n    };\n\n    static ei_register_info ei[] = {\n        { &ei_checksum, { \"rgmp.bad_checksum\", PI_CHECKSUM, PI_ERROR, \"Bad checksum\", EXPFILL }},\n    };\n\n    expert_module_t* expert_rgmp;\n\n    proto_rgmp = proto_register_protocol(\"Router-port Group Management Protocol\", \"RGMP\", \"rgmp\");\n    proto_register_field_array(proto_rgmp, hf, array_length(hf));\n    proto_register_subtree_array(ett, array_length(ett));\n    expert_rgmp = expert_register_protocol(proto_rgmp);\n    expert_register_field_array(expert_rgmp, ei, array_length(ei));\n\n    rgmp_handle = register_dissector(\"rgmp\", dissect_rgmp, proto_rgmp);\n}\n\nvoid\nproto_reg_handoff_rgmp(void)\n{\n    dissector_add_uint(\"igmp.type\", IGMP_RGMP_HELLO, rgmp_handle);\n    dissector_add_uint(\"igmp.type\", IGMP_RGMP_BYE, rgmp_handle);\n    dissector_add_uint(\"igmp.type\", IGMP_RGMP_JOIN, rgmp_handle);\n    dissector_add_uint(\"igmp.type\", IGMP_RGMP_LEAVE, rgmp_handle);\n}\n\n/*\n * Editor modelines  -  http://www.wireshark.org/tools/modelines.html\n *\n * Local variables:\n * c-basic-offset: 4\n * tab-width: 8\n * indent-tabs-mode: nil\n * End:\n *\n * vi: set shiftwidth=4 tabstop=8 expandtab:\n * :indentSize=4:tabSize=8:noTabs=true:\n */", "", "", "/*\n * Copyright (c) 2010, Oracle America, Inc.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *     * Redistributions of source code must retain the above copyright\n *       notice, this list of conditions and the following disclaimer.\n *     * Redistributions in binary form must reproduce the above\n *       copyright notice, this list of conditions and the following\n *       disclaimer in the documentation and/or other materials\n *       provided with the distribution.\n *     * Neither the name of the \"Oracle America, Inc.\" nor the names of its\n *       contributors may be used to endorse or promote products derived\n *       from this software without specific prior written permission.\n *\n *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n *   \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n *   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS\n *   FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE\n *   COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n *   DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE\n *   GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n *   INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *   WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n *   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n *   OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n#include <alloca.h>\n#include <errno.h>\n#include <string.h>\n#include <rpc/rpc.h>\n#include <sys/socket.h>\n#include <netdb.h>\n#include <shlib-compat.h>\n\n/*\n * Generic client creation: takes (hostname, program-number, protocol) and\n * returns client handle. Default options are set, which the user can\n * change using the rpc equivalent of ioctl()'s.\n */\nCLIENT *\nclnt_create (const char *hostname, u_long prog, u_long vers,\n\t     const char *proto)\n{\n  struct protoent protobuf, *p;\n  size_t prtbuflen;\n  char *prttmpbuf;\n  struct sockaddr_in sin;\n  struct sockaddr_un sun;\n  int sock;\n  struct timeval tv;\n  CLIENT *client;\n\n  if (strcmp (proto, \"unix\") == 0)\n    {\n      if (__sockaddr_un_set (&sun, hostname) < 0)\n\t{\n\t  struct rpc_createerr *ce = &get_rpc_createerr ();\n\t  ce->cf_stat = RPC_SYSTEMERROR;\n\t  ce->cf_error.re_errno = errno;\n\t  return NULL;\n\t}\n      sock = RPC_ANYSOCK;\n      client = clntunix_create (&sun, prog, vers, &sock, 0, 0);\n      if (client == NULL)\n\treturn NULL;\n#if 0\n      /* This is not wanted.  This would disable the user from having\n\t a timeout in the clnt_call() call.  Only a call to cnlt_control()\n\t by the user should set the timeout value.  */\n      tv.tv_sec = 25;\n      tv.tv_usec = 0;\n      clnt_control (client, CLSET_TIMEOUT, (char *)&tv);\n#endif\n      return client;\n    }\n\n  if (__libc_rpc_gethostbyname (hostname, &sin) != 0)\n    return NULL;\n\n  prtbuflen = 1024;\n  prttmpbuf = __alloca (prtbuflen);\n  while (__getprotobyname_r (proto, &protobuf, prttmpbuf, prtbuflen, &p) != 0\n\t || p == NULL)\n    if (errno != ERANGE)\n      {\n\tstruct rpc_createerr *ce = &get_rpc_createerr ();\n\tce->cf_stat = RPC_UNKNOWNPROTO;\n\tce->cf_error.re_errno = EPFNOSUPPORT;\n\treturn NULL;\n      }\n    else\n      {\n\t/* Enlarge the buffer.  */\n\tprtbuflen *= 2;\n\tprttmpbuf = __alloca (prtbuflen);\n      }\n\n  sock = RPC_ANYSOCK;\n  switch (p->p_proto)\n    {\n    case IPPROTO_UDP:\n      tv.tv_sec = 5;\n      tv.tv_usec = 0;\n      client = clntudp_create (&sin, prog, vers, tv, &sock);\n      if (client == NULL)\n\t{\n\t  return NULL;\n\t}\n#if 0\n      /* This is not wanted.  This would disable the user from having\n\t a timeout in the clnt_call() call.  Only a call to cnlt_control()\n\t by the user should set the timeout value.  */\n      tv.tv_sec = 25;\n      clnt_control (client, CLSET_TIMEOUT, (char *)&tv);\n#endif\n      break;\n    case IPPROTO_TCP:\n      client = clnttcp_create (&sin, prog, vers, &sock, 0, 0);\n      if (client == NULL)\n\t{\n\t  return NULL;\n\t}\n#if 0\n      /* This is not wanted.  This would disable the user from having\n\t a timeout in the clnt_call() call.  Only a call to cnlt_control()\n\t by the user should set the timeout value.  */\n      tv.tv_sec = 25;\n      tv.tv_usec = 0;\n      clnt_control (client, CLSET_TIMEOUT, (char *)&tv);\n#endif\n      break;\n    default:\n      {\n\tstruct rpc_createerr *ce = &get_rpc_createerr ();\n\tce->cf_stat = RPC_SYSTEMERROR;\n\tce->cf_error.re_errno = EPFNOSUPPORT;\n      }\n      return (NULL);\n    }\n  return client;\n}\n#ifdef EXPORT_RPC_SYMBOLS\nlibc_hidden_def (clnt_create)\n#else\nlibc_hidden_nolink_sunrpc (clnt_create, GLIBC_2_0)\n#endif", "/*\n * Copyright 2016-2018 The OpenSSL Project Authors. All Rights Reserved.\n *\n * Licensed under the OpenSSL license (the \"License\").  You may not use\n * this file except in compliance with the License.  You can obtain a copy\n * in the file LICENSE in the source distribution or at\n * https://www.openssl.org/source/license.html\n */\n\n/*\n * Licensed under the OpenSSL licenses, (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n * https://www.openssl.org/source/license.html\n * or in the file LICENSE in the source distribution.\n */\n\n#ifndef HEADER_RAND_INT_H\n# define HEADER_RAND_INT_H\n\n# include <openssl/rand.h>\n\n/* forward declaration */\ntypedef struct rand_pool_st RAND_POOL;\n\nvoid rand_cleanup_int(void);\nvoid rand_drbg_cleanup_int(void);\nvoid drbg_delete_thread_state(void);\n\n/* Hardware-based seeding functions. */\nsize_t rand_acquire_entropy_from_tsc(RAND_POOL *pool);\nsize_t rand_acquire_entropy_from_cpu(RAND_POOL *pool);\n\n/* DRBG entropy callbacks. */\nsize_t rand_drbg_get_entropy(RAND_DRBG *drbg,\n                             unsigned char **pout,\n                             int entropy, size_t min_len, size_t max_len,\n                             int prediction_resistance);\nvoid rand_drbg_cleanup_entropy(RAND_DRBG *drbg,\n                               unsigned char *out, size_t outlen);\nsize_t rand_drbg_get_nonce(RAND_DRBG *drbg,\n                           unsigned char **pout,\n                           int entropy, size_t min_len, size_t max_len);\nvoid rand_drbg_cleanup_nonce(RAND_DRBG *drbg,\n                             unsigned char *out, size_t outlen);\n\nsize_t rand_drbg_get_additional_data(RAND_POOL *pool, unsigned char **pout);\n\nvoid rand_drbg_cleanup_additional_data(RAND_POOL *pool, unsigned char *out);\n\n/*\n * RAND_POOL functions\n */\nRAND_POOL *rand_pool_new(int entropy_requested, int secure,\n                         size_t min_len, size_t max_len);\nRAND_POOL *rand_pool_attach(const unsigned char *buffer, size_t len,\n                            size_t entropy);\nvoid rand_pool_free(RAND_POOL *pool);\n\nconst unsigned char *rand_pool_buffer(RAND_POOL *pool);\nunsigned char *rand_pool_detach(RAND_POOL *pool);\nvoid rand_pool_reattach(RAND_POOL *pool, unsigned char *buffer);\n\nsize_t rand_pool_entropy(RAND_POOL *pool);\nsize_t rand_pool_length(RAND_POOL *pool);\n\nsize_t rand_pool_entropy_available(RAND_POOL *pool);\nsize_t rand_pool_entropy_needed(RAND_POOL *pool);\n/* |entropy_factor| expresses how many bits of data contain 1 bit of entropy */\nsize_t rand_pool_bytes_needed(RAND_POOL *pool, unsigned int entropy_factor);\nsize_t rand_pool_bytes_remaining(RAND_POOL *pool);\n\nint rand_pool_add(RAND_POOL *pool,\n                  const unsigned char *buffer, size_t len, size_t entropy);\nunsigned char *rand_pool_add_begin(RAND_POOL *pool, size_t len);\nint rand_pool_add_end(RAND_POOL *pool, size_t len, size_t entropy);\n\n\n/*\n * Add random bytes to the pool to acquire requested amount of entropy\n *\n * This function is platform specific and tries to acquire the requested\n * amount of entropy by polling platform specific entropy sources.\n *\n * If the function succeeds in acquiring at least |entropy_requested| bits\n * of entropy, the total entropy count is returned. If it fails, it returns\n * an entropy count of 0.\n */\nsize_t rand_pool_acquire_entropy(RAND_POOL *pool);\n\n/*\n * Add some application specific nonce data\n *\n * This function is platform specific and adds some application specific\n * data to the nonce used for instantiating the drbg.\n *\n * This data currently consists of the process and thread id, and a high\n * resolution timestamp. The data does not include an atomic counter,\n * because that is added by the calling function rand_drbg_get_nonce().\n *\n * Returns 1 on success and 0 on failure.\n */\nint rand_pool_add_nonce_data(RAND_POOL *pool);\n\n\n/*\n * Add some platform specific additional data\n *\n * This function is platform specific and adds some random noise to the\n * additional data used for generating random bytes and for reseeding\n * the drbg.\n *\n * Returns 1 on success and 0 on failure.\n */\nint rand_pool_add_additional_data(RAND_POOL *pool);\n\n/*\n * Initialise the random pool reseeding sources.\n *\n * Returns 1 on success and 0 on failure.\n */\nint rand_pool_init(void);\n\n/*\n * Finalise the random pool reseeding sources.\n */\n// ... 函数内容截断 ...\n/*\n * Add random bytes to the pool to acquire requested amount of entropy\n *\n * This function is platform specific and tries to acquire the requested\n * amount of entropy by polling platform specific entropy sources.\n *\n * If the function succeeds in acquiring at least |entropy_requested| bits\n * of entropy, the total entropy count is returned. If it fails, it returns\n * an entropy count of 0.\n */\nsize_t rand_pool_acquire_entropy(RAND_POOL *pool);\n\n/*\n * Add some application specific nonce data\n *\n * This function is platform specific and adds some application specific\n * data to the nonce used for instantiating the drbg.\n *\n * This data currently consists of the process and thread id, and a high\n * resolution timestamp. The data does not include an atomic counter,\n * because that is added by the calling function rand_drbg_get_nonce().\n *\n * Returns 1 on success and 0 on failure.\n */\nint rand_pool_add_nonce_data(RAND_POOL *pool);\n\n\n/*\n * Add some platform specific additional data\n *\n * This function is platform specific and adds some random noise to the\n * additional data used for generating random bytes and for reseeding\n * the drbg.\n *\n * Returns 1 on success and 0 on failure.\n */\nint rand_pool_add_additional_data(RAND_POOL *pool);\n\n/*\n * Initialise the random pool reseeding sources.\n *\n * Returns 1 on success and 0 on failure.\n */\nint rand_pool_init(void);\n\n/*\n * Finalise the random pool reseeding sources.\n */\nvoid rand_pool_cleanup(void);\n\n/*\n * Control the random pool use of open file descriptors.\n */\nvoid rand_pool_keep_random_devices_open(int keep);\n\n/* Equivalent of RAND_priv_bytes() but additionally taking an OPENSSL_CTX */\nint rand_priv_bytes_ex(OPENSSL_CTX *ctx, unsigned char *buf, int num);\n\n/* Equivalent of RAND_bytes() but additionally taking an OPENSSL_CTX */\nint rand_bytes_ex(OPENSSL_CTX *ctx, unsigned char *buf, int num);\n\n#endif", "", "/*\n * KVM coalesced MMIO\n *\n * Copyright (c) 2008 Bull S.A.S.\n * Copyright 2009 Red Hat, Inc. and/or its affiliates.\n *\n *  Author: <PERSON> <<PERSON>.<PERSON>iv<PERSON>@bull.net>\n *\n */\n\n#include <kvm/iodev.h>\n\n#include <linux/kvm_host.h>\n#include <linux/slab.h>\n#include <linux/kvm.h>\n\n#include \"coalesced_mmio.h\"\n\nstatic inline struct kvm_coalesced_mmio_dev *to_mmio(struct kvm_io_device *dev)\n{\n\treturn container_of(dev, struct kvm_coalesced_mmio_dev, dev);\n}\n\nstatic int coalesced_mmio_in_range(struct kvm_coalesced_mmio_dev *dev,\n\t\t\t\t   gpa_t addr, int len)\n{\n\t/* is it in a batchable area ?\n\t * (addr,len) is fully included in\n\t * (zone->addr, zone->size)\n\t */\n\tif (len < 0)\n\t\treturn 0;\n\tif (addr + len < addr)\n\t\treturn 0;\n\tif (addr < dev->zone.addr)\n\t\treturn 0;\n\tif (addr + len > dev->zone.addr + dev->zone.size)\n\t\treturn 0;\n\treturn 1;\n}\n\nstatic int coalesced_mmio_has_room(struct kvm_coalesced_mmio_dev *dev, u32 last)\n{\n\tstruct kvm_coalesced_mmio_ring *ring;\n\tunsigned avail;\n\n\t/* Are we able to batch it ? */\n\n\t/* last is the first free entry\n\t * check if we don't meet the first used entry\n\t * there is always one unused entry in the buffer\n\t */\n\tring = dev->kvm->coalesced_mmio_ring;\n\tavail = (ring->first - last - 1) % KVM_COALESCED_MMIO_MAX;\n\tif (avail == 0) {\n\t\t/* full */\n\t\treturn 0;\n\t}\n\n\treturn 1;\n}\n\nstatic int coalesced_mmio_write(struct kvm_vcpu *vcpu,\n\t\t\t\tstruct kvm_io_device *this, gpa_t addr,\n\t\t\t\tint len, const void *val)\n{\n\tstruct kvm_coalesced_mmio_dev *dev = to_mmio(this);\n\tstruct kvm_coalesced_mmio_ring *ring = dev->kvm->coalesced_mmio_ring;\n\t__u32 insert;\n\n\tif (!coalesced_mmio_in_range(dev, addr, len))\n\t\treturn -EOPNOTSUPP;\n\n\tspin_lock(&dev->kvm->ring_lock);\n\n\tinsert = READ_ONCE(ring->last);\n\tif (!coalesced_mmio_has_room(dev, insert) ||\n\t    insert >= KVM_COALESCED_MMIO_MAX) {\n\t\tspin_unlock(&dev->kvm->ring_lock);\n\t\treturn -EOPNOTSUPP;\n\t}\n\n\t/* copy data in first free entry of the ring */\n\n\tring->coalesced_mmio[insert].phys_addr = addr;\n\tring->coalesced_mmio[insert].len = len;\n\tmemcpy(ring->coalesced_mmio[insert].data, val, len);\n\tsmp_wmb();\n\tring->last = (insert + 1) % KVM_COALESCED_MMIO_MAX;\n\tspin_unlock(&dev->kvm->ring_lock);\n\treturn 0;\n}\n\nstatic void coalesced_mmio_destructor(struct kvm_io_device *this)\n{\n\tstruct kvm_coalesced_mmio_dev *dev = to_mmio(this);\n\n\tlist_del(&dev->list);\n\n\tkfree(dev);\n}\n\nstatic const struct kvm_io_device_ops coalesced_mmio_ops = {\n\t.write      = coalesced_mmio_write,\n\t.destructor = coalesced_mmio_destructor,\n};\n\nint kvm_coalesced_mmio_init(struct kvm *kvm)\n{\n\tstruct page *page;\n\tint ret;\n\n\tret = -ENOMEM;\n\tpage = alloc_page(GFP_KERNEL | __GFP_ZERO);\n\tif (!page)\n\t\tgoto out_err;\n\n\tret = 0;\n\tkvm->coalesced_mmio_ring = page_address(page);\n\n\t/*\n\t * We're using this spinlock to sync access to the coalesced ring.\n\t * The list doesn't need it's own lock since device registration and\n\t * unregistration should only happen when kvm->slots_lock is held.\n\t */\n\tspin_lock_init(&kvm->ring_lock);\n\tINIT_LIST_HEAD(&kvm->coalesced_zones);\n\nout_err:\n\treturn ret;\n}\n\nvoid kvm_coalesced_mmio_free(struct kvm *kvm)\n{\n\tif (kvm->coalesced_mmio_ring)\n\t\tfree_page((unsigned long)kvm->coalesced_mmio_ring);\n}\n\nint kvm_vm_ioctl_register_coalesced_mmio(struct kvm *kvm,\n\t\t\t\t\t struct kvm_coalesced_mmio_zone *zone)\n{\n\tint ret;\n\tstruct kvm_coalesced_mmio_dev *dev;\n\n\tdev = kzalloc(sizeof(struct kvm_coalesced_mmio_dev), GFP_KERNEL);\n\tif (!dev)\n\t\treturn -ENOMEM;\n\n\tkvm_iodevice_init(&dev->dev, &coalesced_mmio_ops);\n\tdev->kvm = kvm;\n\tdev->zone = *zone;\n\n\tmutex_lock(&kvm->slots_lock);\n\tret = kvm_io_bus_register_dev(kvm, KVM_MMIO_BUS, zone->addr,\n\t\t\t\t      zone->size, &dev->dev);\n\tif (ret < 0)\n\t\tgoto out_free_dev;\n\tlist_add_tail(&dev->list, &kvm->coalesced_zones);\n\tmutex_unlock(&kvm->slots_lock);\n\n\treturn 0;\n\nout_free_dev:\n\tmutex_unlock(&kvm->slots_lock);\n\tkfree(dev);\n\n\treturn ret;\n}\n\nint kvm_vm_ioctl_unregister_coalesced_mmio(struct kvm *kvm,\n\t\t\t\t\t   struct kvm_coalesced_mmio_zone *zone)\n{\n\tstruct kvm_coalesced_mmio_dev *dev, *tmp;\n\n\tmutex_lock(&kvm->slots_lock);\n\n\tlist_for_each_entry_safe(dev, tmp, &kvm->coalesced_zones, list)\n\t\tif (coalesced_mmio_in_range(dev, zone->addr, zone->size)) {\n\t\t\tkvm_io_bus_unregister_dev(kvm, KVM_MMIO_BUS, &dev->dev);\n\t\t\tkvm_iodevice_destructor(&dev->dev);\n\t\t}\n\n\tmutex_unlock(&kvm->slots_lock);\n\n\treturn 0;\n}", "", "// SPDX-License-Identifier: GPL-2.0-or-later\n/* System hash blacklist.\n *\n * Copyright (C) 2016 Red Hat, Inc. All Rights Reserved.\n * Written by <NAME> (<EMAIL>)\n */\n\n#define pr_fmt(fmt) \"blacklist: \"fmt\n#include <linux/module.h>\n#include <linux/slab.h>\n#include <linux/key.h>\n#include <linux/key-type.h>\n#include <linux/sched.h>\n#include <linux/ctype.h>\n#include <linux/err.h>\n#include <linux/seq_file.h>\n#include <linux/uidgid.h>\n#include <keys/system_keyring.h>\n#include \"blacklist.h\"\n\nstatic struct key *blacklist_keyring;\n\n/*\n * The description must be a type prefix, a colon and then an even number of\n * hex digits.  The hash is kept in the description.\n */\nstatic int blacklist_vet_description(const char *desc)\n{\n\tint n = 0;\n\n\tif (*desc == ':')\n\t\treturn -EINVAL;\n\tfor (; *desc; desc++)\n\t\tif (*desc == ':')\n\t\t\tgoto found_colon;\n\treturn -EINVAL;\n\nfound_colon:\n\tdesc++;\n\tfor (; *desc; desc++) {\n\t\tif (!isxdigit(*desc) || isupper(*desc))\n\t\t\treturn -EINVAL;\n\t\tn++;\n\t}\n\n\tif (n == 0 || n & 1)\n\t\treturn -EINVAL;\n\treturn 0;\n}\n\n/*\n * The hash to be blacklisted is expected to be in the description.  There will\n * be no payload.\n */\nstatic int blacklist_preparse(struct key_preparsed_payload *prep)\n{\n\tif (prep->datalen > 0)\n\t\treturn -EINVAL;\n\treturn 0;\n}\n\nstatic void blacklist_free_preparse(struct key_preparsed_payload *prep)\n{\n}\n\nstatic void blacklist_describe(const struct key *key, struct seq_file *m)\n{\n\tseq_puts(m, key->description);\n}\n\nstatic struct key_type key_type_blacklist = {\n\t.name\t\t\t= \"blacklist\",\n\t.vet_description\t= blacklist_vet_description,\n\t.preparse\t\t= blacklist_preparse,\n\t.free_preparse\t\t= blacklist_free_preparse,\n\t.instantiate\t\t= generic_key_instantiate,\n\t.describe\t\t= blacklist_describe,\n};\n\n/**\n * mark_hash_blacklisted - Add a hash to the system blacklist\n * @hash: The hash as a hex string with a type prefix (eg. \"tbs:23aa429783\")\n */\nint mark_hash_blacklisted(const char *hash)\n{\n\tkey_ref_t key;\n\n\tkey = key_create_or_update(make_key_ref(blacklist_keyring, true),\n\t\t\t\t   \"blacklist\",\n\t\t\t\t   hash,\n\t\t\t\t   NULL,\n\t\t\t\t   0,\n\t\t\t\t   ((KEY_POS_ALL & ~KEY_POS_SETATTR) |\n\t\t\t\t    KEY_USR_VIEW),\n\t\t\t\t   KEY_ALLOC_NOT_IN_QUOTA |\n\t\t\t\t   KEY_ALLOC_BUILT_IN);\n\tif (IS_ERR(key)) {\n\t\tpr_err(\"Problem blacklisting hash (%ld)\\n\", PTR_ERR(key));\n\t\treturn PTR_ERR(key);\n\t}\n\treturn 0;\n}\n\n/**\n * is_hash_blacklisted - Determine if a hash is blacklisted\n * @hash: The hash to be checked as a binary blob\n * @hash_len: The length of the binary hash\n * @type: Type of hash\n */\nint is_hash_blacklisted(const u8 *hash, size_t hash_len, const char *type)\n{\n\tkey_ref_t kref;\n\tsize_t type_len = strlen(type);\n\tchar *buffer, *p;\n\tint ret = 0;\n\n\tbuffer = kmalloc(type_len + 1 + hash_len * 2 + 1, GFP_KERNEL);\n\tif (!buffer)\n\t\treturn -ENOMEM;\n\tp = memcpy(buffer, type, type_len);\n\tp += type_len;\n\t*p++ = ':';\n\tbin2hex(p, hash, hash_len);\n\tp += hash_len * 2;\n\t*p = 0;\n\n\tkref = keyring_search(make_key_ref(blacklist_keyring, true),\n\t\t\t      &key_type_blacklist, buffer, false);\n\tif (!IS_ERR(kref)) {\n\t\tkey_ref_put(kref);\n\t\tret = -EKEYREJECTED;\n\t}\n\n\tkfree(buffer);\n\treturn ret;\n}\nEXPORT_SYMBOL_GPL(is_hash_blacklisted);\n\nint is_binary_blacklisted(const u8 *hash, size_t hash_len)\n{\n\tif (is_hash_blacklisted(hash, hash_len, \"bin\") == -EKEYREJECTED)\n\t\treturn -EPERM;\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(is_binary_blacklisted);\n\n#ifdef CONFIG_SYSTEM_REVOCATION_LIST\n/**\n * add_key_to_revocation_list - Add a revocation certificate to the blacklist\n * @data: The data blob containing the certificate\n * @size: The size of data blob\n */\nint add_key_to_revocation_list(const char *data, size_t size)\n{\n\tkey_ref_t key;\n\n\tkey = key_create_or_update(make_key_ref(blacklist_keyring, true),\n\t\t\t\t   \"asymmetric\",\n\t\t\t\t   NULL,\n\t\t\t\t   data,\n\t\t\t\t   size,\n\t\t\t\t   ((KEY_POS_ALL & ~KEY_POS_SETATTR) | KEY_USR_VIEW),\n\t\t\t\t   KEY_ALLOC_NOT_IN_QUOTA | KEY_ALLOC_BUILT_IN);\n\n\tif (IS_ERR(key)) {\n\t\tpr_err(\"Problem with revocation key (%ld)\\n\", PTR_ERR(key));\n\t\treturn PTR_ERR(key);\n\t}\n\n// ... 函数内容截断 ...\n\t\tret = -EKEYREJECTED;\n\t}\n\n\tkfree(buffer);\n\treturn ret;\n}\nEXPORT_SYMBOL_GPL(is_hash_blacklisted);\n\nint is_binary_blacklisted(const u8 *hash, size_t hash_len)\n{\n\tif (is_hash_blacklisted(hash, hash_len, \"bin\") == -EKEYREJECTED)\n\t\treturn -EPERM;\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(is_binary_blacklisted);\n\n/*\n * Initialise the blacklist\n */\nstatic int __init blacklist_init(void)\n{\n\tconst char *const *bl;\n\n\tif (register_key_type(&key_type_blacklist) < 0)\n\t\tpanic(\"Can't allocate system blacklist key type\\n\");\n\n\tblacklist_keyring =\n\t\tkeyring_alloc(\".blacklist\",\n\t\t\t      KUIDT_INIT(0), KGIDT_INIT(0),\n\t\t\t      current_cred(),\n\t\t\t      (KEY_POS_ALL & ~KEY_POS_SETATTR) |\n\t\t\t      KEY_USR_VIEW | KEY_USR_READ |\n\t\t\t      KEY_USR_SEARCH,\n\t\t\t      KEY_ALLOC_NOT_IN_QUOTA |\n\t\t\t      KEY_ALLOC_SET_KEEP,\n\t\t\t      NULL, NULL);\n\tif (IS_ERR(blacklist_keyring))\n\t\tpanic(\"Can't allocate system blacklist keyring\\n\");\n\n\tfor (bl = blacklist_hashes; *bl; bl++)\n\t\tif (mark_hash_blacklisted(*bl) < 0)\n\t\t\tpr_err(\"- blacklisting failed\\n\");\n\treturn 0;\n}\n\n/*\n * Must be initialised before we try and load the keys into the keyring.\n */\ndevice_initcall(blacklist_init);", ":\n/*\n * Copyright 1995-2016 The OpenSSL Project Authors. All Rights Reserved.\n *\n * Licensed under the OpenSSL license (the \"License\").  You may not use\n * this file except in compliance with the License.  You can obtain a copy\n * in the file LICENSE in the source distribution or at\n * https://www.openssl.org/source/license.html\n */\n\n#include <stdio.h>\n#include <stdlib.h>\n#include <string.h>\n#include <openssl/crypto.h>\n#include <openssl/des.h>\n#include <openssl/mdc2.h>\n\n#undef c2l\n#define c2l(c,l)        (l =((DES_LONG)(*((c)++)))    , \\\n                         l|=((DES_LONG)(*((c)++)))<< 8L, \\\n                         l|=((DES_LONG)(*((c)++)))<<16L, \\\n                         l|=((DES_LONG)(*((c)++)))<<24L)\n\n#undef l2c\n#define l2c(l,c)        (*((c)++)=(unsigned char)(((l)     )&0xff), \\\n                        *((c)++)=(unsigned char)(((l)>> 8L)&0xff), \\\n                        *((c)++)=(unsigned char)(((l)>>16L)&0xff), \\\n                        *((c)++)=(unsigned char)(((l)>>24L)&0xff))\n\nstatic void mdc2_body(MDC2_CTX *c, const unsigned char *in, size_t len);\nint MDC2_Init(MDC2_CTX *c)\n{\n    c->num = 0;\n    c->pad_type = 1;\n    memset(&(c->h[0]), 0x52, MDC2_BLOCK);\n    memset(&(c->hh[0]), 0x25, MDC2_BLOCK);\n    return 1;\n}\n\nint MDC2_Update(MDC2_CTX *c, const unsigned char *in, size_t len)\n{\n    size_t i, j;\n\n    i = c->num;\n    if (i != 0) {\n        if (len < MDC2_BLOCK - i) {\n            /* partial block */\n            memcpy(&(c->data[i]), in, len);\n            c->num += (int)len;\n            return 1;\n        } else {\n            /* filled one */\n            j = MDC2_BLOCK - i;\n            memcpy(&(c->data[i]), in, j);\n            len -= j;\n            in += j;\n            c->num = 0;\n            mdc2_body(c, &(c->data[0]), MDC2_BLOCK);\n        }\n    }\n    i = len & ~((size_t)MDC2_BLOCK - 1);\n    if (i > 0)\n        mdc2_body(c, in, i);\n    j = len - i;\n    if (j > 0) {\n        memcpy(&(c->data[0]), &(in[i]), j);\n        c->num = (int)j;\n    }\n    return 1;\n}\n\nstatic void mdc2_body(MDC2_CTX *c, const unsigned char *in, size_t len)\n{\n    register DES_LONG tin0, tin1;\n    register DES_LONG ttin0, ttin1;\n    DES_LONG d[2], dd[2];\n    DES_key_schedule k;\n    unsigned char *p;\n    size_t i;\n\n    for (i = 0; i < len; i += 8) {\n        c2l(in, tin0);\n        d[0] = dd[0] = tin0;\n        c2l(in, tin1);\n        d[1] = dd[1] = tin1;\n        c->h[0] = (c->h[0] & 0x9f) | 0x40;\n        c->hh[0] = (c->hh[0] & 0x9f) | 0x20;\n\n        DES_set_odd_parity(&c->h);\n        DES_set_key_unchecked(&c->h, &k);\n        DES_encrypt1(d, &k, 1);\n\n        DES_set_odd_parity(&c->hh);\n        DES_set_key_unchecked(&c->hh, &k);\n        DES_encrypt1(dd, &k, 1);\n\n        ttin0 = tin0 ^ dd[0];\n        ttin1 = tin1 ^ dd[1];\n        tin0 ^= d[0];\n        tin1 ^= d[1];\n\n        p = c->h;\n        l2c(tin0, p);\n        l2c(ttin1, p);\n        p = c->hh;\n        l2c(ttin0, p);\n        l2c(tin1, p);\n    }\n}\n\nint MDC2_Final(unsigned char *md, MDC2_CTX *c)\n{\n    unsigned int i;\n    int j;\n\n    i = c->num;\n    j = c->pad_type;\n    if ((i > 0) || (j == 2)) {\n        if (j == 2)\n            c->data[i++] = 0x80;\n        memset(&(c->data[i]), 0, MDC2_BLOCK - i);\n        mdc2_body(c, c->data, MDC2_BLOCK);\n    }\n    memcpy(md, (char *)c->h, MDC2_BLOCK);\n    memcpy(&(md[MDC2_BLOCK]), (char *)c->hh, MDC2_BLOCK);\n    return 1;\n}\n\n#undef TEST\n\n#ifdef TEST\nmain()\n{\n    unsigned char md[MDC2_DIGEST_LENGTH];\n    int i;\n    MDC2_CTX c;\n    static char *text = \"Now is the time for all \";\n\n    MDC2_Init(&c);\n    MDC2_Update(&c, text, strlen(text));\n    MDC2_Final(&(md[0]), &c);\n\n    for (i = 0; i < MDC2_DIGEST_LENGTH; i++)\n        printf(\"%02X\", md[i]);\n    printf(\"\\n\");\n}\n\n#endif", "", "", "/*\n * Force feedback support for ACRUX game controllers\n *\n * From what I have gathered, these devices are mass produced in China\n * by several vendors. They often share the same design as the original\n * Xbox 360 controller.\n *\n * 1a34:0802 \"ACRUX USB GAMEPAD 8116\"\n *  - tested with an EXEQ EQ-PCU-02090 game controller.\n *\n * Copyright (c) 2010 <PERSON> <<EMAIL>>\n */\n\n/*\n * This program is free software; you can redistribute it and/or modify\n * it under the terms of the GNU General Public License as published by\n * the Free Software Foundation; either version 2 of the License, or\n * (at your option) any later version.\n *\n * This program is distributed in the hope that it will be useful,\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n * GNU General Public License for more details.\n *\n * You should have received a copy of the GNU General Public License\n * along with this program; if not, write to the Free Software\n * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307 USA\n */\n\n#include <linux/input.h>\n#include <linux/slab.h>\n#include <linux/hid.h>\n#include <linux/module.h>\n\n#include \"hid-ids.h\"\n\n#ifdef CONFIG_HID_ACRUX_FF\n\nstruct axff_device {\n\tstruct hid_report *report;\n};\n\nstatic int axff_play(struct input_dev *dev, void *data, struct ff_effect *effect)\n{\n\tstruct hid_device *hid = input_get_drvdata(dev);\n\tstruct axff_device *axff = data;\n\tstruct hid_report *report = axff->report;\n\tint field_count = 0;\n\tint left, right;\n\tint i, j;\n\n\tleft = effect->u.rumble.strong_magnitude;\n\tright = effect->u.rumble.weak_magnitude;\n\n\tdbg_hid(\"called with 0x%04x 0x%04x\", left, right);\n\n\tleft = left * 0xff / 0xffff;\n\tright = right * 0xff / 0xffff;\n\n\tfor (i = 0; i < report->maxfield; i++) {\n\t\tfor (j = 0; j < report->field[i]->report_count; j++) {\n\t\t\treport->field[i]->value[j] =\n\t\t\t\tfield_count % 2 ? right : left;\n\t\t\tfield_count++;\n\t\t}\n\t}\n\n\tdbg_hid(\"running with 0x%02x 0x%02x\", left, right);\n\thid_hw_request(hid, axff->report, HID_REQ_SET_REPORT);\n\n\treturn 0;\n}\n\nstatic int axff_init(struct hid_device *hid)\n{\n\tstruct axff_device *axff;\n\tstruct hid_report *report;\n\tstruct hid_input *hidinput;\n\tstruct list_head *report_list =&hid->report_enum[HID_OUTPUT_REPORT].report_list;\n\tstruct input_dev *dev;\n\tint field_count = 0;\n\tint i, j;\n\tint error;\n\n\tif (list_empty(&hid->inputs)) {\n\t\thid_err(hid, \"no inputs found\\n\");\n\t\treturn -ENODEV;\n\t}\n\thidinput = list_first_entry(&hid->inputs, struct hid_input, list);\n\tdev = hidinput->input;\n\n\tif (list_empty(report_list)) {\n\t\thid_err(hid, \"no output reports found\\n\");\n\t\treturn -ENODEV;\n\t}\n\n\treport = list_first_entry(report_list, struct hid_report, list);\n\tfor (i = 0; i < report->maxfield; i++) {\n\t\tfor (j = 0; j < report->field[i]->report_count; j++) {\n\t\t\treport->field[i]->value[j] = 0x00;\n\t\t\tfield_count++;\n\t\t}\n\t}\n\n\tif (field_count < 4 && hid->product != 0xf705) {\n\t\thid_err(hid, \"not enough fields in the report: %d\\n\",\n\t\t\tfield_count);\n\t\treturn -ENODEV;\n\t}\n\n\taxff = kzalloc(sizeof(struct axff_device), GFP_KERNEL);\n\tif (!axff)\n\t\treturn -ENOMEM;\n\n\tset_bit(FF_RUMBLE, dev->ffbit);\n\n\terror = input_ff_create_memless(dev, axff, axff_play);\n\tif (error)\n\t\tgoto err_free_mem;\n\n\taxff->report = report;\n\thid_hw_request(hid, axff->report, HID_REQ_SET_REPORT);\n\n\thid_info(hid, \"Force Feedback for ACRUX game controllers by <NAME> <<EMAIL>>\\n\");\n\n\treturn 0;\n\nerr_free_mem:\n\tkfree(axff);\n\treturn error;\n}\n#else\nstatic inline int axff_init(struct hid_device *hid)\n{\n\treturn 0;\n}\n#endif\n\nstatic int ax_probe(struct hid_device *hdev, const struct hid_device_id *id)\n{\n\tint error;\n\n\tdev_dbg(&hdev->dev, \"ACRUX HID hardware probe...\\n\");\n\n\terror = hid_parse(hdev);\n\tif (error) {\n\t\thid_err(hdev, \"parse failed\\n\");\n\t\treturn error;\n\t}\n\n\terror = hid_hw_start(hdev, HID_CONNECT_DEFAULT & ~HID_CONNECT_FF);\n\tif (error) {\n\t\thid_err(hdev, \"hw start failed\\n\");\n\t\treturn error;\n\t}\n\n\terror = axff_init(hdev);\n\tif (error) {\n\t\t/*\n\t\t * Do not fail device initialization completely as device\n\t\t * may still be partially operable, just warn.\n\t\t */\n\t\thid_warn(hdev,\n\t\t\t \"Failed to enable force feedback support, error: %d\\n\",\n\t\t\t error);\n\t}\n\n\t/*\n\t * We need to start polling device right away, otherwise\n\t * it will go into a coma.\n\t */\n\terror = hid_hw_open(hdev);\n\tif (error) {\n\t\tdev_err(&hdev->dev, \"hw open failed\\n\");\n\t\thid_hw_stop(hdev);\n\t\treturn error;\n\t}\n\n\treturn 0;\n}\n\nstatic void ax_remove(struct hid_device *hdev)\n{\n\thid_hw_close(hdev);\n\thid_hw_stop(hdev);\n}\n\nstatic const struct hid_device_id ax_devices[] = {\n\t{ HID_USB_DEVICE(USB_VENDOR_ID_ACRUX, 0x0802), },\n\t{ HID_USB_DEVICE(USB_VENDOR_ID_ACRUX, 0xf705), },\n\t{ }\n};\nMODULE_DEVICE_TABLE(hid, ax_devices);\n\nstatic struct hid_driver ax_driver = {\n\t.name\t\t= \"acrux\",\n\t.id_table\t= ax_devices,\n\t.probe\t\t= ax_probe,\n\t.remove\t\t= ax_remove,\n};\nmodule_hid_driver(ax_driver);\n\nMODULE_AUTHOR(\"<NAME>\");\nMODULE_DESCRIPTION(\"Force feedback support for ACRUX game controllers\");\nMODULE_LICENSE(\"GPL\");", "", "", "", "", "", "", "", "", "/*\n * Block driver for Parallels disk image format\n *\n * Copyright (c) 2007 <NAME>\n *\n * This code is based on comparing different disk images created by Paralle<PERSON>.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL\n * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n#include \"qemu-common.h\"\n#include \"block/block_int.h\"\n#include \"qemu/module.h\"\n\n/**************************************************************/\n\n#define HEADER_MAGIC \"WithoutFreeSpace\"\n#define HEADER_VERSION 2\n#define HEADER_SIZE 64\n\n// always little-endian\nstruct parallels_header {\n    char magic[16]; // \"WithoutFreeSpace\"\n    uint32_t version;\n    uint32_t heads;\n    uint32_t cylinders;\n    uint32_t tracks;\n    uint32_t catalog_entries;\n    uint32_t nb_sectors;\n    char padding[24];\n} QEMU_PACKED;\n\ntypedef struct BDRVParallelsState {\n    CoMutex lock;\n\n    uint32_t *catalog_bitmap;\n    unsigned int catalog_size;\n\n    unsigned int tracks;\n} BDRVParallelsState;\n\nstatic int parallels_probe(const uint8_t *buf, int buf_size, const char *filename)\n{\n    const struct parallels_header *ph = (const void *)buf;\n\n    if (buf_size < HEADER_SIZE)\n\treturn 0;\n\n    if (!memcmp(ph->magic, HEADER_MAGIC, 16) &&\n\t(le32_to_cpu(ph->version) == HEADER_VERSION))\n\treturn 100;\n\n    return 0;\n}\n\nstatic int parallels_open(BlockDriverState *bs, QDict *options, int flags,\n                          Error **errp)\n{\n    BDRVParallelsState *s = bs->opaque;\n    int i;\n    struct parallels_header ph;\n    int ret;\n\n    bs->read_only = 1; // no write support yet\n\n    ret = bdrv_pread(bs->file, 0, &ph, sizeof(ph));\n    if (ret < 0) {\n        goto fail;\n    }\n\n    if (memcmp(ph.magic, HEADER_MAGIC, 16) ||\n        (le32_to_cpu(ph.version) != HEADER_VERSION)) {\n        error_setg(errp, \"Image not in Parallels format\");\n        ret = -EINVAL;\n        goto fail;\n    }\n\n    bs->total_sectors = le32_to_cpu(ph.nb_sectors);\n\n    s->tracks = le32_to_cpu(ph.tracks);\n    if (s->tracks == 0) {\n        error_setg(errp, \"Invalid image: Zero sectors per track\");\n        ret = -EINVAL;\n        goto fail;\n    }\n\n    s->catalog_size = le32_to_cpu(ph.catalog_entries);\n    if (s->catalog_size > INT_MAX / 4) {\n        error_setg(errp, \"Catalog too large\");\n        ret = -EFBIG;\n        goto fail;\n    }\n    s->catalog_bitmap = g_malloc(s->catalog_size * 4);\n\n    ret = bdrv_pread(bs->file, 64, s->catalog_bitmap, s->catalog_size * 4);\n    if (ret < 0) {\n        goto fail;\n    }\n\n    for (i = 0; i < s->catalog_size; i++)\n\tle32_to_cpus(&s->catalog_bitmap[i]);\n\n    qemu_co_mutex_init(&s->lock);\n    return 0;\n\nfail:\n    g_free(s->catalog_bitmap);\n    return ret;\n}\n\nstatic int64_t seek_to_sector(BlockDriverState *bs, int64_t sector_num)\n{\n    BDRVParallelsState *s = bs->opaque;\n    uint32_t index, offset;\n\n    index = sector_num / s->tracks;\n    offset = sector_num % s->tracks;\n\n    /* not allocated */\n    if ((index > s->catalog_size) || (s->catalog_bitmap[index] == 0))\n\treturn -1;\n    return (uint64_t)(s->catalog_bitmap[index] + offset) * 512;\n}\n\nstatic int parallels_read(BlockDriverState *bs, int64_t sector_num,\n                    uint8_t *buf, int nb_sectors)\n{\n    while (nb_sectors > 0) {\n        int64_t position = seek_to_sector(bs, sector_num);\n        if (position >= 0) {\n            if (bdrv_pread(bs->file, position, buf, 512) != 512)\n                return -1;\n        } else {\n            memset(buf, 0, 512);\n        }\n        nb_sectors--;\n        sector_num++;\n        buf += 512;\n    }\n    return 0;\n}\n\nstatic coroutine_fn int parallels_co_read(BlockDriverState *bs, int64_t sector_num,\n                                          uint8_t *buf, int nb_sectors)\n{\n    int ret;\n    BDRVParallelsState *s = bs->opaque;\n    qemu_co_mutex_lock(&s->lock);\n    ret = parallels_read(bs, sector_num, buf, nb_sectors);\n    qemu_co_mutex_unlock(&s->lock);\n    return ret;\n}\n\nstatic void parallels_close(BlockDriverState *bs)\n{\n    BDRVParallelsState *s = bs->opaque;\n    g_free(s->catalog_bitmap);\n}\n\nstatic BlockDriver bdrv_parallels = {\n    .format_name\t= \"parallels\",\n    .instance_size\t= sizeof(BDRVParallelsState),\n    .bdrv_probe\t\t= parallels_probe,\n    .bdrv_open\t\t= parallels_open,\n    .bdrv_read          = parallels_co_read,\n    .bdrv_close\t\t= parallels_close,\n};\n\nstatic void bdrv_parallels_init(void)\n{\n    bdrv_register(&bdrv_parallels);\n}\n\nblock_init(bdrv_parallels_init);", "// SPDX-License-Identifier: GPL-2.0+\n/*\n * Dummy inodes to buffer blocks for garbage collection\n *\n * Copyright (C) 2005-2008 Nippon Telegraph and Telephone Corporation.\n *\n * Written by <NAME>, <NAME>, and <NAME>.\n * Revised by <NAME>.\n *\n */\n/*\n * This file adds the cache of on-disk blocks to be moved in garbage\n * collection.  The disk blocks are held with dummy inodes (called\n * gcinodes), and this file provides lookup function of the dummy\n * inodes and their buffer read function.\n *\n * Buffers and pages held by the dummy inodes will be released each\n * time after they are copied to a new log.  Dirty blocks made on the\n * current generation and the blocks to be moved by GC never overlap\n * because the dirty blocks make a new generation; they rather must be\n * written individually.\n */\n\n#include <linux/buffer_head.h>\n#include <linux/mpage.h>\n#include <linux/hash.h>\n#include <linux/slab.h>\n#include <linux/swap.h>\n#include \"nilfs.h\"\n#include \"btree.h\"\n#include \"btnode.h\"\n#include \"page.h\"\n#include \"mdt.h\"\n#include \"dat.h\"\n#include \"ifile.h\"\n\n/*\n * nilfs_gccache_submit_read_data() - add data buffer and submit read request\n * @inode - gc inode\n * @blkoff - dummy offset treated as the key for the page cache\n * @pbn - physical block number of the block\n * @vbn - virtual block number of the block, 0 for non-virtual block\n * @out_bh - indirect pointer to a buffer_head struct to receive the results\n *\n * Description: nilfs_gccache_submit_read_data() registers the data buffer\n * specified by @pbn to the GC pagecache with the key @blkoff.\n * This function sets @vbn (@pbn if @vbn is zero) in b_blocknr of the buffer.\n *\n * Return Value: On success, 0 is returned. On Error, one of the following\n * negative error code is returned.\n *\n * %-EIO - I/O error.\n *\n * %-ENOMEM - Insufficient amount of memory available.\n *\n * %-ENOENT - The block specified with @pbn does not exist.\n */\nint nilfs_gccache_submit_read_data(struct inode *inode, sector_t blkoff,\n\t\t\t\t   sector_t pbn, __u64 vbn,\n\t\t\t\t   struct buffer_head **out_bh)\n{\n\tstruct buffer_head *bh;\n\tint err;\n\n\tbh = nilfs_grab_buffer(inode, inode->i_mapping, blkoff, 0);\n\tif (unlikely(!bh))\n\t\treturn -ENOMEM;\n\n\tif (buffer_uptodate(bh))\n\t\tgoto out;\n\n\tif (pbn == 0) {\n\t\tstruct the_nilfs *nilfs = inode->i_sb->s_fs_info;\n\n\t\terr = nilfs_dat_translate(nilfs->ns_dat, vbn, &pbn);\n\t\tif (unlikely(err)) /* -EIO, -ENOMEM, -ENOENT */\n\t\t\tgoto failed;\n\t}\n\n\tlock_buffer(bh);\n\tif (buffer_uptodate(bh)) {\n\t\tunlock_buffer(bh);\n\t\tgoto out;\n\t}\n\n\tif (!buffer_mapped(bh)) {\n\t\tbh->b_bdev = inode->i_sb->s_bdev;\n\t\tset_buffer_mapped(bh);\n\t}\n\tbh->b_blocknr = pbn;\n\tbh->b_end_io = end_buffer_read_sync;\n\tget_bh(bh);\n\tsubmit_bh(REQ_OP_READ, bh);\n\tif (vbn)\n\t\tbh->b_blocknr = vbn;\n out:\n\terr = 0;\n\t*out_bh = bh;\n\n failed:\n\tunlock_page(bh->b_page);\n\tput_page(bh->b_page);\n\tif (unlikely(err))\n\t\tbrelse(bh);\n\treturn err;\n}\n\n/*\n * nilfs_gccache_submit_read_node() - add node buffer and submit read request\n * @inode - gc inode\n * @pbn - physical block number for the block\n * @vbn - virtual block number for the block\n * @out_bh - indirect pointer to a buffer_head struct to receive the results\n *\n * Description: nilfs_gccache_submit_read_node() registers the node buffer\n * specified by @vbn to the GC pagecache.  @pbn can be supplied by the\n * caller to avoid translation of the disk block address.\n *\n * Return Value: On success, 0 is returned. On Error, one of the following\n * negative error code is returned.\n *\n * %-EIO - I/O error.\n *\n * %-ENOMEM - Insufficient amount of memory available.\n */\nint nilfs_gccache_submit_read_node(struct inode *inode, sector_t pbn,\n\t\t\t\t   __u64 vbn, struct buffer_head **out_bh)\n{\n\tstruct inode *btnc_inode = NILFS_I(inode)->i_assoc_inode;\n\tint ret;\n\n\tret = nilfs_btnode_submit_block(btnc_inode->i_mapping, vbn ? : pbn, pbn,\n\t\t\t\t\tREQ_OP_READ, out_bh, &pbn);\n\tif (ret == -EEXIST) /* internal code (cache hit) */\n\t\tret = 0;\n\treturn ret;\n}\n\nint nilfs_gccache_wait_and_mark_dirty(struct buffer_head *bh)\n{\n\twait_on_buffer(bh);\n\tif (!buffer_uptodate(bh)) {\n\t\tstruct inode *inode = bh->b_folio->mapping->host;\n\n\t\tnilfs_err(inode->i_sb,\n\t\t\t  \"I/O error reading %s block for GC (ino=%lu, vblocknr=%llu)\",\n\t\t\t  buffer_nilfs_node(bh) ? \"node\" : \"data\",\n\t\t\t  inode->i_ino, (unsigned long long)bh->b_blocknr);\n\t\treturn -EIO;\n\t}\n\tif (buffer_dirty(bh))\n\t\treturn -EEXIST;\n\n\tif (buffer_nilfs_node(bh) && nilfs_btree_broken_node_block(bh)) {\n\t\tclear_buffer_uptodate(bh);\n\t\treturn -EIO;\n\t}\n\tmark_buffer_dirty(bh);\n\treturn 0;\n}\n\nint nilfs_init_gcinode(struct inode *inode)\n{\n\tstruct nilfs_inode_info *ii = NILFS_I(inode);\n\n\tinode->i_mode = S_IFREG;\n\tmapping_set_gfp_mask(inode->i_mapping, GFP_NOFS);\n\tinode->i_mapping->a_ops = &empty_aops;\n\n\tii->i_flags = 0;\n\tnilfs_bmap_init_gc(ii->i_bmap);\n\n\treturn nilfs_attach_btree_node_cache(inode);\n}\n\n/**\n * nilfs_remove_all_gcinodes() - remove all unprocessed gc inodes\n */\nvoid nilfs_remove_all_gcinodes(struct the_nilfs *nilfs)\n{\n\tstruct list_head *head = &nilfs->ns_gc_inodes;\n\tstruct nilfs_inode_info *ii;\n\n\twhile (!list_empty(head)) {\n\t\tii = list_first_entry(head, struct nilfs_inode_info, i_dirty);\n\t\tlist_del_init(&ii->i_dirty);\n\t\ttruncate_inode_pages(&ii->vfs_inode.i_data, 0);\n\t\tnilfs_btnode_cache_clear(ii->i_assoc_inode->i_mapping);\n\t\tiput(&ii->vfs_inode);\n\t}\n}", "/*\n *  Copyright (c) by <PERSON><PERSON><PERSON> <<EMAIL>>\n *  Copyright (c) 2009 by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n *  Routines for control of MPU-401 in UART mode\n *\n *  MPU-401 supports UART mode which is not capable generate transmit\n *  interrupts thus output is done via polling. Also, if irq < 0, then\n *  input is done also via polling. Do not expect good performance.\n *\n *\n *   This program is free software; you can redistribute it and/or modify\n *   it under the terms of the GNU General Public License as published by\n *   the Free Software Foundation; either version 2 of the License, or\n *   (at your option) any later version.\n *\n *   This program is distributed in the hope that it will be useful,\n *   but WITHOUT ANY WARRANTY; without even the implied warranty of\n *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n *   GNU General Public License for more details.\n *\n *   You should have received a copy of the GNU General Public License\n *   along with this program; if not, write to the Free Software\n *   Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307 USA\n *\n */\n\n#include <linux/io.h>\n#include <linux/slab.h>\n#include <linux/delay.h>\n#include <linux/ioport.h>\n#include <linux/errno.h>\n#include <linux/export.h>\n#include <sound/core.h>\n#include <sound/rawmidi.h>\n\n#include \"msnd.h\"\n\n#define MSNDMIDI_MODE_BIT_INPUT\t\t0\n#define MSNDMIDI_MODE_BIT_OUTPUT\t\t1\n#define MSNDMIDI_MODE_BIT_INPUT_TRIGGER\t2\n#define MSNDMIDI_MODE_BIT_OUTPUT_TRIGGER\t3\n\nstruct snd_msndmidi {\n\tstruct snd_msnd *dev;\n\n\tunsigned long mode;\t\t/* MSNDMIDI_MODE_XXXX */\n\n\tstruct snd_rawmidi_substream *substream_input;\n\n\tspinlock_t input_lock;\n};\n\n/*\n * input/output open/close - protected by open_mutex in rawmidi.c\n */\nstatic int snd_msndmidi_input_open(struct snd_rawmidi_substream *substream)\n{\n\tstruct snd_msndmidi *mpu;\n\n\tsnd_printdd(\"snd_msndmidi_input_open()\\n\");\n\n\tmpu = substream->rmidi->private_data;\n\n\tmpu->substream_input = substream;\n\n\tsnd_msnd_enable_irq(mpu->dev);\n\n\tsnd_msnd_send_dsp_cmd(mpu->dev, HDEX_MIDI_IN_START);\n\tset_bit(MSNDMIDI_MODE_BIT_INPUT, &mpu->mode);\n\treturn 0;\n}\n\nstatic int snd_msndmidi_input_close(struct snd_rawmidi_substream *substream)\n{\n\tstruct snd_msndmidi *mpu;\n\n\tmpu = substream->rmidi->private_data;\n\tsnd_msnd_send_dsp_cmd(mpu->dev, HDEX_MIDI_IN_STOP);\n\tclear_bit(MSNDMIDI_MODE_BIT_INPUT, &mpu->mode);\n\tmpu->substream_input = NULL;\n\tsnd_msnd_disable_irq(mpu->dev);\n\treturn 0;\n}\n\nstatic void snd_msndmidi_input_drop(struct snd_msndmidi *mpu)\n{\n\tu16 tail;\n\n\ttail = readw(mpu->dev->MIDQ + JQS_wTail);\n\twritew(tail, mpu->dev->MIDQ + JQS_wHead);\n}\n\n/*\n * trigger input\n */\nstatic void snd_msndmidi_input_trigger(struct snd_rawmidi_substream *substream,\n\t\t\t\t\tint up)\n{\n\tunsigned long flags;\n\tstruct snd_msndmidi *mpu;\n\n\tsnd_printdd(\"snd_msndmidi_input_trigger(, %i)\\n\", up);\n\n\tmpu = substream->rmidi->private_data;\n\tspin_lock_irqsave(&mpu->input_lock, flags);\n\tif (up) {\n\t\tif (!test_and_set_bit(MSNDMIDI_MODE_BIT_INPUT_TRIGGER,\n\t\t\t\t      &mpu->mode))\n\t\t\tsnd_msndmidi_input_drop(mpu);\n\t} else {\n\t\tclear_bit(MSNDMIDI_MODE_BIT_INPUT_TRIGGER, &mpu->mode);\n\t}\n\tspin_unlock_irqrestore(&mpu->input_lock, flags);\n\tif (up)\n\t\tsnd_msndmidi_input_read(mpu);\n}\n\nvoid snd_msndmidi_input_read(void *mpuv)\n{\n\tunsigned long flags;\n\tstruct snd_msndmidi *mpu = mpuv;\n\tvoid *pwMIDQData = mpu->dev->mappedbase + MIDQ_DATA_BUFF;\n\tu16 head, tail, size;\n\n\tspin_lock_irqsave(&mpu->input_lock, flags);\n\thead = readw(mpu->dev->MIDQ + JQS_wHead);\n\ttail = readw(mpu->dev->MIDQ + JQS_wTail);\n\tsize = readw(mpu->dev->MIDQ + JQS_wSize);\n\tif (head > size || tail > size)\n\t\tgoto out;\n\twhile (head != tail) {\n\t\tunsigned char val = readw(pwMIDQData + 2 * head);\n\n\t\tif (test_bit(MSNDMIDI_MODE_BIT_INPUT_TRIGGER, &mpu->mode))\n\t\t\tsnd_rawmidi_receive(mpu->substream_input, &val, 1);\n\t\tif (++head > size)\n\t\t\thead = 0;\n\t\twritew(head, mpu->dev->MIDQ + JQS_wHead);\n\t}\n out:\n\tspin_unlock_irqrestore(&mpu->input_lock, flags);\n}\nEXPORT_SYMBOL(snd_msndmidi_input_read);\n\nstatic struct snd_rawmidi_ops snd_msndmidi_input = {\n\t.open =\t\tsnd_msndmidi_input_open,\n\t.close =\tsnd_msndmidi_input_close,\n\t.trigger =\tsnd_msndmidi_input_trigger,\n};\n\nstatic void snd_msndmidi_free(struct snd_rawmidi *rmidi)\n{\n\tstruct snd_msndmidi *mpu = rmidi->private_data;\n\tkfree(mpu);\n}\n\nint snd_msndmidi_new(struct snd_card *card, int device)\n{\n\tstruct snd_msnd *chip = card->private_data;\n\tstruct snd_msndmidi *mpu;\n\tstruct snd_rawmidi *rmidi;\n\tint err;\n\n\terr = snd_rawmidi_new(card, \"MSND-MIDI\", device, 1, 1, &rmidi);\n\tif (err < 0)\n\t\treturn err;\n\tmpu = kzalloc(sizeof(*mpu), GFP_KERNEL);\n\tif (mpu == NULL) {\n\t\tsnd_device_free(card, rmidi);\n\t\treturn -ENOMEM;\n\t}\n\tmpu->dev = chip;\n\tchip->msndmidi_mpu = mpu;\n\trmidi->private_data = mpu;\n\trmidi->private_free = snd_msndmidi_free;\n\tspin_lock_init(&mpu->input_lock);\n\tstrcpy(rmidi->name, \"MSND MIDI\");\n\tsnd_rawmidi_set_ops(rmidi, SNDRV_RAWMIDI_STREAM_INPUT,\n\t\t\t    &snd_msndmidi_input);\n\trmidi->info_flags |= SNDRV_RAWMIDI_INFO_INPUT;\n\treturn 0;\n}", "/*\n *  Copyright (c) by <PERSON><PERSON><PERSON> <<EMAIL>>\n *  Copyright (c) 2009 by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n *  Routines for control of MPU-401 in UART mode\n *\n *  MPU-401 supports UART mode which is not capable generate transmit\n *  interrupts thus output is done via polling. Also, if irq < 0, then\n *  input is done also via polling. Do not expect good performance.\n *\n *\n *   This program is free software; you can redistribute it and/or modify\n *   it under the terms of the GNU General Public License as published by\n *   the Free Software Foundation; either version 2 of the License, or\n *   (at your option) any later version.\n *\n *   This program is distributed in the hope that it will be useful,\n *   but WITHOUT ANY WARRANTY; without even the implied warranty of\n *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n *   GNU General Public License for more details.\n *\n *   You should have received a copy of the GNU General Public License\n *   along with this program; if not, write to the Free Software\n *   Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307 USA\n *\n */\n\n#include <linux/io.h>\n#include <linux/slab.h>\n#include <linux/delay.h>\n#include <linux/ioport.h>\n#include <linux/errno.h>\n#include <linux/export.h>\n#include <sound/core.h>\n#include <sound/rawmidi.h>\n\n#include \"msnd.h\"\n\n#define MSNDMIDI_MODE_BIT_INPUT\t\t0\n#define MSNDMIDI_MODE_BIT_OUTPUT\t\t1\n#define MSNDMIDI_MODE_BIT_INPUT_TRIGGER\t2\n#define MSNDMIDI_MODE_BIT_OUTPUT_TRIGGER\t3\n\nstruct snd_msndmidi {\n\tstruct snd_msnd *dev;\n\n\tunsigned long mode;\t\t/* MSNDMIDI_MODE_XXXX */\n\n\tstruct snd_rawmidi_substream *substream_input;\n\n\tspinlock_t input_lock;\n};\n\n/*\n * input/output open/close - protected by open_mutex in rawmidi.c\n */\nstatic int snd_msndmidi_input_open(struct snd_rawmidi_substream *substream)\n{\n\tstruct snd_msndmidi *mpu;\n\n\tsnd_printdd(\"snd_msndmidi_input_open()\\n\");\n\n\tmpu = substream->rmidi->private_data;\n\n\tmpu->substream_input = substream;\n\n\tsnd_msnd_enable_irq(mpu->dev);\n\n\tsnd_msnd_send_dsp_cmd(mpu->dev, HDEX_MIDI_IN_START);\n\tset_bit(MSNDMIDI_MODE_BIT_INPUT, &mpu->mode);\n\treturn 0;\n}\n\nstatic int snd_msndmidi_input_close(struct snd_rawmidi_substream *substream)\n{\n\tstruct snd_msndmidi *mpu;\n\n\tmpu = substream->rmidi->private_data;\n\tsnd_msnd_send_dsp_cmd(mpu->dev, HDEX_MIDI_IN_STOP);\n\tclear_bit(MSNDMIDI_MODE_BIT_INPUT, &mpu->mode);\n\tmpu->substream_input = NULL;\n\tsnd_msnd_disable_irq(mpu->dev);\n\treturn 0;\n}\n\nstatic void snd_msndmidi_input_drop(struct snd_msndmidi *mpu)\n{\n\tu16 tail;\n\n\ttail = readw(mpu->dev->MIDQ + JQS_wTail);\n\twritew(tail, mpu->dev->MIDQ + JQS_wHead);\n}\n\n/*\n * trigger input\n */\nstatic void snd_msndmidi_input_trigger(struct snd_rawmidi_substream *substream,\n\t\t\t\t\tint up)\n{\n\tunsigned long flags;\n\tstruct snd_msndmidi *mpu;\n\n\tsnd_printdd(\"snd_msndmidi_input_trigger(, %i)\\n\", up);\n\n\tmpu = substream->rmidi->private_data;\n\tspin_lock_irqsave(&mpu->input_lock, flags);\n\tif (up) {\n\t\tif (!test_and_set_bit(MSNDMIDI_MODE_BIT_INPUT_TRIGGER,\n\t\t\t\t      &mpu->mode))\n\t\t\tsnd_msndmidi_input_drop(mpu);\n\t} else {\n\t\tclear_bit(MSNDMIDI_MODE_BIT_INPUT_TRIGGER, &mpu->mode);\n\t}\n\tspin_unlock_irqrestore(&mpu->input_lock, flags);\n\tif (up)\n\t\tsnd_msndmidi_input_read(mpu);\n}\n\nvoid snd_msndmidi_input_read(void *mpuv)\n{\n\tunsigned long flags;\n\tstruct snd_msndmidi *mpu = mpuv;\n\tvoid *pwMIDQData = mpu->dev->mappedbase + MIDQ_DATA_BUFF;\n\tu16 head, tail, size;\n\n\tspin_lock_irqsave(&mpu->input_lock, flags);\n\thead = readw(mpu->dev->MIDQ + JQS_wHead);\n\ttail = readw(mpu->dev->MIDQ + JQS_wTail);\n\tsize = readw(mpu->dev->MIDQ + JQS_wSize);\n\tif (head > size || tail > size)\n\t\tgoto out;\n\twhile (head != tail) {\n\t\tunsigned char val = readw(pwMIDQData + 2 * head);\n\n\t\tif (test_bit(MSNDMIDI_MODE_BIT_INPUT_TRIGGER, &mpu->mode))\n\t\t\tsnd_rawmidi_receive(mpu->substream_input, &val, 1);\n\t\tif (++head > size)\n\t\t\thead = 0;\n\t\twritew(head, mpu->dev->MIDQ + JQS_wHead);\n\t}\n out:\n\tspin_unlock_irqrestore(&mpu->input_lock, flags);\n}\nEXPORT_SYMBOL(snd_msndmidi_input_read);\n\nstatic struct snd_rawmidi_ops snd_msndmidi_input = {\n\t.open =\t\tsnd_msndmidi_input_open,\n\t.close =\tsnd_msndmidi_input_close,\n\t.trigger =\tsnd_msndmidi_input_trigger,\n};\n\nstatic void snd_msndmidi_free(struct snd_rawmidi *rmidi)\n{\n\tstruct snd_msndmidi *mpu = rmidi->private_data;\n\tkfree(mpu);\n}\n\nint snd_msndmidi_new(struct snd_card *card, int device)\n{\n\tstruct snd_msnd *chip = card->private_data;\n\tstruct snd_msndmidi *mpu;\n\tstruct snd_rawmidi *rmidi;\n\tint err;\n\n\terr = snd_rawmidi_new(card, \"MSND-MIDI\", device, 1, 1, &rmidi);\n\tif (err < 0)\n\t\treturn err;\n\tmpu = kzalloc(sizeof(*mpu), GFP_KERNEL);\n\tif (mpu == NULL) {\n\t\tsnd_device_free(card, rmidi);\n\t\treturn -ENOMEM;\n\t}\n\tmpu->dev = chip;\n\tchip->msndmidi_mpu = mpu;\n\trmidi->private_data = mpu;\n\trmidi->private_free = snd_msndmidi_free;\n\tspin_lock_init(&mpu->input_lock);\n\tstrcpy(rmidi->name, \"MSND MIDI\");\n\tsnd_rawmidi_set_ops(rmidi, SNDRV_RAWMIDI_STREAM_INPUT,\n\t\t\t    &snd_msndmidi_input);\n\trmidi->info_flags |= SNDRV_RAWMIDI_INFO_INPUT;\n\treturn 0;\n}", "/* packet-cms.c\n * Routines for RFC5652 Cryptographic Message Syntax packet dissection\n *   <PERSON> 2004\n *   <PERSON><PERSON> 2010\n *\n * Wireshark - Network traffic analyzer\n * By <PERSON> <<EMAIL>>\n * Copyright 1998 <PERSON>\n *\n * SPDX-License-Identifier: GPL-2.0-or-later\n */\n\n#include \"config.h\"\n\n#include <epan/packet.h>\n#include <epan/oids.h>\n#include <epan/asn1.h>\n#include <epan/proto_data.h>\n#include <wsutil/wsgcrypt.h>\n\n#include \"packet-ber.h\"\n#include \"packet-cms.h\"\n#include \"packet-x509af.h\"\n#include \"packet-x509ce.h\"\n#include \"packet-x509if.h\"\n#include \"packet-x509sat.h\"\n#include \"packet-pkcs12.h\"\n\n#define PNAME  \"Cryptographic Message Syntax\"\n#define PSNAME \"CMS\"\n#define PFNAME \"cms\"\n\nvoid proto_register_cms(void);\nvoid proto_reg_handoff_cms(void);\n\n/* Initialize the protocol and registered fields */\nstatic int proto_cms = -1;\nstatic int hf_cms_ci_contentType = -1;\n#include \"packet-cms-hf.c\"\n\n/* Initialize the subtree pointers */\n#include \"packet-cms-ett.c\"\n\nstatic int dissect_cms_OCTET_STRING(gboolean implicit_tag _U_, tvbuff_t *tvb, int offset, asn1_ctx_t *actx, proto_tree *tree, int hf_index _U_) ; /* XXX kill a compiler warning until asn2wrs stops generating these silly wrappers */\n\nstruct cms_private_data {\n  const char *object_identifier_id;\n  tvbuff_t *content_tvb;\n};\n\nstatic proto_tree *top_tree=NULL;\nstatic proto_tree *cap_tree=NULL;\n\n#define HASH_SHA1 \"1.3.14.3.2.26\"\n\n#define HASH_MD5 \"1.2.840.113549.2.5\"\n\n\n/* SHA-2 variants */\n#define HASH_SHA224 \"2.16.840.1.101.3.4.2.4\"\n#define SHA224_BUFFER_SIZE  32 /* actually 28 */\n#define HASH_SHA256 \"2.16.840.1.101.3.4.2.1\"\n#define SHA256_BUFFER_SIZE  32\n\nunsigned char digest_buf[MAX(HASH_SHA1_LENGTH, HASH_MD5_LENGTH)];\n\nstatic struct cms_private_data*\ncms_get_private_data(packet_info *pinfo)\n{\n  struct cms_private_data *cms_data = (struct cms_private_data*)p_get_proto_data(pinfo->pool, pinfo, proto_cms, 0);\n  if (!cms_data) {\n    cms_data = wmem_new0(pinfo->pool, struct cms_private_data);\n    p_add_proto_data(pinfo->pool, pinfo, proto_cms, 0, cms_data);\n  }\n  return cms_data;\n}\n\nstatic void\ncms_verify_msg_digest(proto_item *pi, tvbuff_t *content, const char *alg, tvbuff_t *tvb, int offset)\n{\n  int i= 0, buffer_size = 0;\n\n  /* we only support two algorithms at the moment  - if we do add SHA2\n     we should add a registration process to use a registration process */\n\n  if(strcmp(alg, HASH_SHA1) == 0) {\n    gcry_md_hash_buffer(GCRY_MD_SHA1, digest_buf, tvb_get_ptr(content, 0, tvb_captured_length(content)), tvb_captured_length(content));\n    buffer_size = HASH_SHA1_LENGTH;\n\n  } else if(strcmp(alg, HASH_MD5) == 0) {\n    gcry_md_hash_buffer(GCRY_MD_MD5, digest_buf, tvb_get_ptr(content, 0, tvb_captured_length(content)), tvb_captured_length(content));\n    buffer_size = HASH_MD5_LENGTH;\n  }\n\n  if(buffer_size) {\n    /* compare our computed hash with what we have received */\n\n    if(tvb_bytes_exist(tvb, offset, buffer_size) &&\n       (tvb_memeql(tvb, offset, digest_buf, buffer_size) != 0)) {\n      proto_item_append_text(pi, \" [incorrect, should be \");\n      for(i = 0; i < buffer_size; i++)\n\tproto_item_append_text(pi, \"%02X\", digest_buf[i]);\n\n      proto_item_append_text(pi, \"]\");\n    }\n    else\n      proto_item_append_text(pi, \" [correct]\");\n  } else {\n    proto_item_append_text(pi, \" [unable to verify]\");\n  }\n\n}\n\n#include \"packet-cms-fn.c\"\n\n/*--- proto_register_cms ----------------------------------------------*/\nvoid proto_register_cms(void) {\n\n  /* List of fields */\n  static hf_register_info hf[] = {\n    { &hf_cms_ci_contentType,\n      { \"contentType\", \"cms.contentInfo.contentType\",\n        FT_OID, BASE_NONE, NULL, 0,\n        NULL, HFILL }},\n#include \"packet-cms-hfarr.c\"\n  };\n\n  /* List of subtrees */\n  static gint *ett[] = {\n#include \"packet-cms-ettarr.c\"\n  };\n\n  /* Register protocol */\n  proto_cms = proto_register_protocol(PNAME, PSNAME, PFNAME);\n\n  /* Register fields and subtrees */\n  proto_register_field_array(proto_cms, hf, array_length(hf));\n  proto_register_subtree_array(ett, array_length(ett));\n\n  register_ber_syntax_dissector(\"ContentInfo\", proto_cms, dissect_ContentInfo_PDU);\n  register_ber_syntax_dissector(\"SignedData\", proto_cms, dissect_SignedData_PDU);\n  register_ber_oid_syntax(\".p7s\", NULL, \"ContentInfo\");\n  register_ber_oid_syntax(\".p7m\", NULL, \"ContentInfo\");\n  register_ber_oid_syntax(\".p7c\", NULL, \"ContentInfo\");\n\n\n}\n\n\n/*--- proto_reg_handoff_cms -------------------------------------------*/\nvoid proto_reg_handoff_cms(void) {\n  dissector_handle_t content_info_handle;\n#include \"packet-cms-dis-tab.c\"\n\n  /* RFC 3370 [CMS-ASN} section 4.3.1 */\n  register_ber_oid_dissector(\"1.2.840.113549.1.9.16.3.6\", dissect_ber_oid_NULL_callback, proto_cms, \"id-alg-CMS3DESwrap\");\n\n  oid_add_from_string(\"id-data\",\"1.2.840.113549.1.7.1\");\n  oid_add_from_string(\"id-alg-des-cbc\",\"1.3.14.3.2.7\");\n\n  content_info_handle = create_dissector_handle (dissect_ContentInfo_PDU, proto_cms);\n  dissector_add_string(\"media_type\", \"application/pkcs7-mime\", content_info_handle);\n  dissector_add_string(\"media_type\", \"application/pkcs7-signature\", content_info_handle);\n  dissector_add_string(\"rfc7468.preeb_label\", \"CMS\", content_info_handle);\n}", "", "", "", "", "", "", "", "/* crypto/asn1/a_type.c */\n/* Copyright (C) 1995-1998 <NAME> (<EMAIL>)\n * All rights reserved.\n *\n * This package is an SSL implementation written\n * by <NAME> (<EMAIL>).\n * The implementation was written so as to conform with Netscapes SSL.\n *\n * This library is free for commercial and non-commercial use as long as\n * the following conditions are aheared to.  The following conditions\n * apply to all code found in this distribution, be it the RC4, RSA,\n * lhash, DES, etc., code; not just the SSL code.  The SSL documentation\n * included with this distribution is covered by the same copyright terms\n * except that the holder is <NAME> (<EMAIL>).\n *\n * Copyright remains <PERSON>'s, and as such any Copyright notices in\n * the code are not to be removed.\n * If this package is used in a product, <PERSON> should be given attribution\n * as the author of the parts of the library used.\n * This can be in the form of a textual message at program startup or\n * in documentation (online or textual) provided with the package.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n * 1. Redistributions of source code must retain the copyright\n *    notice, this list of conditions and the following disclaimer.\n * 2. Redistributions in binary form must reproduce the above copyright\n *    notice, this list of conditions and the following disclaimer in the\n *    documentation and/or other materials provided with the distribution.\n * 3. All advertising materials mentioning features or use of this software\n *    must display the following acknowledgement:\n *    \"This product includes cryptographic software written by\n *     <NAME> (<EMAIL>)\"\n *    The word 'cryptographic' can be left out if the rouines from the library\n *    being used are not cryptographic related :-).\n * 4. If you include any Windows specific code (or a derivative thereof) from\n *    the apps directory (application code) you must include an acknowledgement:\n *    \"This product includes software written by <NAME> (<EMAIL>)\"\n *\n * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE\n * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS\n * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)\n * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT\n * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY\n * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF\n * SUCH DAMAGE.\n *\n * The licence and distribution terms for any publically available version or\n * derivative of this code cannot be changed.  i.e. this code cannot simply be\n * copied and put under another distribution licence\n * [including the GNU Public Licence.]\n */\n\n#include <stdio.h>\n#include \"cryptlib.h\"\n#include <openssl/asn1t.h>\n#include <openssl/objects.h>\n\nint ASN1_TYPE_get(ASN1_TYPE *a)\n{\n    if ((a->value.ptr != NULL) || (a->type == V_ASN1_NULL))\n        return (a->type);\n    else\n        return (0);\n}\n\nvoid ASN1_TYPE_set(ASN1_TYPE *a, int type, void *value)\n{\n    if (a->value.ptr != NULL) {\n        ASN1_TYPE **tmp_a = &a;\n        ASN1_primitive_free((ASN1_VALUE **)tmp_a, NULL);\n    }\n    a->type = type;\n    if (type == V_ASN1_BOOLEAN)\n        a->value.boolean = value ? 0xff : 0;\n    else\n        a->value.ptr = value;\n}\n\nint ASN1_TYPE_set1(ASN1_TYPE *a, int type, const void *value)\n{\n    if (!value || (type == V_ASN1_BOOLEAN)) {\n        void *p = (void *)value;\n        ASN1_TYPE_set(a, type, p);\n    } else if (type == V_ASN1_OBJECT) {\n        ASN1_OBJECT *odup;\n        odup = OBJ_dup(value);\n        if (!odup)\n            return 0;\n        ASN1_TYPE_set(a, type, odup);\n    } else {\n        ASN1_STRING *sdup;\n        sdup = ASN1_STRING_dup(value);\n        if (!sdup)\n            return 0;\n        ASN1_TYPE_set(a, type, sdup);\n    }\n    return 1;\n}\n\nIMPLEMENT_STACK_OF(ASN1_TYPE)\n\nIMPLEMENT_ASN1_SET_OF(ASN1_TYPE)\n\n/* Returns 0 if they are equal, != 0 otherwise. */\nint ASN1_TYPE_cmp(const ASN1_TYPE *a, const ASN1_TYPE *b)\n{\n    int result = -1;\n\n    if (!a || !b || a->type != b->type)\n        return -1;\n\n    switch (a->type) {\n    case V_ASN1_OBJECT:\n        result = OBJ_cmp(a->value.object, b->value.object);\n        break;\n    case V_ASN1_BOOLEAN:\n        result = a->value.boolean - b->value.boolean;\n        break;\n    case V_ASN1_NULL:\n        result = 0;             /* They do not have content. */\n        break;\n    case V_ASN1_INTEGER:\n    case V_ASN1_NEG_INTEGER:\n    case V_ASN1_ENUMERATED:\n    case V_ASN1_NEG_ENUMERATED:\n    case V_ASN1_BIT_STRING:\n    case V_ASN1_OCTET_STRING:\n    case V_ASN1_SEQUENCE:\n    case V_ASN1_SET:\n    case V_ASN1_NUMERICSTRING:\n    case V_ASN1_PRINTABLESTRING:\n    case V_ASN1_T61STRING:\n    case V_ASN1_VIDEOTEXSTRING:\n    case V_ASN1_IA5STRING:\n    case V_ASN1_UTCTIME:\n    case V_ASN1_GENERALIZEDTIME:\n    case V_ASN1_GRAPHICSTRING:\n    case V_ASN1_VISIBLESTRING:\n    case V_ASN1_GENERALSTRING:\n    case V_ASN1_UNIVERSALSTRING:\n    case V_ASN1_BMPSTRING:\n    case V_ASN1_UTF8STRING:\n    case V_ASN1_OTHER:\n    default:\n        result = ASN1_STRING_cmp((ASN1_STRING *)a->value.ptr,\n                                 (ASN1_STRING *)b->value.ptr);\n        break;\n    }\n\n    return result;\n}", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "/* Copyright (C) 2000-2015 Free Software Foundation, Inc.\n   This file is part of the GNU C Library.\n   Contributed by <NAME> <<EMAIL>>, 2000.\n\n   The GNU C Library is free software; you can redistribute it and/or\n   modify it under the terms of the GNU Lesser General Public\n   License as published by the Free Software Foundation; either\n   version 2.1 of the License, or (at your option) any later version.\n\n   The GNU C Library is distributed in the hope that it will be useful,\n   but WITHOUT ANY WARRANTY; without even the implied warranty of\n   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n   Lesser General Public License for more details.\n\n   You should have received a copy of the GNU Lesser General Public\n   License along with the GNU C Library; if not, see\n   <http://www.gnu.org/licenses/>.  */\n\n#include <stdlib.h>\n#include <stdio.h>\n#include <locale.h>\n\n#ifndef CHAR\n# define CHAR char\n# define L(str) str\n# define SSCANF sscanf\n#endif\n\nconst CHAR *str_double[] =\n{\n// ... 函数内容截断 ...\n\t  printf (\"double_tests2[%d] stopped at '%c' != '%c'\\n\",\n\t\t  i, c, double_tests2[i].residual);\n\t  result = 1;\n\t}\n    }\n\n  return result;\n}", "/* Copyright (C) 2000-2015 Free Software Foundation, Inc.\n   This file is part of the GNU C Library.\n   Contributed by <NAME> <<EMAIL>>, 2000.\n\n   The GNU C Library is free software; you can redistribute it and/or\n   modify it under the terms of the GNU Lesser General Public\n   License as published by the Free Software Foundation; either\n   version 2.1 of the License, or (at your option) any later version.\n\n   The GNU C Library is distributed in the hope that it will be useful,\n   but WITHOUT ANY WARRANTY; without even the implied warranty of\n   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n   Lesser General Public License for more details.\n\n   You should have received a copy of the GNU Lesser General Public\n   License along with the GNU C Library; if not, see\n   <http://www.gnu.org/licenses/>.  */\n\n#include <stdlib.h>\n#include <stdio.h>\n#include <locale.h>\n\n#ifndef CHAR\n# define CHAR char\n# define L(str) str\n# define SSCANF sscanf\n#endif\n\nconst CHAR *str_double[] =\n{\n// ... 函数内容截断 ...\n\t  printf (\"double_tests2[%d] stopped at '%c' != '%c'\\n\",\n\t\t  i, c, double_tests2[i].residual);\n\t  result = 1;\n\t}\n    }\n\n  return result;\n}", "", "", "", "", "", "/*\n * QEMU USB packet combining code (for input pipelining)\n *\n * Copyright(c) 2012 Red Hat, Inc.\n *\n * Red Hat Authors: <AUTHORS>\n *\n * This library is free software; you can redistribute it and/or\n * modify it under the terms of the GNU Lesser General Public\n * License as published by the Free Software Foundation; either\n * version 2.1 of the License, or (at your option) any later version.\n *\n * This library is distributed in the hope that it will be useful,\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n * Lesser General Public License for more details.\n *\n * You should have received a copy of the GNU Lesser General Public License\n * along with this program; if not, see <http://www.gnu.org/licenses/>.\n */\n#include \"qemu/osdep.h\"\n#include \"qemu/units.h\"\n#include \"hw/usb.h\"\n#include \"qemu/iov.h\"\n#include \"trace.h\"\n\nstatic void usb_combined_packet_add(USBCombinedPacket *combined, USBPacket *p)\n{\n    qemu_iovec_concat(&combined->iov, &p->iov, 0, p->iov.size);\n    QTAILQ_INSERT_TAIL(&combined->packets, p, combined_entry);\n    p->combined = combined;\n}\n\n/* Note will free combined when the last packet gets removed */\nstatic void usb_combined_packet_remove(USBCombinedPacket *combined,\n                                       USBPacket *p)\n{\n    assert(p->combined == combined);\n    p->combined = NULL;\n    QTAILQ_REMOVE(&combined->packets, p, combined_entry);\n    if (QTAILQ_EMPTY(&combined->packets)) {\n        qemu_iovec_destroy(&combined->iov);\n        g_free(combined);\n    }\n}\n\n/* Also handles completion of non combined packets for pipelined input eps */\nvoid usb_combined_input_packet_complete(USBDevice *dev, USBPacket *p)\n{\n    USBCombinedPacket *combined = p->combined;\n    USBEndpoint *ep = p->ep;\n    USBPacket *next;\n    int status, actual_length;\n    bool short_not_ok, done = false;\n\n    if (combined == NULL) {\n        usb_packet_complete_one(dev, p);\n        goto leave;\n    }\n\n    assert(combined->first == p && p == QTAILQ_FIRST(&combined->packets));\n\n    status = combined->first->status;\n    actual_length = combined->first->actual_length;\n    short_not_ok = QTAILQ_LAST(&combined->packets)->short_not_ok;\n\n    QTAILQ_FOREACH_SAFE(p, &combined->packets, combined_entry, next) {\n        if (!done) {\n            /* Distribute data over uncombined packets */\n            if (actual_length >= p->iov.size) {\n                p->actual_length = p->iov.size;\n            } else {\n                /* Send short or error packet to complete the transfer */\n                p->actual_length = actual_length;\n                done = true;\n            }\n            /* Report status on the last packet */\n            if (done || next == NULL) {\n                p->status = status;\n            } else {\n                p->status = USB_RET_SUCCESS;\n            }\n            p->short_not_ok = short_not_ok;\n            /* Note will free combined when the last packet gets removed! */\n            usb_combined_packet_remove(combined, p);\n            usb_packet_complete_one(dev, p);\n            actual_length -= p->actual_length;\n        } else {\n            /* Remove any leftover packets from the queue */\n            p->status = USB_RET_REMOVE_FROM_QUEUE;\n            /* Note will free combined on the last packet! */\n            dev->port->ops->complete(dev->port, p);\n        }\n    }\n    /* Do not use combined here, it has been freed! */\nleave:\n    /* Check if there are packets in the queue waiting for our completion */\n    usb_ep_combine_input_packets(ep);\n}\n\n/* May only be called for combined packets! */\nvoid usb_combined_packet_cancel(USBDevice *dev, USBPacket *p)\n{\n    USBCombinedPacket *combined = p->combined;\n    assert(combined != NULL);\n    USBPacket *first = p->combined->first;\n\n    /* Note will free combined on the last packet! */\n    usb_combined_packet_remove(combined, p);\n    if (p == first) {\n        usb_device_cancel_packet(dev, p);\n    }\n}\n\n/*\n * Large input transfers can get split into multiple input packets, this\n * function recombines them, removing the short_not_ok checks which all but\n * the last packet of such splits transfers have, thereby allowing input\n * transfer pipelining (which we cannot do on short_not_ok transfers)\n */\nvoid usb_ep_combine_input_packets(USBEndpoint *ep)\n{\n    USBPacket *p, *u, *next, *prev = NULL, *first = NULL;\n    USBPort *port = ep->dev->port;\n    int totalsize;\n\n    assert(ep->pipeline);\n    assert(ep->pid == USB_TOKEN_IN);\n\n    QTAILQ_FOREACH_SAFE(p, &ep->queue, queue, next) {\n        /* Empty the queue on a halt */\n        if (ep->halted) {\n            p->status = USB_RET_REMOVE_FROM_QUEUE;\n            port->ops->complete(port, p);\n            continue;\n        }\n\n        /* Skip packets already submitted to the device */\n        if (p->state == USB_PACKET_ASYNC) {\n            prev = p;\n            continue;\n        }\n        usb_packet_check_state(p, USB_PACKET_QUEUED);\n\n        /*\n         * If the previous (combined) packet has the short_not_ok flag set\n         * stop, as we must not submit packets to the device after a transfer\n         * ending with short_not_ok packet.\n         */\n        if (prev && prev->short_not_ok) {\n            break;\n        }\n\n        if (first) {\n            if (first->combined == NULL) {\n                USBCombinedPacket *combined = g_new0(USBCombinedPacket, 1);\n\n                combined->first = first;\n                QTAILQ_INIT(&combined->packets);\n                qemu_iovec_init(&combined->iov, 2);\n                usb_combined_packet_add(combined, first);\n            }\n            usb_combined_packet_add(first->combined, p);\n        } else {\n            first = p;\n        }\n\n        /* Is this packet the last one of a (combined) transfer? */\n        totalsize = (p->combined) ? p->combined->iov.size : p->iov.size;\n        if ((p->iov.size % ep->max_packet_size) != 0 || !p->short_not_ok ||\n                next == NULL ||\n                /* Work around for Linux usbfs bulk splitting + migration */\n                (totalsize == (16 * KiB - 36) && p->int_req) ||\n                /* Next package may grow combined package over 1MiB */\n                totalsize > 1 * MiB - ep->max_packet_size) {\n            usb_device_handle_data(ep->dev, first);\n            assert(first->status == USB_RET_ASYNC);\n            if (first->combined) {\n                QTAILQ_FOREACH(u, &first->combined->packets, combined_entry) {\n                    usb_packet_set_state(u, USB_PACKET_ASYNC);\n                }\n            } else {\n                usb_packet_set_state(first, USB_PACKET_ASYNC);\n            }\n            first = NULL;\n            prev = p;\n        }\n    }\n}", "", "", "", "", "", "", "", "", "", "", "", "", "", "_llcp_send_symm(struct nfc_dev *dev);\nint nfc_llcp_send_connect(struct nfc_llcp_sock *sock);\nint nfc_llcp_send_cc(struct nfc_llcp_sock *sock);\nint nfc_llcp_send_snl_sdres(struct nfc_llcp_local *local,\n\t\t\t    struct hlist_head *tlv_list, size_t tlvs_len);\nint nfc_llcp_send_snl_sdreq(struct nfc_llcp_local *local,\n\t\t\t    struct hlist_head *tlv_list, size_t tlvs_len);\nint nfc_llcp_send_dm(struct nfc_llcp_local *local, u8 ssap, u8 dsap, u8 reason);\nint nfc_llcp_send_disconnect(struct nfc_llcp_sock *sock);\nint nfc_llcp_send_i_frame(struct nfc_llcp_sock *sock,", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "1pad_tmpl);\n\tif (err) {\n\t\tcrypto_unregister_akcipher(&rsa);\n\t\treturn err;\n\t}\n\n\treturn 0;\n}\n\nstatic void __exit rsa_exit(void)\n{\n\tcrypto_unregister_akcipher(&rsa);\n\tcrypto_unregister_template(&rsa_pkcs1pad_tmpl);\n}\n\nmodule_init(rsa_init);\nmodule_exit(rsa_exit);\nMODULE_ALIAS_CRYPTO(\"rsa\");\nMODULE_LICENSE(\"GPL\");\nMODULE_DESCRIPTION(\"RSA generic algorithm\");", "/* SCTP kernel implementation\n * Copyright (c) 1999-2000 Cisco, Inc.\n * Copyright (c) 1999-2001 Motorola, Inc.\n * Copyright (c) 2002 International Business Machines, Corp.\n *\n * This file is part of the SCTP kernel implementation\n *\n// ... 函数内容截断 ...\n\t\t * If the receiver detects a partial chunk, it MUST drop\n\t\t * the chunk.\n\t\t *\n\t\t * Since the end of the chunk is past the end of our buffer\n\t\t * (which contains the whole packet, we can freely discard\n\t\t * the whole packet.\n\t\t */\n\t\tsctp_chunk_free(chunk);\n\t\tchunk = queue->in_progress = NULL;\n\n\t\treturn NULL;\n\t} else {\n\t\t/* We are at the end of the packet, so mark the chunk\n\t\t * in case we need to send a SACK.\n\t\t */\n\t\tchunk->end_of_packet = 1;\n\t}\n\n\tpr_debug(\"+++sctp_inq_pop+++ chunk:%p[%s], length:%d, skb->len:%d\\n\",\n\t\t chunk, sctp_cname(SCTP_ST_CHUNK(chunk->chunk_hdr->type)),\n\t\t ntohs(chunk->chunk_hdr->length), chunk->skb->len);\n\n\treturn chunk;\n}\n\n/* Set a top-half handler.\n *\n * Originally, we the top-half handler was scheduled as a BH.  We now\n * call the handler directly in sctp_inq_push() at a time that\n * we know we are lock safe.\n * The intent is that this routine will pull stuff out of the\n * inqueue and process it.\n */\nvoid sctp_inq_set_th_handler(struct sctp_inq *q, work_func_t callback)\n{\n\tINIT_WORK(&q->immediate, callback);\n}", "", "_FEATURE_OSD_RECOVERY_DELETES |\t\\\n\t CEPH_FEATURE_FS_BTIME |\t\t\\\n\t CEPH_FEATURE_FS_CHANGE_ATTR |\t\t\\\n\t CEPH_FEATURE_MSG_ADDR2 |\t\t\\\n\t CEPH_FEATURE_OSD_REPOP |\t\t\\\n\t CEPH_FEATURE_OSD_OBJECT_DIGEST |\t\\\n\t CEPH_FEATURE_OSD_TRANSACTION_MAY_LAYOUT |\t\\\n\t CEPH_FEATURE_OSD_BITWISE_HOBJ_SORT |\t\\\n\t CEPH_FEATURE_OSD_PROXY_WRITE_FEATURES |\t\\\n\t CEPH_FEATURE_OSD_HITSET_GMT |\t\t\\\n\t CEPH_FEATURE_HAMMER_0_94_4 |\t\t\\\n\t CEPH_FEATURE_FS_FILE_LAYOUT_V2 |\t\\\n\t CEPH_FEATURE_OSD_PROXY_FEATURES |\t\\\n\t CEPH_FEATURE_ERASURE_CODE_PLUGINS_V3 |\t\\\n\t CEPH_FEATURE_OSD_MIN_SIZE_RECOVERY |\t\\\n\t CEPH_FEATURE_MON_METADATA |\t\t\\\n\t CEPH_FEATURE_MON_ROUTE_OSDMAP |\t\\\n\t CEPH_FEATURE_OSDSUBOP_NO_SNAPCONTEXT |\t\\\n\t CEPH_FEATURE_MON_SCRUB |\t\t\\\n\t CEPH_FEATURE_MON_GV |\t\t\t\\\n\t CEPH_FEATURE_MON_NULLROUTE |\t\t\\\n\t CEPH_FEATURE_MON_SINGLE_PAXOS |\t\t\\\n\t CEPH_FEATURE_MON_ROUTE_OSDMAP |\t\\\n\t CEPH_FEATURE_OSD_ERASURE_CODES |\t\\\n\t CEPH_FEATURE_OSD_OSD_TMAP2OMAP |\t\\\n\t CEPH_FEATURE_OSD_SET_ALLOC_HINT |\t\\\n\t CEPH_FEATURE_OSD_FADVISE_FLAGS |\t\\\n\t CEPH_FEATURE_OSD_RECOVERY_DELETES |\t\\\n\t CEPH_FEATURE_CEPHX_V2)\n\n#endif", "", "", "", "", "", "", "#include <linux/mm.h>\n#include <linux/highmem.h>\n#include <linux/sched.h>\n#include <linux/hugetlb.h>\n\nstatic int walk_pte_range(pmd_t *pmd, unsigned long addr, unsigned long end,\n\t\t\t  struct mm_walk *walk)\n{\n\tpte_t *pte;\n\tint err = 0;\n\n\tpte = pte_offset_map(pmd, addr);\n\tfor (;;) {\n\t\terr = walk->pte_entry(pte, addr, addr + PAGE_SIZE, walk);\n\t\tif (err)\n\t\t       break;\n\t\taddr += PAGE_SIZE;\n\t\tif (addr == end)\n\t\t\tbreak;\n\t\tpte++;\n\t}\n\n\tpte_unmap(pte);\n\treturn err;\n}\n\nstatic int walk_pmd_range(pud_t *pud, unsigned long addr, unsigned long end,\n\t\t\t  struct mm_walk *walk)\n{\n\tpmd_t *pmd;\n// ... 函数内容截断 ...\n\twalk->vma = vma;\n\terr = walk_page_test(vma->vm_start, vma->vm_end, walk);\n\tif (err > 0)\n\t\treturn 0;\n\tif (err < 0)\n\t\treturn err;\n\treturn __walk_page_range(vma->vm_start, vma->vm_end, walk);\n}", "", "", "", "data = NULL;\n\treturn 0;\n}", "ndlc->t2_timer);\n\tndlc->t2_active = false;\n\tndlc->t1_active = false;\n\n\tskb_queue_purge(&ndlc->rcv_q);\n\tskb_queue_purge(&ndlc->send_q);\n}\nEXPORT_SYMBOL(ndlc_remove);", "io_probe,\n\t.remove\t\t= btsdio_remove,\n\t.id_table\t= btsdio_table,\n};\n\nmodule_sdio_driver(btsdio_driver);\n\nMODULE_AUTHOR(\"<NAME> <<EMAIL>>\");\nMODULE_DESCRIPTION(\"Generic Bluetooth SDIO driver ver \" VERSION);\nMODULE_VERSION(VERSION);\nMODULE_LICENSE(\"GPL\");", "", "", "", "", "frequency_stepsize\t= 125,\n\t\t.symbol_rate_min        = 1000000,  /* guessed */\n\t\t.symbol_rate_max        = 45000000, /* guessed */\n\t\t.caps =\tFE_CAN_FEC_1_2 | FE_CAN_FEC_2_3 | FE_CAN_FEC_3_4 |\n\t\t\tFE_CAN_FEC_5_6 | FE_CAN_FEC_7_8 | FE_CAN_FEC_AUTO |\n\t\t\tFE_CAN_QPSK\n\t},\n\n\t.release = ttusbdecfe_release,\n\n\t.set_frontend = ttusbdecfe_dvbs_set_frontend,\n\n\t.read_status = ttusbdecfe_dvbs_read_status,\n\n\t.diseqc_send_master_cmd = ttusbdecfe_dvbs_diseqc_send_master_cmd,\n\n\t.set_tone = ttusbdecfe_dvbs_set_tone,\n\n\t.set_voltage = ttusbdecfe_dvbs_set_voltage,\n};", ") {\n\t\t\tif (xz_err == XZ_STREAM_END && !outlen)\n\t\t\t\tbreak;\n\t\t\terofs_err(rq->sb, \"failed to decompress %d in[%u] out[%u]\",\n\t\t\t\t  xz_err, rq->inputsize, rq->outputsize);\n\t\t\terr = -EFSCORRUPTED;\n\t\t\tbreak;\n\t\t}\n\t}\n\n\t/* 4. push back LZMA stream context to the global list */\n\tspin_lock(&z_erofs_lzma_lock);\n\tstrm->next = z_erofs_lzma_head;\n\tz_erofs_lzma_head = strm;\n\tspin_unlock(&z_erofs_lzma_lock);\n\twake_up(&z_erofs_lzma_wq);\n\treturn err;\n}", "", "", "// SPDX-License-Identifier: GPL-2.0\n/*\n * Primary to Sideband (P2SB) bridge access support\n *\n * Copyright (c) 2017, 2021-2022 Intel Corporation.\n *\n * Authors: <AUTHORS>\n *\t    <NAME> <<EMAIL>>\n */\n\n#include <linux/bits.h>\n#include <linux/export.h>\n#include <linux/pci.h>\n#include <linux/platform_data/x86/p2sb.h>\n\n#include <asm/cpu_device_id.h>\n#include <asm/intel-family.h>\n\n#define P2SBC\t\t\t0xe0\n#define P2SBC_HIDE\t\tBIT(8)\n\n#define P2SB_DEVFN_DEFAULT\tPCI_DEVFN(31, 1)\n\nstatic const struct x86_cpu_id p2sb_cpu_ids[] = {\n\tX86_MATCH_INTEL_FAM6_MODEL(ATOM_GOLDMONT,\tPCI_DEVFN(13, 0)),\n\t{}\n};\n\n/*\n * Cache BAR0 of P2SB device functions 0 to 7.\n * TODO: The constant 8 is the number of functions that PCI specification\n *       defines. Same definitions exist tree-wide. Unify this definition and\n *       the other definitions then move to include/uapi/linux/pci.h.\n */\n#define NR_P2SB_RES_CACHE 8\n\nstruct p2sb_res_cache {\n\tu32 bus_dev_id;\n\tstruct resource res;\n};\n\nstatic struct p2sb_res_cache p2sb_resources[NR_P2SB_RES_CACHE];\n\nstatic int p2sb_get_devfn(unsigned int *devfn)\n{\n\tunsigned int fn = P2SB_DEVFN_DEFAULT;\n\tconst struct x86_cpu_id *id;\n\n\tid = x86_match_cpu(p2sb_cpu_ids);\n\tif (id)\n\t\tfn = (unsigned int)id->driver_data;\n\n\t*devfn = fn;\n\treturn 0;\n}\n\nstatic bool p2sb_valid_resource(struct resource *res)\n{\n\tif (res->flags)\n\t\treturn true;\n\n\treturn false;\n}\n\n/* Copy resource from the first BAR of the device in question */\nstatic void p2sb_read_bar0(struct pci_dev *pdev, struct resource *mem)\n{\n\tstruct resource *bar0 = &pdev->resource[0];\n\n\t/* Make sure we have no dangling pointers in the output */\n\tmemset(mem, 0, sizeof(*mem));\n\n\t/*\n\t * We copy only selected fields from the original resource.\n\t * Because a PCI device will be removed soon, we may not use\n\t * any allocated data, hence we may not copy any pointers.\n\t */\n\tmem->start = bar0->start;\n\tmem->end = bar0->end;\n\tmem->flags = bar0->flags;\n\tmem->desc = bar0->desc;\n}\n\nstatic void p2sb_scan_and_cache_devfn(struct pci_bus *bus, unsigned int devfn)\n{\n\tstruct p2sb_res_cache *cache = &p2sb_resources[PCI_FUNC(devfn)];\n\tstruct pci_dev *pdev;\n\n\tpdev = pci_scan_single_device(bus, devfn);\n\tif (!pdev)\n\t\treturn;\n\n\tp2sb_read_bar0(pdev, &cache->res);\n\tcache->bus_dev_id = bus->dev.id;\n\n\tpci_stop_and_remove_bus_device(pdev);\n}\n\nstatic int p2sb_scan_and_cache(struct pci_bus *bus, unsigned int devfn)\n{\n\tunsigned int slot, fn;\n\n\tif (PCI_FUNC(devfn) == 0) {\n\t\t/*\n\t\t * When function number of the P2SB device is zero, scan it and\n\t\t * other function numbers, and if devices are available, cache\n\t\t * their BAR0s.\n\t\t */\n\t\tslot = PCI_SLOT(devfn);\n\t\tfor (fn = 0; fn < NR_P2SB_RES_CACHE; fn++)\n\t\t\tp2sb_scan_and_cache_devfn(bus, PCI_DEVFN(slot, fn));\n\t} else {\n\t\t/* Scan the P2SB device and cache its BAR0 */\n\t\tp2sb_scan_and_cache_devfn(bus, devfn);\n\t}\n\n\tif (!p2sb_valid_resource(&p2sb_resources[PCI_FUNC(devfn)].res))\n\t\treturn -ENOENT;\n\n\treturn 0;\n}\n\nstatic struct pci_bus *p2sb_get_bus(struct pci_bus *bus)\n{\n\tstatic struct pci_bus *p2sb_bus;\n\n\tbus = bus ?: p2sb_bus;\n\tif (bus)\n\t\treturn bus;\n\n\t/* Assume P2SB is on the bus 0 in domain 0 */\n\tp2sb_bus = pci_find_bus(0, 0);\n\treturn p2sb_bus;\n}\n\n// ... 函数内容截断 ...\n * 0 on success or appropriate errno value on error.\n */\nint p2sb_bar(struct pci_bus *bus, unsigned int devfn, struct resource *mem)\n{\n\tstruct pci_dev *pdev_p2sb;\n\tunsigned int devfn_p2sb;\n\tu32 value = P2SBC_HIDE;\n\tint ret;\n\n\t/* Get devfn for P2SB device itself */\n\tret = p2sb_get_devfn(&devfn_p2sb);\n\tif (ret)\n\t\treturn ret;\n\n\t/* if @bus is NULL, use bus 0 in domain 0 */\n\tbus = bus ?: pci_find_bus(0, 0);\n\n\t/*\n\t * Prevent concurrent PCI bus scan from seeing the P2SB device and\n\t * removing via sysfs while it is temporarily exposed.\n\t */\n\tpci_lock_rescan_remove();\n\n\t/* Unhide the P2SB device, if needed */\n\tpci_bus_read_config_dword(bus, devfn_p2sb, P2SBC, &value);\n\tif (value & P2SBC_HIDE)\n\t\tpci_bus_write_config_dword(bus, devfn_p2sb, P2SBC, 0);\n\n\tpdev_p2sb = pci_scan_single_device(bus, devfn_p2sb);\n\tif (devfn)\n\t\tret = p2sb_scan_and_cache(bus, devfn);\n\telse\n\t\tret = p2sb_read_bar0(pdev_p2sb, mem);\n\tpci_stop_and_remove_bus_device(pdev_p2sb);\n\n\t/* Hide the P2SB device, if it was hidden */\n\tif (value & P2SBC_HIDE)\n\t\tpci_bus_write_config_dword(bus, devfn_p2sb, P2SBC, P2SBC_HIDE);\n\n\tpci_unlock_rescan_remove();\n\n\tif (ret)\n\t\treturn ret;\n\n\tif (mem->flags == 0)\n\t\treturn -ENODEV;\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(p2sb_bar);", "", "", "return 0;\n\n\treturn regulator_coupler_register(&tegra30_coupler.coupler);\n}\narch_initcall(tegra_regulator_coupler_init);", "", "req->execute = nvmet_execute_io_connect;\n\treturn 0;\n}", "7\", dissect_ber_oid_NULL_callback, proto_x509af, \"desEDE\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.18\", dissect_ber_oid_NULL_callback, proto_x509af, \"sha\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.19\", dissect_ber_oid_NULL_callback, proto_x509af, \"mdc-2\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.20\", dissect_ber_oid_NULL_callback, proto_x509af, \"dsaCommon\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.21\", dissect_ber_oid_NULL_callback, proto_x509af, \"dsaCommonWithSHA\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.22\", dissect_ber_oid_NULL_callback, proto_x509af, \"rsaKeyTransport\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.23\", dissect_ber_oid_NULL_callback, proto_x509af, \"keyed-hash-seal\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.24\", dissect_ber_oid_NULL_callback, proto_x509af, \"md2WithRSASignature\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.25\", dissect_ber_oid_NULL_callback, proto_x509af, \"md5WithRSASignature\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.26\", dissect_ber_oid_NULL_callback, proto_x509af, \"SHA-1\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.27\", dissect_ber_oid_NULL_callback, proto_x509af, \"dsaWithSHA1\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.28\", dissect_ber_oid_NULL_callback, proto_x509af, \"dsaWithCommonSHA1\");\n\tregister_ber_oid_dissector(\"1.3.14.3.2.29\", dissect_ber_oid_NULL_callback, proto_x509af, \"sha-1WithRSAEncryption\");\n\n\t/* these will generally be encoded as \";binary\" in LDAP */\n\n\tdissector_add_string(\"ldap.name\", \"cACertificate\", create_dissector_handle(dissect_x509af_Certificate_PDU, proto_x509af));\n\tdissector_add_string(\"ldap.name\", \"userCertificate\", create_dissector_handle(dissect_x509af_Certificate_PDU, proto_x509af));\n\n}", "2\");", "", "", "", "substream_to_rtd(substream);\n\tstruct lpaif_dmactl *dmactl = NULL;\n\tint ret = 0, id;\n\n\tswitch (cmd) {\n\tcase SNDRV_PCM_TRIGGER_START:\n\tcase SNDRV_PCM_TRIGGER_RESUME:\n\tcase SNDRV_PCM_TRIGGER_PAUSE_RELEASE:\n\t\t__lpass_platform_codec_intf_init(dai, substream);\n\t\tbreak;\n\tcase SNDRV_PCM_TRIGGER_STOP:\n\tcase SNDRV_PCM_TRIGGER_SUSPEND:\n\tcase SNDRV_PCM_TRIGGER_PAUSE_PUSH:\n\t\t__lpass_get_dmactl_handle(substream, dai, &dmactl, &id);\n\t\tif (!dmactl)\n\t\t\treturn -EINVAL;\n\n\t\tret = regmap_fields_write(dmactl->codec_enable, id, LPAIF_DMACTL_ENABLE_OFF);\n\t\tif (ret) {\n\t\t\tdev_err(soc_runtime->dev,\n\t\t\t\t\"error writing to dmactl codec_enable reg: %d\\n\", ret);\n\t\t\treturn ret;\n\t\t}\n\t\tbreak;\n\tdefault:\n\t\tret = -EINVAL;\n\t\tdev_err(soc_runtime->dev, \"%s: invalid %d interface\\n\", __func__, cmd);\n\t\tbreak;\n\t}\n\treturn ret;\n}\n\nconst struct snd_soc_dai_ops asoc_qcom_lpass_cdc_dma_dai_ops = {\n\t.startup\t= lpass_cdc_dma_daiops_startup,\n\t.shutdown\t= lpass_cdc_dma_daiops_shutdown,\n\t.hw_params\t= lpass_cdc_dma_daiops_hw_params,\n\t.trigger\t= lpass_cdc_dma_daiops_trigger,\n};\nEXPORT_SYMBOL_GPL(asoc_qcom_lpass_cdc_dma_dai_ops);\n\nMODULE_DESCRIPTION(\"QTi LPASS CDC DMA Driver\");\nMODULE_LICENSE(\"GPL\");", "ancestor_size);\n}\n\n#endif /* _IPV6_H */", "*pool)\n{\n}\n\nstatic inline void xsk_buff_raw_dma_sync_for_device(struct xsk_buff_pool *pool,\n\t\t\t\t\t\t    dma_addr_t dma,\n\t\t\t\t\t\t    size_t size)\n{\n}\n\n#endif /* CONFIG_XDP_SOCKETS */\n\n#endif /* _LINUX_XDP_SOCK_DRV_H */", ") {\n\t\tif ((desc->completion->status & DSA_COMP_STATUS_MASK) != DSA_COMP_SUCCESS)\n\t\t\tmatch_fault(desc, data);\n\t\tcomplete_desc(desc, reason);\n\t}\n\n\treturn queued;\n}\n\nstatic int idxd_desc_process(struct idxd_irq_entry *irq_entry)\n{\n\tint rc, processed, total = 0;\n\n\t/*\n\t * There are two lists we are processing. The pending_llist is where\n\t * submmiter adds all the submitted descriptor after sending it to\n\t * the workqueue. It's a lockless singly linked list. The work_list\n\t * is the common linux double linked list. We are in a scenario of\n\t * multiple producers and a single consumer. The producers are all\n\t * the kernel submitters of descriptors, and the consumer is the\n\t * kernel irq handler thread for the msix vector when using threaded\n\t * irq. To work with the restrictions of llist to remain lockless,\n\t * we are doing the following steps:\n\t * 1. Iterate through the work_list and process any completed\n\t *    descriptor. Delete the completed entries during iteration.\n\t * 2. llist_del_all() from the pending list.\n\t * 3. Iterate through the llist that was deleted from the pending list\n\t *    and process the completed entries.\n\t * 4. If the entry is still waiting on hardware, list_add_tail() to\n\t *    the work_list.\n\t * 5. Repeat until no more descriptors.\n\t */\n\tdo {\n\t\trc = irq_process_work_list(irq_entry, IRQ_WORK_NORMAL, &processed, 0);\n\t\ttotal += processed;\n\t\tif (rc != 0)\n\t\t\tcontinue;\n\n\t\trc = irq_process_pending_llist(irq_entry, IRQ_WORK_NORMAL, &processed, 0);\n\t\ttotal += processed;\n\t\tif (rc != 0)\n\t\t\tcontinue;\n\n\t\trc = irq_process_work_list(irq_entry, IRQ_WORK_DEV_FAIL, &processed,\n\t\t\t\t\t  irq_entry->fault_addr);\n\t\ttotal += processed;\n\t\tif (rc != 0)\n\t\t\tcontinue;\n\n\t\trc = irq_process_pending_llist(irq_entry, IRQ_WORK_DEV_FAIL, &processed,\n\t\t\t\t\t      irq_entry->fault_addr);\n\t\ttotal += processed;\n\t} while (rc != 0);\n\n\treturn total;\n}", "_free_limb_space(tspace);\n\treturn rc;\n}\nEXPORT_SYMBOL_GPL(mpi_powm);", "", "", "134_boards[dev->board].ts_force_val << 4));\n\t\tbreak;\n\t}\n\tdev->ts_started = 1;\n\treturn 0;\n}", "", "", "", "EVP_add_cipher_alias(SN_aes_192_cbc,\"AES192\");\n\tEVP_add_cipher_alias(SN_aes_192_cbc,\"aes192\");\n\tEVP_add_cipher(EVP_aes_256_ctr());\n\tEVP_add_cipher(EVP_aes_256_gcm());\n\tEVP_add_cipher(EVP_aes_256_xts());\n\tEVP_add_cipher_alias(SN_aes_256_cbc,\"AES256\");\n\tEVP_add_cipher_alias(SN_aes_256_cbc,\"aes256\");\n#if !defined(OPENSSL_NO_SHA) && !defined(OPENSSL_NO_SHA1)\n\tEVP_add_cipher(EVP_aes_128_cbc_hmac_sha1());\n\tEVP_add_cipher(EVP_aes_256_cbc_hmac_sha1());\n#endif\n#endif\n\n#ifndef OPENSSL_NO_ARIA\n\tEVP_add_cipher(EVP_aria_128_ecb());\n\tEVP_add_cipher(EVP_aria_128_cbc());\n\tEVP_add_cipher(EVP_aria_128_cfb());\n\tEVP_add_cipher(EVP_aria_128_cfb1());\n\tEVP_add_cipher(EVP_aria_128_cfb8());\n\tEVP_add_cipher(EVP_aria_128_ofb());\n\tEVP_add_cipher_alias(SN_aria_128_cbc,\"ARIA128\");\n\tEVP_add_cipher_alias(SN_aria_128_cbc,\"aria128\");\n\tEVP_add_cipher(EVP_aria_192_ecb());\n\tEVP_add_cipher(EVP_aria_192_cbc());\n\tEVP_add_cipher(EVP_aria_192_cfb());\n\tEVP_add_cipher(EVP_aria_192_cfb1());\n\tEVP_add_cipher(EVP_aria_192_cfb8());\n\tEVP_add_cipher(EVP_aria_192_ofb());\n\tEVP_add_cipher_alias(SN_aria_192_cbc,\"ARIA192\");\n\tEVP_add_cipher_alias(SN_aria_192_cbc,\"aria192\");\n\tEVP_add_cipher(EVP_aria_256_ecb());\n\tEVP_add_cipher(EVP_aria_256_cbc());\n\tEVP_add_cipher(EVP_aria_256_cfb());\n\tEVP_add_cipher(EVP_aria_256_cfb1());\n\tEVP_add_cipher(EVP_aria_256_cfb8());\n\tEVP_add_cipher(EVP_aria_256_ofb());\n\tEVP_add_cipher_alias(SN_aria_256_cbc,\"ARIA256\");\n\tEVP_add_cipher_alias(SN_aria_256_cbc,\"aria256\");\n#endif\n\n#ifndef OPENSSL_NO_CHACHA\n\tEVP_add_cipher(EVP_chacha20());\n\tEVP_add_cipher(EVP_chacha20_poly1305());\n#endif\n\n#ifndef OPENSSL_NO_SM4\n\tEVP_add_cipher(EVP_sm4_ecb());\n\tEVP_add_cipher(EVP_sm4_cbc());\n\tEVP_add_cipher(EVP_sm4_cfb());\n\tEVP_add_cipher(EVP_sm4_ofb());\n\tEVP_add_cipher_alias(SN_sm4_cbc,\"SM4\");\n\tEVP_add_cipher_alias(SN_sm4_cbc,\"sm4\");\n#endif\n\n#ifndef OPENSSL_NO_BLAKE2\n\tEVP_add_cipher(EVP_blake2s256());\n\tEVP_add_cipher(EVP_blake2b512());\n#endif\n\n#ifndef OPENSSL_NO_CAMELLIA\n\tEVP_add_cipher(EVP_camellia_256_cfb());\n\tEVP_add_cipher(EVP_camellia_256_cfb1());\n\tEVP_add_cipher(EVP_camellia_256_cfb8());\n\tEVP_add_cipher(EVP_camellia_256_ofb());\n\tEVP_add_cipher_alias(SN_camellia_256_cbc,\"CAMELLIA256\");\n\tEVP_add_cipher_alias(SN_camellia_256_cbc,\"camellia256\");\n#endif\n\t}", "", "+= y - 1;\n\tdo_div(x, y);\n\treturn x * y;\n}\n\nstatic inline __uint64_t howmany_64(__uint64_t x, __uint32_t y)\n{\n\tx += y - 1;\n\tdo_div(x, y);\n\treturn x;\n}\n\n#define ASSERT_ALWAYS(expr)\t\\\n\t(likely(expr) ? (void)0 : assfail(#expr, __FILE__, __LINE__))\n\n#ifdef DEBUG\n#define ASSERT(expr)\t\\\n\t(likely(expr) ? (void)0 : assfail(#expr, __FILE__, __LINE__))\n\n#ifndef STATIC\n# define STATIC noinline\n#endif\n\n#else\t/* !DEBUG */\n\n#ifdef XFS_WARN\n\n#define ASSERT(expr)\t\\\n\t(likely(expr) ? (void)0 : asswarn(#expr, __FILE__, __LINE__))\n\n#ifndef STATIC\n# define STATIC static noinline\n#endif\n\n#else\t/* !DEBUG && !XFS_WARN */\n\n#define ASSERT(expr)\t((void)0)\n\n#ifndef STATIC\n# define STATIC static noinline\n#endif\n\n#endif /* XFS_WARN */", "", "(void)\n{\n\tdissector_handle_t msnip_handle;\n\n\tmsnip_handle = create_dissector_handle(dissect_msnip, proto_msnip);\n\tdissector_add_uint(\"igmp.type\", IGMP_TYPE_0x23, msnip_handle);\n\tdissector_add_uint(\"igmp.type\", IGMP_TYPE_0x24, msnip_handle);\n\tdissector_add_uint(\"igmp.type\", IGMP_TYPE_0x25, msnip_handle);\n}\n\n/*\n * Editor modelines  -  http://www.wireshark.org/tools/modelines.html\n *\n * Local variables:\n * c-basic-offset: 8\n * tab-width: 8\n * indent-tabs-mode: t\n * End:\n *\n * vi: set shiftwidth=8 tabstop=8 noexpandtab:\n * :indentSize=8:tabSize=8:noTabs=false:\n */", "", "rc4_hmac_md5_gettable_params },\n    { OSSL_FUNC_CIPHER_GET_CTX_PARAMS,\n// ... 函数内容截断 ...\n    }\n    p = OSSL_PARAM_locate_const(params, OSSL_CIPHER_PARAM_AEAD_TLS1_AAD);\n    if (p != NULL) {\n        if (p->data_type != OSSL_PARAM_OCTET_STRING) {\n            ERR_raise(ERR_LIB_PROV, PROV_R_FAILED_TO_GET_PARAMETER);\n            return 0;\n        }\n        GET_HW(ctx)->init_mackey(&ctx->base, p->data, p->data_size);\n    }\n    p = OSSL_PARAM_locate_const(params, OSSL_CIPHER_PARAM_TLS_VERSION);\n    if (p != NULL) {\n        if (!OSSL_PARAM_get_uint(p, &ctx->base.tlsversion)) {\n            ERR_raise(ERR_LIB_PROV, PROV_R_FAILED_TO_GET_PARAMETER);\n            return 0;\n        }\n    }\n\n    return 1;\n}", "_p = key_user_first(seq_user_ns(p), &key_user_tree);\n\twhile (pos > 0 && _p) {\n\t\tpos--;\n\t\t_p = key_user_next(seq_user_ns(p), _p);\n\t}\n\n\treturn _p;\n}\n\nstatic void *proc_key_users_next(struct seq_file *p, void *v, loff_t *_pos)\n{\n\t(*_pos)++;\n\treturn key_user_next(seq_user_ns(p), (struct rb_node *)v);\n}\n\nstatic void proc_key_users_stop(struct seq_file *p, void *v)\n\t__releases(key_user_lock)\n{\n\tspin_unlock(&key_user_lock);\n}\n\nstatic int proc_key_users_show(struct seq_file *m, void *v)\n{\n\tstruct rb_node *_p = v;\n\tstruct key_user *user = rb_entry(_p, struct key_user, node);\n\tunsigned maxkeys = uid_eq(user->uid, GLOBAL_ROOT_UID) ?\n\t\tkey_quota_root_maxkeys : key_quota_maxkeys;\n\tunsigned maxbytes = uid_eq(user->uid, GLOBAL_ROOT_UID) ?\n\t\tkey_quota_root_maxbytes : key_quota_maxbytes;\n\n\tseq_printf(m, \"%5u: %5d %d/%d %d/%d %d/%d\\n\",\n\t\t   from_kuid_munged(seq_user_ns(m), user->uid),\n\t\t   atomic_read(&user->usage),\n\t\t   atomic_read(&user->nikeys),\n\t\t   user->qnkeys,\n\t\t   maxkeys,\n\t\t   user->qnbytes,\n\t\t   maxbytes);\n\treturn 0;\n}", "", "th->alive = 1;\n\tspk_ttyio_synth = synth;\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(spk_ttyio_synth_probe);\n\nvoid spk_ttyio_release(void)\n{\n\tif (!speakup_tty)\n\t\treturn;\n\n\ttty_lock(speakup_tty);\n\n\tif (speakup_tty->ops->close)\n\t\tspeakup_tty->ops->close(speakup_tty, NULL);\n\n\ttty_ldisc_flush(speakup_tty);\n\ttty_unlock(speakup_tty);\n// ... 函数内容截断 ...", "", "mslots(kvm);\n\n\tkvm_for_each_memslot(i, slots)\n\t\tkvm_iommu_unmap_pages(kvm, slots->memslots[i]);\n\n\tsrcu_read_unlock(&kvm->srcu, idx);\n\n\treturn 0;\n}\n\nint kvm_iommu_unmap_guest(struct kvm *kvm)\n{\n\tstruct iommu_domain *domain = kvm->arch.iommu_domain;\n\n\t/* check if iommu exists and in use */\n\tif (!domain)\n\t\treturn 0;\n\n\tkvm_iommu_unmap_memslots(kvm);\n\tiommu_domain_free(domain);\n\tkvm->arch.iommu_domain = NULL;\n\n\treturn 0;\n}", "FIXME should we update ctime ?\n\t\t\t\t * What is the following setxattr update the\n\t\t\t\t * mode ?\n\t\t\t\t */\n\t\t\t\tiattr.ia_valid = ATTR_MODE;\n\t\t\t\tiattr.ia_mode = mode;\n\t\t\t\tv9fs_vfs_setattr_dotl(dentry, &iattr);\n\t\t\t}\n\t\t}\n\t\tbreak;\n\tcase ACL_TYPE_DEFAULT:\n\t\tif (!S_ISDIR(inode->i_mode)) {\n\t\t\tretval = acl ? -EINVAL : 0;\n\t\t\tgoto err_out;\n\t\t}\n\t\tbreak;\n\tdefault:\n\t\tBUG();\n\t}\n\tretval = v9fs_remote_set_acl(dentry, name, value, size, flags, type);\n\tif (!retval)\n\t\tset_cached_acl(inode, type, acl);\nerr_out:\n\tposix_acl_release(acl);\n\treturn retval;\n};", "); i++) {\n\tred2[i] = ~red2[i];\n\tgreen2[i] = ~green2[i];\n\tblue2[i] = ~blue2[i];\n    }\n    for (i = 0; i < ARRAY_SIZE(red4); i++) {\n\tred4[i] = ~red4[i];\n\tgreen4[i] = ~green4[i];\n\tblue4[i] = ~blue4[i];\n    }\n    for (i = 0; i < ARRAY_SIZE(red8); i++) {\n\tred8[i] = ~red8[i];\n\tgreen8[i] = ~green8[i];\n\tblue8[i] = ~blue8[i];\n    }\n    for (i = 0; i < ARRAY_SIZE(red16); i++) {\n\tred16[i] = ~red16[i];\n\tgreen16[i] = ~green16[i];\n\tblue16[i] = ~blue16[i];\n    }\n}", "bond_mask = lan966x_lag_get_mask(lan966x, lag);\n\treturn !bond_mask || !bond_mask & ~BIT(port->chip_port);\n}", "info(\"path-MTU clamping only supported in \"\n\t\t\t\"FORWARD, OUTPUT and POSTROUTING hooks\\n\");\n\t\treturn -<PERSON>IN<PERSON>L;\n\t}\n\txt_ematch_foreach(ematch, e)\n\t\tif (find_syn_match(ematch))\n\t\t\treturn 0;\n\tpr_info(\"Only works on TCP SYN packets\\n\");\n\treturn -EINVAL;\n}\n\nstatic int __init tcpmss_tg_init(void)\n{\n\treturn xt_register_targets(tcpmss_tg_reg, ARRAY_SIZE(tcpmss_tg_reg));\n}\n\nstatic void __exit tcpmss_tg_exit(void)\n{\n\txt_unregister_targets(tcpmss_tg_reg, ARRAY_SIZE(tcpmss_tg_reg));\n}\n\nmodule_init(tcpmss_tg_init);\nmodule_exit(tcpmss_tg_exit);", "-EIO;\n\t\tgoto out;\n\t}\n\n\t/* check if there is a pending command */\n\tif (unlikely(wmi->cmd_rsp_buf != NULL)) {\n\t\tret = -EBUSY;\n\t\tgoto out;\n\t}\n\n\twmi->cmd_rsp_buf = rsp_buf;\n\twmi->cmd_rsp_len = rsp_len;\n\n\tret = ath9k_wmi_cmd_issue(wmi, skb, cmd_id, cmd_len);\n\tif (ret)\n\t\tgoto out;\n\n\ttime_left = wait_for_completion_timeout(&wmi->cmd_wait,\n\t\t\t\t\t\tmsecs_to_jiffies(timeout));\n\tif (time_left == 0) {\n\t\tath_dbg(common, WMI, \"WMI command %s timed out\\n\",\n\t\t\twmi_cmd_to_name(cmd_id));\n\t\tret = -ETIMEDOUT;\n\t\tgoto out;\n\t}\n\n\tif (unlikely(wmi->cmd_rsp_len != rsp_len)) {\n\t\tath_dbg(common, WMI, \"WMI command %s failed\\n\",\n\t\t\twmi_cmd_to_name(cmd_id));\n\t\tret = -EIO;\n\t\tgoto out;\n\t}\n\nout:\n\twmi->cmd_rsp_buf = NULL;\n\twmi->cmd_rsp_len = 0;\n\tmutex_unlock(&wmi->op_mutex);\n\n\treturn ret;\n}\n\nstatic const char *wmi_cmd_to_name(enum wmi_cmd_id wmi_cmd)\n{\n\tswitch (wmi_cmd) {\n\tcase WMI_ECHO_CMDID:\n\t\treturn \"WMI_ECHO_CMDID\";\n\tcase WMI_ACCESS_MEMORY_CMDID:\n\t\treturn \"WMI_ACCESS_MEMORY_CMDID\";\n\tcase WMI_GET_FW_VERSION:\n\t\treturn \"WMI_GET_FW_VERSION\";\n\tcase WMI_DISABLE_INTR_CMDID:\n\t\treturn \"WMI_DISABLE_INTR_CMDID\";\n\tcase WMI_ENABLE_INTR_CMDID:\n\t\treturn \"WMI_ENABLE_INTR_CMDID\";\n\tcase WMI_ATH_INIT_CMDID:\n\t\treturn \"WMI_ATH_INIT_CMDID\";\n\tcase WMI_ABORT_TXQ_CMDID:\n\t\treturn \"WMI_ABORT_TXQ_CMDID\";\n\tcase WMI_STOP_TX_DMA_CMDID:\n\t\treturn \"WMI_STOP_TX_DMA_CMDID\";\n\tcase WMI_ABORT_TX_DMA_CMDID:\n\t\treturn \"WMI_ABORT_TX_DMA_CMDID\";\n\tcase WMI_DRAIN_TXQ_CMDID:\n\t\treturn \"WMI_DRAIN_TXQ_CMDID\";\n\tcase WMI_DRAIN_TXQ_ALL_CMDID:\n\t\treturn \"WMI_DRAIN_TXQ_ALL_CMDID\";\n\tcase WMI_START_RECV_CMDID:\n\t\treturn \"WMI_START_RECV_CMDID\";\n\tcase WMI_STOP_RECV_CMDID:\n\t\treturn \"WMI_STOP_RECV_CMDID\";\n\tcase WMI_FLUSH_RECV_CMDID:\n\t\treturn \"WMI_FLUSH_RECV_CMDID\";\n\tcase WMI_SET_MODE_CMDID:\n\t\treturn \"WMI_SET_MODE_CMDID\";\n\tcase WMI_NODE_CREATE_CMDID:\n\t\treturn \"WMI_NODE_CREATE_CMDID\";\n\tcase WMI_NODE_REMOVE_CMDID:\n\t\treturn \"WMI_NODE_REMOVE_CMDID\";\n\tcase WMI_VAP_REMOVE_CMDID:\n\t\treturn \"WMI_VAP_REMOVE_CMDID\";\n\tcase WMI_VAP_CREATE_CMDID:\n\t\treturn \"WMI_VAP_CREATE_CMDID\";\n\tcase WMI_REG_READ_CMDID:\n\t\treturn \"WMI_REG_READ_CMDID\";\n\tcase WMI_REG_WRITE_CMDID:\n\t\treturn \"WMI_REG_WRITE_CMDID\";\n\tcase WMI_REG_RMW_CMDID:\n\t\treturn \"WMI_REG_RMW_CMDID\";\n\tcase WMI_RC_STATE_CHANGE_CMDID:\n\t\treturn \"WMI_RC_STATE_CHANGE_CMDID\";\n\tcase WMI_RC_RATE_UPDATE_CMDID:\n\t\treturn \"WMI_RC_RATE_UPDATE_CMDID\";\n\tcase WMI_TARGET_IC_UPDATE_CMDID:\n\t\treturn \"WMI_TARGET_IC_UPDATE_CMDID\";\n\tcase WMI_TX_AGGR_ENABLE_CMDID:\n\t\treturn \"WMI_TX_AGGR_ENABLE_CMDID\";\n\tcase WMI_TGT_DETACH_CMDID:\n\t\treturn \"WMI_TGT_DETACH_CMDID\";\n\tcase WMI_NODE_UPDATE_CMDID:\n\t\treturn \"WMI_NODE_UPDATE_CMDID\";\n\tcase WMI_INT_STATS_CMDID:\n\t\treturn \"WMI_INT_STATS_CMDID\";\n\tcase WMI_TX_STATS_CMDID:\n\t\treturn \"WMI_TX_STATS_CMDID\";\n\tcase WMI_RX_STATS_CMDID:\n\t\treturn \"WMI_RX_STATS_CMDID\";\n\tcase WMI_BITRATE_MASK_CMDID:\n\t\treturn \"WMI_BITRATE_MASK_CMDID\";\n\t}\n\n\treturn \"Bogus\";\n}", "", "], PL022State),\n        VMSTATE_END_OF_LIST()\n    }\n};\n\nstatic void pl022_register_types(void)\n{\n    type_register_static(&pl022_info);\n}\n\ntype_init(pl022_register_types)", "return ret;\n\t}\n\n\tinfo(\"found a '%s' in warm state.\", desc->name);\n\td = kzalloc(sizeof(struct dvb_usb_device), GFP_KERNEL);\n\tif (d == NULL) {\n\t\terr(\"no memory for 'struct dvb_usb_device'\");\n\t\treturn -ENOMEM;\n\t}\n\n\td->udev = udev;\n\tmemcpy(&d->props, props, sizeof(struct dvb_usb_device_properties));\n\td->desc = desc;\n\td->owner = owner;\n\n\tusb_set_intfdata(intf, d);\n\n\tif (du != NULL)\n\t\t*du = d;\n\n\tret = dvb_usb_init(d, adapter_nums);\n\n\tif (ret == 0)\n\t\tinfo(\"%s successfully initialized and connected.\", desc->name);\n\telse\n\t\tinfo(\"%s error while loading driver (%d)\", desc->name, ret);\n\treturn ret;\n}\nEXPORT_SYMBOL(dvb_usb_device_exit);\n\nMODULE_VERSION(\"1.0\");\nMODULE_AUTHOR(\"<NAME> <<EMAIL>>\");\nMODULE_DESCRIPTION(\"A library module containing commonly used USB and DVB function USB DVB devices\");\nMODULE_LICENSE(\"GPL\");", "", "t7411_data *data = i2c_get_clientdata(client);\n\n\thwmon_device_unregister(data->hwmon_dev);\n\tsysfs_remove_group(&client->dev.kobj, &adt7411_attr_grp);\n\n\treturn 0;\n}\n\nstatic const struct i2c_device_id adt7411_id[] = {\n\t{\"adt7411\", 0},\n\t{}\n};\nMODULE_DEVICE_TABLE(i2c, adt7411_id);\n\nstatic struct i2c_driver adt7411_driver = {\n\t.class = I2C_CLASS_HWMON,\n\t.driver = {\n\t\t.name = \"adt7411\",\n\t},\n\t.probe = adt7411_probe,\n\t.remove = adt7411_remove,\n\t.id_table = adt7411_id,\n};\n\nstatic int __init adt7411_init(void)\n{\n\treturn i2c_add_driver(&adt7411_driver);\n}\n\nstatic void __exit adt7411_exit(void)\n{\n\ti2c_del_driver(&adt7411_driver);\n}\n\nMODULE_AUTHOR(\"<NAME> <<EMAIL>>\");\nMODULE_DESCRIPTION(\"ADT7411 driver\");\nMODULE_LICENSE(\"GPL\");\n\nmodule_init(adt7411_init);\nmodule_exit(adt7411_exit);", "_unmap(page, flags)\t(0)\n\n#endif\t/* !CONFIG_MMU */", "struct kstat stat;\n\t\tenum ovl_path_type type = ovl_path_type(dentry);\n\n\t\tif (OVL_TYPE_UPPER(type))\n\t\t\tbreak;\n\n\t\tnext = dget(dentry);\n\t\t/* find the topmost dentry not yet copied up */\n\t\tfor (;;) {\n\t\t\tparent = dget_parent(next);\n\n\t\t\ttype = ovl_path_type(parent);\n\t\t\tif (OVL_TYPE_UPPER(type))\n\t\t\t\tbreak;\n\n\t\t\tdput(next);\n\t\t\tnext = parent;\n\t\t}\n\n\t\tovl_path_lower(next, &lowerpath);\n\t\terr = vfs_getattr(&lowerpath, &stat);\n// ... 函数内容截断 ...\nout_put_cred:\n\trevert_creds(old_cred);\n\tput_cred(override_cred);\n\nout_free_link:\n\tif (link)\n\t\tfree_page((unsigned long) link);\n\n\treturn err;\n}", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]