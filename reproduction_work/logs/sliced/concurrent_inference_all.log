2025-07-31 07:47:02,759 - InferencePipeline - INFO - 🔮 推理管道初始化完成
2025-07-31 07:47:02,759 - InferencePipeline - INFO - 📁 输出目录: reproduction_work/inference_results/sliced
2025-07-31 07:47:02,759 - InferencePipeline - INFO - 🌐 API服务器: 2 个
2025-07-31 07:47:02,759 - InferencePipeline - INFO -    API-1: http://************:5001
2025-07-31 07:47:02,759 - InferencePipeline - INFO -    API-2: http://************:5002
2025-07-31 07:47:02,759 - InferencePipeline - INFO - ⚙️ 配置: {'api_base_url': 'http://************:5002', 'api_urls': ['http://************:5001', 'http://************:5002'], 'api_model_name': 'starcoder-lora', 'use_api_service': True, 'concurrent_mode': True}
2025-07-31 07:47:02,760 - InferencePipeline - INFO - 🚀 开始并发推理: reproduction_work/data/organized/sliced/custom_vim_neovim_quality_all_sliced.json
2025-07-31 07:47:02,760 - InferencePipeline - INFO - 🌐 使用 2 个API并发处理
