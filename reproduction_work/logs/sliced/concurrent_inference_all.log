2025-07-31 07:47:02,759 - InferencePipeline - INFO - 🔮 推理管道初始化完成
2025-07-31 07:47:02,759 - InferencePipeline - INFO - 📁 输出目录: reproduction_work/inference_results/sliced
2025-07-31 07:47:02,759 - InferencePipeline - INFO - 🌐 API服务器: 2 个
2025-07-31 07:47:02,759 - InferencePipeline - INFO -    API-1: http://************:5001
2025-07-31 07:47:02,759 - InferencePipeline - INFO -    API-2: http://************:5002
2025-07-31 07:47:02,759 - InferencePipeline - INFO - ⚙️ 配置: {'api_base_url': 'http://************:5002', 'api_urls': ['http://************:5001', 'http://************:5002'], 'api_model_name': 'starcoder-lora', 'use_api_service': True, 'concurrent_mode': True}
2025-07-31 07:47:02,760 - InferencePipeline - INFO - 🚀 开始并发推理: reproduction_work/data/organized/sliced/custom_vim_neovim_quality_all_sliced.json
2025-07-31 07:47:02,760 - InferencePipeline - INFO - 🌐 使用 2 个API并发处理
2025-07-31 07:47:17,109 - InferencePipeline - INFO - 📊 总样本数: 2548
2025-07-31 07:47:17,109 - InferencePipeline - INFO - 📦 数据分割为 2 个块
2025-07-31 07:47:17,109 - InferencePipeline - INFO -    块-1: 1274 个样本 -> API: http://************:5001
2025-07-31 07:47:17,109 - InferencePipeline - INFO -    块-2: 1274 个样本 -> API: http://************:5002
2025-07-31 07:47:17,109 - InferencePipeline - INFO - 🔄 多API并发处理模式
2025-07-31 07:47:17,110 - InferencePipeline - INFO - 🔄 API块-1 (http://************:5001) 开始处理 1274 个样本
2025-07-31 07:47:17,111 - InferencePipeline - INFO - 🔄 API块-2 (http://************:5002) 开始处理 1274 个样本
2025-07-31 07:47:17,722 - InferencePipeline - WARNING - ⚠️ Prompt过长(421818字符)，尝试截断...
2025-07-31 07:47:17,722 - InferencePipeline - WARNING - ⚠️ Prompt过长(171041字符)，尝试截断...
2025-07-31 07:47:17,723 - InferencePipeline - INFO - 📏 截断后长度: 421818字符
2025-07-31 07:47:17,724 - InferencePipeline - INFO - 📏 截断后长度: 171041字符
2025-07-31 07:47:17,724 - InferencePipeline - INFO - 🔄 API调用尝试 1/2...
2025-07-31 07:47:17,724 - InferencePipeline - INFO - 🔄 API调用尝试 1/2...
