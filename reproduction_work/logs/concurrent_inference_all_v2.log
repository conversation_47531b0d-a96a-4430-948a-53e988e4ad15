nohup: 忽略输入
======================================
🚀 多API并发推理系统启动
开始时间: 2025年 07月 31日 星期四 07:47:00 UTC
======================================
🔧 检测到 2 个API服务器
   API-1: http://10.150.10.76:5001
   API-2: http://10.150.10.76:5002
🔍 检查API服务器可用性...
测试 API-1 (http://10.150.10.76:5001)... ✅ 可用
测试 API-2 (http://10.150.10.76:5002)... ✅ 可用

🚀 自动启动并发模式: 多个API并发处理同一数据文件
   这样可以充分利用所有可用的API服务器
🚀 启动并发模式...
======================================
🚀 多API并发处理单一文件模式
数据文件: reproduction_work/data/organized/sliced/custom_vim_neovim_quality_all_sliced.json
数据类型: sliced
大小版本: all
使用API: 2 个
======================================
🚀 开始多API并发推理...
📝 日志文件: reproduction_work/logs/sliced/concurrent_inference_all.log
📁 所有结果将保存在: reproduction_work/inference_results/sliced
