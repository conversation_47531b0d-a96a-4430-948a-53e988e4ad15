#!/usr/bin/env python3
"""
数据字段分析脚本
用于对比源数据集和过滤后数据集的空字段情况，找出数据质量问题的根源
"""

import json
import os
from collections import defaultdict
from pathlib import Path

def analyze_empty_fields(data, dataset_name):
    """
    分析数据集中的空字段情况
    
    Args:
        data: 数据集列表
        dataset_name: 数据集名称
    
    Returns:
        dict: 空字段统计结果
    """
    print(f"\n🔍 分析 {dataset_name} 数据集...")
    
    if not data:
        print("❌ 数据集为空")
        return {}
    
    # 获取所有字段名
    if isinstance(data, list) and len(data) > 0:
        sample_record = data[0]
        all_fields = set(sample_record.keys()) if isinstance(sample_record, dict) else set()
    else:
        print("❌ 数据格式不正确")
        return {}
    
    print(f"📊 总记录数: {len(data)}")
    print(f"📋 字段总数: {len(all_fields)}")
    print(f"🔤 字段列表: {', '.join(sorted(all_fields))}")
    
    # 统计空字段
    empty_stats = defaultdict(int)
    field_samples = defaultdict(list)  # 存储每个字段的样本值
    
    for i, record in enumerate(data):
        if not isinstance(record, dict):
            continue
            
        for field in all_fields:
            value = record.get(field, "")
            
            # 判断是否为空
            is_empty = (
                value == "" or 
                value is None or 
                value == "unknown" or
                value == "null" or
                (isinstance(value, str) and value.strip() == "")
            )
            
            if is_empty:
                empty_stats[field] += 1
            else:
                # 收集非空样本值（仅取前3个作为示例）
                if len(field_samples[field]) < 3:
                    sample_value = str(value)[:100]  # 截取前100个字符
                    if sample_value not in field_samples[field]:
                        field_samples[field].append(sample_value)
    
    # 计算空字段比例
    total_records = len(data)
    empty_percentages = {
        field: (count / total_records) * 100 
        for field, count in empty_stats.items()
    }
    
    return {
        'total_records': total_records,
        'total_fields': len(all_fields),
        'empty_stats': dict(empty_stats),
        'empty_percentages': empty_percentages,
        'field_samples': dict(field_samples),
        'fields': sorted(all_fields)
    }

def compare_datasets(source_stats, filtered_stats):
    """
    对比两个数据集的空字段情况
    
    Args:
        source_stats: 源数据集统计结果
        filtered_stats: 过滤后数据集统计结果
    """
    print("\n" + "="*80)
    print("🔍 数据集对比分析")
    print("="*80)
    
    # 基础统计对比
    print(f"\n📊 基础统计对比:")
    print(f"{'指标':<25} {'源数据集':<15} {'过滤后数据集':<15} {'差异':<10}")
    print("-" * 70)
    
    source_records = source_stats['total_records']
    filtered_records = filtered_stats['total_records']
    record_diff = filtered_records - source_records
    
    print(f"{'总记录数':<25} {source_records:<15} {filtered_records:<15} {record_diff:<10}")
    print(f"{'字段数':<25} {source_stats['total_fields']:<15} {filtered_stats['total_fields']:<15} {filtered_stats['total_fields'] - source_stats['total_fields']:<10}")
    
    # 空字段对比
    print(f"\n📋 空字段详细对比:")
    print(f"{'字段名':<25} {'源数据集空比例':<20} {'过滤后空比例':<20} {'差异':<15} {'问题来源':<15}")
    print("-" * 100)
    
    # 获取所有字段的并集
    all_fields = set(source_stats['fields'] + filtered_stats['fields'])
    
    issues_found = []
    
    for field in sorted(all_fields):
        source_pct = source_stats['empty_percentages'].get(field, 0)
        filtered_pct = filtered_stats['empty_percentages'].get(field, 0)
        diff = filtered_pct - source_pct
        
        # 判断问题来源
        if abs(diff) < 0.1:
            issue_source = "源数据问题"
        elif diff > 0:
            issue_source = "过滤过程问题"
        else:
            issue_source = "数据改善"
        
        print(f"{field:<25} {source_pct:<20.1f}% {filtered_pct:<20.1f}% {diff:<15.1f}% {issue_source:<15}")
        
        # 记录需要关注的问题
        if source_pct > 50 or filtered_pct > 50 or abs(diff) > 10:
            issues_found.append({
                'field': field,
                'source_pct': source_pct,
                'filtered_pct': filtered_pct,
                'diff': diff,
                'issue_source': issue_source
            })
    
    # 生成问题总结
    print(f"\n🚨 问题总结:")
    if not issues_found:
        print("✅ 未发现严重的数据质量问题")
    else:
        print(f"❌ 发现 {len(issues_found)} 个需要关注的问题:")
        for issue in issues_found:
            print(f"   • {issue['field']}: {issue['issue_source']} (源: {issue['source_pct']:.1f}%, 过滤后: {issue['filtered_pct']:.1f}%)")
    
    return issues_found

def main():
    """主函数"""
    print("🔍 数据字段空值分析工具")
    print("="*80)
    
    # 数据文件路径
    source_path = "../../reproduction_work/data/custom/enhanced_ppathf_adapted.json"
    filtered_path = "../../src/PPatHF-main/PPatHF-main/data/backup_full_dataset_20250712_092137/custom_vim_neovim_test_all_processed.json"
    
    print(f"📁 源数据集路径: {source_path}")
    print(f"📁 过滤后数据集路径: {filtered_path}")
    
    # 检查文件是否存在
    if not os.path.exists(source_path):
        print(f"❌ 源数据集文件不存在: {source_path}")
        return
    
    if not os.path.exists(filtered_path):
        print(f"❌ 过滤后数据集文件不存在: {filtered_path}")
        return
    
    try:
        # 加载源数据集
        print(f"\n📖 加载源数据集...")
        with open(source_path, 'r', encoding='utf-8') as f:
            source_data = json.load(f)
        
        # 加载过滤后数据集
        print(f"📖 加载过滤后数据集...")
        with open(filtered_path, 'r', encoding='utf-8') as f:
            filtered_data = json.load(f)
        
        # 分析源数据集
        source_stats = analyze_empty_fields(source_data, "源数据集")
        
        # 分析过滤后数据集
        filtered_stats = analyze_empty_fields(filtered_data, "过滤后数据集")
        
        # 对比分析
        issues = compare_datasets(source_stats, filtered_stats)
        
        # 保存分析结果
        analysis_result = {
            'timestamp': '2025-01-12',
            'source_dataset': {
                'path': source_path,
                'stats': source_stats
            },
            'filtered_dataset': {
                'path': filtered_path,
                'stats': filtered_stats
            },
            'issues_found': issues,
            'summary': {
                'total_issues': len(issues),
                'records_comparison': {
                    'source': source_stats['total_records'],
                    'filtered': filtered_stats['total_records'],
                    'difference': filtered_stats['total_records'] - source_stats['total_records']
                }
            }
        }
        
        # 保存分析结果到文件
        output_path = "field_analysis_result.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 分析结果已保存到: {output_path}")
        
        # 生成结论
        print(f"\n🎯 结论:")
        if filtered_stats['total_records'] == source_stats['total_records']:
            print("✅ 记录数量一致，说明没有在过滤过程中丢失记录")
        else:
            print(f"⚠️  记录数量不一致，丢失了 {source_stats['total_records'] - filtered_stats['total_records']} 条记录")
        
        # 分析主要问题来源
        source_issues = sum(1 for issue in issues if issue['issue_source'] == '源数据问题')
        filter_issues = sum(1 for issue in issues if issue['issue_source'] == '过滤过程问题')
        
        print(f"🔍 问题来源分析:")
        print(f"   • 源数据集本身问题: {source_issues} 个字段")
        print(f"   • 过滤过程引入问题: {filter_issues} 个字段")
        
        if source_issues > filter_issues:
            print("💡 建议: 主要问题来自源数据集，需要改进GitHub API数据收集策略")
        else:
            print("💡 建议: 主要问题来自过滤过程，需要检查数据处理流程")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 