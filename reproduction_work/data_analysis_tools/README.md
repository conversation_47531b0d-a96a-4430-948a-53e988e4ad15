# 数据分析工具集

此目录包含了用于分析自建数据集质量问题的专用工具脚本。这些工具是在发现数据集中存在大量空字段问题时开发的，用于深入分析问题根源并提供解决方案。

## 🔧 工具列表

### 1. `data_field_analysis.py` - 数据字段对比分析工具
**用途**: 对比源数据集和过滤后数据集的空字段情况，找出数据质量问题的根源。

**功能**:
- 分析数据集中各字段的空值比例
- 对比源数据集和过滤后数据集的差异
- 生成详细的分析报告
- 判断问题来源（源数据问题 vs 过滤过程问题）

**使用方法**:
```bash
cd /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data_analysis_tools
python data_field_analysis.py
```

**输出文件**: `field_analysis_result.json`

### 2. `analyze_filtered_datasets.py` - 长度过滤数据集质量分析工具
**用途**: 分析长度过滤后各版本数据集的质量差异，找出过滤算法的问题。

**功能**:
- 分析不同长度限制版本数据集的有效记录比例
- 统计完全有效、部分有效、完全空记录的数量
- 计算空记录偏差，识别过滤算法问题
- 生成质量对比报告

**使用方法**:
```bash
cd /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data_analysis_tools
python analyze_filtered_datasets.py
```

**输出文件**: `filtered_datasets_analysis.json`

### 3. `fix_data_quality.py` - 数据质量修复工具
**用途**: 过滤掉空记录，生成高质量的数据集。

**功能**:
- 过滤掉所有空记录，保留100%有效记录
- 生成不同长度版本的高质量数据集
- 创建详细的修复报告
- 提供优化后的数据集使用建议

**使用方法**:
```bash
cd /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data_analysis_tools
python fix_data_quality.py
```

**输出目录**: `src/PPatHF-main/PPatHF-main/data/quality_fixed/`

## 📊 分析结果文件

### `field_analysis_result.json`
包含源数据集和过滤后数据集的详细对比分析结果。

**主要信息**:
- 两个数据集的基本统计信息
- 各字段的空值比例对比
- 问题来源分析结果

### `filtered_datasets_analysis.json`
包含不同长度版本数据集的质量分析结果。

**主要信息**:
- 各版本数据集的有效记录统计
- 空记录偏差分析
- 质量变化趋势

## 🎯 重要发现

通过这些工具的分析，我们发现了两层问题：

1. **原始数据收集问题 (17%)**:
   - 源数据集中有524条记录完全缺少函数代码
   - 主要原因：GitHub API限制、已删除commit、私有仓库等
   - 这是数据收集阶段的问题，不是过滤过程产生的

2. **长度过滤算法问题**:
   - 空记录因字符数少被优先选择进入短数据集
   - 导致过滤后数据集空记录比例急剧上升
   - 空记录偏差达到3.6-5.6倍

## 🛠️ 解决方案

基于分析结果，我们实施了以下解决方案：

1. **高质量数据集生成**: 使用 `fix_data_quality.py` 过滤掉所有空记录
2. **多规格版本提供**: 生成400/800/1200/2548条记录的不同版本
3. **质量提升**: 从82.1%有效率提升到100%有效率

## 📁 生成的高质量数据集

```
📁 reproduction_work/data/quality_fixed/
├── custom_vim_neovim_quality_all.json    # 2548条 (100%有效)
├── custom_vim_neovim_quality_2048.json   # 400条 (100%有效)
├── custom_vim_neovim_quality_4096.json   # 800条 (100%有效)
├── custom_vim_neovim_quality_8192.json   # 1200条 (100%有效)
└── quality_fix_report.json               # 详细修复报告
```

## 🚀 使用建议

### 推荐使用高质量数据集
```bash
# 切换到PPatHF工具目录
cd /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main

# 使用完整高质量数据集
python predict.py --data_path "../../reproduction_work/data/quality_fixed/custom_vim_neovim_quality_all.json"

# 或使用特定长度版本
python predict.py --data_path "data/quality_fixed/custom_vim_neovim_quality_4096.json"
```

## 📋 开发历史

- **2025-01-12**: 发现数据质量问题，用户询问空字段根源
- **2025-01-12**: 开发数据分析工具，找出问题根源
- **2025-01-12**: 实施解决方案，生成高质量数据集
- **2025-01-12**: 整理工具到专门目录，完善文档

## 🔗 相关文档

- 主要进度记录: `../../自建数据复现进度记录.md`
- 数据适配脚本: `../data_adapter.py`
- 原工具集成: `../original_tool_integration.py`

---

**注意**: 这些工具是针对特定的数据质量问题开发的，如果您的数据集格式不同，可能需要相应调整脚本中的字段名称和判断逻辑。 