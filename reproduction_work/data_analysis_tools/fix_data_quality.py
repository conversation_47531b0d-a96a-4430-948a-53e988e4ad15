#!/usr/bin/env python3
"""
修复数据质量问题：过滤掉空记录，生成高质量数据集
"""

import json
import os
from datetime import datetime

def is_valid_record(record):
    """判断记录是否有效"""
    required_fields = ['func_before_source', 'func_after_source', 'func_before_target', 'func_after_target']
    
    for field in required_fields:
        value = record.get(field, "")
        if (value == "" or 
            value is None or 
            value == "unknown" or
            value == "null" or
            (isinstance(value, str) and value.strip() == "")):
            return False
    
    return True

def filter_and_create_quality_datasets():
    """过滤并创建高质量数据集"""
    print("🔧 修复数据质量问题")
    print("="*80)
    
    # 加载完整数据集
    full_data_path = "../../src/PPatHF-main/PPatHF-main/data/backup_full_dataset_20250712_092137/custom_vim_neovim_test_all_processed.json"
    
    if not os.path.exists(full_data_path):
        print(f"❌ 完整数据集文件不存在: {full_data_path}")
        return
    
    print(f"📖 加载完整数据集: {full_data_path}")
    with open(full_data_path, 'r', encoding='utf-8') as f:
        full_data = json.load(f)
    
    print(f"📊 原始数据: {len(full_data)} 条记录")
    
    # 过滤出高质量记录
    print("🔍 过滤高质量记录...")
    valid_records = [record for record in full_data if is_valid_record(record)]
    
    print(f"✅ 高质量记录: {len(valid_records)} 条")
    print(f"📈 质量提升: {len(valid_records)/len(full_data)*100:.1f}% → 100%")
    
    # 创建输出目录
    output_dir = "../../src/PPatHF-main/PPatHF-main/data/quality_fixed"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存完整的高质量数据集
    full_output_path = os.path.join(output_dir, "custom_vim_neovim_quality_all.json")
    with open(full_output_path, 'w', encoding='utf-8') as f:
        json.dump(valid_records, f, indent=2, ensure_ascii=False)
    
    print(f"💾 高质量完整数据集已保存: {full_output_path}")
    
    # 基于字符数估算创建不同长度版本
    print("\n🎯 创建不同长度版本...")
    
    # 计算每条记录的字符数
    records_with_length = []
    for record in valid_records:
        total_chars = sum(len(str(record.get(field, ""))) for field in record.keys())
        records_with_length.append((record, total_chars))
    
    # 按字符数排序
    records_with_length.sort(key=lambda x: x[1])
    
    # 创建不同长度版本
    length_limits = {
        2048: 400,   # 约400条记录
        4096: 800,   # 约800条记录
        8192: 1200   # 约1200条记录
    }
    
    for token_limit, max_records in length_limits.items():
        # 选择字符数较少的记录
        selected_records = [record for record, _ in records_with_length[:max_records]]
        
        if len(selected_records) == 0:
            continue
        
        output_path = os.path.join(output_dir, f"custom_vim_neovim_quality_{token_limit}.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(selected_records, f, indent=2, ensure_ascii=False)
        
        # 计算文件大小
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        
        print(f"📁 {token_limit}版本: {len(selected_records)} 条记录 ({file_size:.1f}MB)")
    
    # 创建质量修复报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "original_dataset": {
            "path": full_data_path,
            "total_records": len(full_data),
            "valid_records": len(valid_records),
            "invalid_records": len(full_data) - len(valid_records),
            "quality_rate": len(valid_records) / len(full_data) * 100
        },
        "quality_fixed_datasets": {
            "output_directory": output_dir,
            "datasets_created": list(length_limits.keys()) + ["all"]
        },
        "improvements": {
            "removed_empty_records": len(full_data) - len(valid_records),
            "quality_improvement": f"{len(valid_records)/len(full_data)*100:.1f}% → 100%"
        }
    }
    
    report_path = os.path.join(output_dir, "quality_fix_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 质量修复报告已保存: {report_path}")
    
    # 使用建议
    print(f"\n🚀 使用建议:")
    print(f"1. 优先使用高质量完整数据集进行实验:")
    print(f"   📁 {full_output_path}")
    print(f"   ✅ {len(valid_records)} 条100%有效记录")
    
    print(f"\n2. 长度限制版本推荐:")
    for token_limit, max_records in length_limits.items():
        actual_records = min(max_records, len(valid_records))
        print(f"   📁 quality_{token_limit}.json: {actual_records} 条记录 (100%有效)")
    
    print(f"\n3. 原PPatHF工具使用:")
    print(f"   cd src/PPatHF-main/PPatHF-main")
    print(f"   python predict.py --data_path \"data/quality_fixed/custom_vim_neovim_quality_all.json\"")

if __name__ == "__main__":
    filter_and_create_quality_datasets() 