{"timestamp": "2025-01-12", "source_dataset": {"path": "../../reproduction_work/data/custom/enhanced_ppathf_adapted.json", "stats": {"total_records": 3103, "total_fields": 14, "empty_stats": {"func_before_source": 538, "func_before_target": 537, "func_after_target": 540, "func_after_source": 540}, "empty_percentages": {"func_before_source": 17.33805994199162, "func_before_target": 17.305833064776024, "func_after_target": 17.402513696422815, "func_after_source": 17.402513696422815}, "field_samples": {"func_before_source": ["/* Copyright (C) 2009 Red Hat, Inc.\n * Copyright (C) 2006 Rusty Russell IBM Corporation\n *\n * Author", "/******************************************************************************\n *\n * Driver for Opt", "/*\n * gw.c - CAN frame Gateway/Router/Bridge with netlink interface\n *\n * Copyright (c) 2017 Volkswa"], "func_before_target": ["/*\n * PCI glue for ISHTP provider device (ISH) driver\n *\n * Copyright (c) 2014-2016, Intel Corporati", "/*\n * linux/include/linux/sunrpc/svc.h\n *\n * RPC server declarations.\n *\n * Copyright (C) 1995, 1996", "/*\n *\tipddp.c: IP to Appletalk-IP Encapsulation driver for Linux\n *\t\t Appletalk-IP to IP Decapsulati"], "func_after_target": ["/*\n * PCI glue for ISHTP provider device (ISH) driver\n *\n * Copyright (c) 2014-2016, Intel Corporati", "/*\n * linux/include/linux/sunrpc/svc.h\n *\n * RPC server declarations.\n *\n * Copyright (C) 1995, 1996", "/*\n *\tipddp.c: IP to Appletalk-IP Encapsulation driver for Linux\n *\t\t Appletalk-IP to IP Decapsulati"], "original_cve_id": ["CVE-2018-1118", "CVE-2018-19985", "CVE-2019-3701"], "neovim_committer_date": ["2018-06-26T00:08:08Z", "2019-03-25T17:32:32Z", "2019-03-25T17:32:33Z"], "diff_source": ["@@ -2345,6 +2345,9 @@ struct vhost_msg_node *vhost_new_msg(struct vhost_virtqueue *vq, int type)\n \ts", "@@ -2807,6 +2807,12 @@ static int hso_get_config_data(struct usb_interface *interface)\n \t\treturn -EI", "@@ -416,13 +416,29 @@ static void can_can_gw_rcv(struct sk_buff *skb, void *data)\n \twhile (modidx < "], "func_after_source": ["/* Copyright (C) 2009 Red Hat, Inc.\n * Copyright (C) 2006 Rusty Russell IBM Corporation\n *\n * Author", "/******************************************************************************\n *\n * Driver for Opt", "/*\n * gw.c - CAN frame Gateway/Router/Bridge with netlink interface\n *\n * Copyright (c) 2017 Volkswa"], "original_unified_id": ["CVE-2018-1118_Linux Kernel_Linux Kernel_7aacb609", "CVE-2018-19985_Linux Kernel_Linux Kernel_96af9720", "CVE-2019-3701_Linux Kernel_Linux Kernel_2ce4a69b"], "commit_id_source": ["670ae9caaca467ea1bfd325cb2a5c98ba87f94ad", "5146f95df782b0ac61abde36567e718692725c89", "0aaa81377c5a01f686bcdb8c7a6929a7bf330c68"], "file_path_target": ["drivers/hid/intel-ish-hid/ipc/pci-ish.c", "include/linux/sunrpc/svc.h", "drivers/net/appletalk/ipddp.c"], "commit_id_target": ["a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99", "801f9d2fb42e450a67f83c18fd5d8450ad29224f", "0c4d7b52773b227211d311858f3c3b56f7c44874"], "file_path_source": ["drivers/vhost/vhost.c", "drivers/net/usb/hso.c", "net/can/gw.c"], "original_patch_type": ["same_repo"], "diff_target": ["@@ -202,8 +202,7 @@ static void ish_remove(struct pci_dev *pdev)\n \tkfree(ishtp_dev);\n }\n \n-#ifdef CO", "@@ -282,9 +282,12 @@ struct svc_rqst {\n \t\t\t\t\t\t * cache pages */\n \twait_queue_head_t\trq_wait;\t/* sync", "@@ -284,8 +284,12 @@ static int ipddp_ioctl(struct net_device *dev, struct ifreq *ifr, int cmd)\n    "]}, "fields": ["commit_id_source", "commit_id_target", "diff_source", "diff_target", "file_path_source", "file_path_target", "func_after_source", "func_after_target", "func_before_source", "func_before_target", "neovim_committer_date", "original_cve_id", "original_patch_type", "original_unified_id"]}}, "filtered_dataset": {"path": "../../src/PPatHF-main/PPatHF-main/data/backup_full_dataset_20250712_092137/custom_vim_neovim_test_all_processed.json", "stats": {"total_records": 3103, "total_fields": 14, "empty_stats": {"func_before_source": 538, "func_before_target": 537, "func_after_target": 540, "func_after_source": 540}, "empty_percentages": {"func_before_source": 17.33805994199162, "func_before_target": 17.305833064776024, "func_after_target": 17.402513696422815, "func_after_source": 17.402513696422815}, "field_samples": {"func_before_source": ["/* Copyright (C) 2009 Red Hat, Inc.\n * Copyright (C) 2006 Rusty Russell IBM Corporation\n *\n * Author", "/******************************************************************************\n *\n * Driver for Opt", "/*\n * gw.c - CAN frame Gateway/Router/Bridge with netlink interface\n *\n * Copyright (c) 2017 Volkswa"], "func_before_target": ["/*\n * PCI glue for ISHTP provider device (ISH) driver\n *\n * Copyright (c) 2014-2016, Intel Corporati", "/*\n * linux/include/linux/sunrpc/svc.h\n *\n * RPC server declarations.\n *\n * Copyright (C) 1995, 1996", "/*\n *\tipddp.c: IP to Appletalk-IP Encapsulation driver for Linux\n *\t\t Appletalk-IP to IP Decapsulati"], "func_after_target": ["/*\n * PCI glue for ISHTP provider device (ISH) driver\n *\n * Copyright (c) 2014-2016, Intel Corporati", "/*\n * linux/include/linux/sunrpc/svc.h\n *\n * RPC server declarations.\n *\n * Copyright (C) 1995, 1996", "/*\n *\tipddp.c: IP to Appletalk-IP Encapsulation driver for Linux\n *\t\t Appletalk-IP to IP Decapsulati"], "original_cve_id": ["CVE-2018-1118", "CVE-2018-19985", "CVE-2019-3701"], "neovim_committer_date": ["2018-06-26 00:08:08+0000", "2019-03-25 17:32:32+0000", "2019-03-25 17:32:33+0000"], "diff_source": ["@@ -2345,6 +2345,9 @@ struct vhost_msg_node *vhost_new_msg(struct vhost_virtqueue *vq, int type)\n \ts", "@@ -2807,6 +2807,12 @@ static int hso_get_config_data(struct usb_interface *interface)\n \t\treturn -EI", "@@ -416,13 +416,29 @@ static void can_can_gw_rcv(struct sk_buff *skb, void *data)\n \twhile (modidx < "], "func_after_source": ["/* Copyright (C) 2009 Red Hat, Inc.\n * Copyright (C) 2006 Rusty Russell IBM Corporation\n *\n * Author", "/******************************************************************************\n *\n * Driver for Opt", "/*\n * gw.c - CAN frame Gateway/Router/Bridge with netlink interface\n *\n * Copyright (c) 2017 Volkswa"], "original_unified_id": ["CVE-2018-1118_Linux Kernel_Linux Kernel_7aacb609", "CVE-2018-19985_Linux Kernel_Linux Kernel_96af9720", "CVE-2019-3701_Linux Kernel_Linux Kernel_2ce4a69b"], "commit_id_source": ["670ae9caaca467ea1bfd325cb2a5c98ba87f94ad", "5146f95df782b0ac61abde36567e718692725c89", "0aaa81377c5a01f686bcdb8c7a6929a7bf330c68"], "file_path_target": ["drivers/hid/intel-ish-hid/ipc/pci-ish.c", "include/linux/sunrpc/svc.h", "drivers/net/appletalk/ipddp.c"], "commit_id_target": ["a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99", "801f9d2fb42e450a67f83c18fd5d8450ad29224f", "0c4d7b52773b227211d311858f3c3b56f7c44874"], "file_path_source": ["drivers/vhost/vhost.c", "drivers/net/usb/hso.c", "net/can/gw.c"], "original_patch_type": ["same_repo"], "diff_target": ["@@ -202,8 +202,7 @@ static void ish_remove(struct pci_dev *pdev)\n \tkfree(ishtp_dev);\n }\n \n-#ifdef CO", "@@ -282,9 +282,12 @@ struct svc_rqst {\n \t\t\t\t\t\t * cache pages */\n \twait_queue_head_t\trq_wait;\t/* sync", "@@ -284,8 +284,12 @@ static int ipddp_ioctl(struct net_device *dev, struct ifreq *ifr, int cmd)\n    "]}, "fields": ["commit_id_source", "commit_id_target", "diff_source", "diff_target", "file_path_source", "file_path_target", "func_after_source", "func_after_target", "func_before_source", "func_before_target", "neovim_committer_date", "original_cve_id", "original_patch_type", "original_unified_id"]}}, "issues_found": [], "summary": {"total_issues": 0, "records_comparison": {"source": 3103, "filtered": 3103, "difference": 0}}}