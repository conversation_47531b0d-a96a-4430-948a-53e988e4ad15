#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自建数据集轻量级适配器 - 将enhanced_and_nvd_dataset.json适配为PPatHF工具格式

核心原则：充分利用原工具仓库的现有功能，仅进行数据格式适配

主要职责：
1. 读取自建数据集（enhanced_and_nvd_dataset.json）
2. 通过GitHub API获取缺失的代码内容和diff信息
3. 转换为原工具期望的标准格式
4. 保存为原工具可直接使用的JSON文件

技术特点：
- 轻量级实现，专注于数据格式对接
- 利用原工具的tree-sitter和reduction功能
- 智能GitHub API使用（token轮换、速率限制）
- 支持断点续传和错误恢复
- 详细的进度跟踪和日志记录

作者：AI助手
创建时间：2025年1月
版本：v2.0 (轻量级适配器版本)
"""

import asyncio
import aiohttp
import json
import logging
import re
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse


class GitHubAPIClient:
    """
    轻量级GitHub API客户端
    专门用于获取commit信息和文件内容
    
    功能：
    - Token轮换使用，避免速率限制
    - 错误处理和重试机制
    - 获取commit详细信息（包含diff）
    - 获取指定commit的文件内容
    """
    
    def __init__(self, tokens: List[str]):
        """
        初始化GitHub API客户端
        
        Args:
            tokens: GitHub Personal Access Tokens列表
        """
        self.tokens = tokens
        self.current_token_idx = 0
        self.session = None
        
        # 请求统计
        self.request_count = 0
        self.error_count = 0
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _get_current_token(self) -> str:
        """获取当前token"""
        return self.tokens[self.current_token_idx]
    
    def _rotate_token(self):
        """轮换到下一个token"""
        self.current_token_idx = (self.current_token_idx + 1) % len(self.tokens)
    
    async def _make_request(self, url: str, **kwargs) -> Any:
        """
        执行HTTP请求，包含错误处理和重试逻辑
        
        Args:
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            响应数据（JSON或文本）
        """
        headers = {
            "Authorization": f"Bearer {self._get_current_token()}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "PPatHF-DataAdapter/2.0"
        }
        
        max_retries = len(self.tokens)  # 最多重试token数量次
        
        for retry in range(max_retries):
            try:
                self.request_count += 1
                
                async with self.session.get(url, headers=headers, **kwargs) as resp:
                    # 检查速率限制
                    if resp.status == 403:
                        rate_limit_remaining = resp.headers.get("X-RateLimit-Remaining", "0")
                        if rate_limit_remaining == "0":
                            logging.warning(f"Token {self.current_token_idx} 达到速率限制，切换token")
                            self._rotate_token()
                            await asyncio.sleep(5)  # 短暂等待后重试
                            continue
                    
                    if resp.status == 404:
                        logging.warning(f"资源不存在: {url}")
                        return None
                    
                    if resp.status != 200:
                        self.error_count += 1
                        logging.error(f"HTTP {resp.status}: {url}")
                        raise aiohttp.ClientResponseError(
                            request_info=resp.request_info,
                            history=resp.history,
                            status=resp.status
                        )
                    
                    # 根据URL类型返回相应格式
                    if "raw.githubusercontent.com" in url:
                        return await resp.text()
                    else:
                        return await resp.json()
                        
            except Exception as e:
                self.error_count += 1
                logging.error(f"请求失败 {url}: {e}")
                
                if retry < max_retries - 1:
                    await asyncio.sleep(2 ** retry)  # 指数退避
                else:
                    return None
        
        return None
    
    async def get_commit_info(self, owner: str, repo: str, commit_hash: str) -> Optional[Dict]:
        """
        获取commit的详细信息
        
        Args:
            owner: 仓库所有者
            repo: 仓库名
            commit_hash: commit哈希
            
        Returns:
            commit详细信息，包含files、diff等
        """
        url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_hash}"
        return await self._make_request(url, timeout=30)
    
    async def get_file_content(self, owner: str, repo: str, commit_hash: str, file_path: str) -> Optional[str]:
        """
        获取指定commit的文件内容
        
        Args:
            owner: 仓库所有者
            repo: 仓库名
            commit_hash: commit哈希
            file_path: 文件路径
            
        Returns:
            文件内容字符串
        """
        url = f"https://raw.githubusercontent.com/{owner}/{repo}/{commit_hash}/{file_path}"
        return await self._make_request(url, timeout=30)

    # ======= 新增代码：通过 tag 获取 commit sha =======
    async def get_commit_by_tag(self, owner: str, repo: str, tag: str) -> Optional[str]:
        """根据 tag 名称获取对应的 commit sha。如果 tag 不存在或解析失败返回 None。"""
        # 1) 查询 tag 引用
        ref_url = f"https://api.github.com/repos/{owner}/{repo}/git/refs/tags/{tag}"
        ref_json = await self._make_request(ref_url, timeout=15)
        if not ref_json:
            return None

        # 2) 处理轻量/附注 tag 两种情况
        obj = ref_json.get("object", {})
        if obj.get("type") == "commit":
            return obj.get("sha")
        if obj.get("type") == "tag":
            tag_sha = obj.get("sha")
            tag_url = f"https://api.github.com/repos/{owner}/{repo}/git/tags/{tag_sha}"
            tag_obj = await self._make_request(tag_url, timeout=15)
            if tag_obj and tag_obj.get("object"):
                return tag_obj["object"].get("sha")
        return None


class CustomDataAdapter:
    """
    自建数据集轻量级适配器
    
    将enhanced_and_nvd_dataset.json适配为PPatHF工具期望的格式
    """
    
    def __init__(self, 
                 input_file: str = "../data/data/enhanced_data/enhanced_and_nvd_dataset.json",
                 output_file: str = "data/custom/enhanced_ppathf_adapted.json"):
        """
        初始化适配器
        
        Args:
            input_file: 输入的自建数据集文件路径
            output_file: 输出的PPatHF格式文件路径
        """
        self.input_file = Path(input_file)
        self.output_file = Path(output_file)
        
        # 确保输出目录存在
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # GitHub API tokens
        self.github_tokens = [
            "*********************************************************************************************",
            "*********************************************************************************************"
        ]
        
        # 统计信息
        self.stats = {
            'total_records': 0,
            'processed_records': 0,
            'successful_adaptations': 0,
            'failed_adaptations': 0,
            'skipped_records': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 设置日志
        self._setup_logging()
        
        self.logger.info("自建数据轻量级适配器初始化完成")
        self.logger.info(f"输入文件: {self.input_file}")
        self.logger.info(f"输出文件: {self.output_file}")
    
    def _setup_logging(self):
        """设置日志配置"""
        log_file = self.output_file.parent / "data_adaptation.log"
        
        # 创建logger
        self.logger = logging.getLogger('CustomDataAdapter')
        self.logger.setLevel(logging.INFO)
        
        # 防止重复添加handler
        if not self.logger.handlers:
            # 文件handler
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加handlers
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def parse_github_url(self, repo_url: str) -> Tuple[str, str]:
        """
        解析GitHub仓库URL，提取owner和repo名称
        
        Args:
            repo_url: GitHub仓库URL
            
        Returns:
            (owner, repo) 元组
        """
        # 清理URL
        clean_url = repo_url.rstrip('/').rstrip('.git')
        
        # 解析URL路径
        parsed = urlparse(clean_url)
        path_parts = parsed.path.strip('/').split('/')
        
        if len(path_parts) >= 2:
            return path_parts[0], path_parts[1]
        else:
            raise ValueError(f"无法解析GitHub URL: {repo_url}")
    
    def select_best_commit(self, version_info: Dict) -> Optional[str]:
        """
        从版本信息中选择最佳的commit
        
        Args:
            version_info: 版本信息字典，包含vulnerable和patched
            
        Returns:
            最佳的commit哈希，优先选择patched
        """
        # 优先选择patched commit，如果没有则选择vulnerable
        patched_commit = version_info.get('patched', {}).get('commit')
        vulnerable_commit = version_info.get('vulnerable', {}).get('commit')
        
        if patched_commit and patched_commit != "unknown":
            return patched_commit
        elif vulnerable_commit and vulnerable_commit != "unknown":
            return vulnerable_commit
        else:
            return None

    # ======= 新增代码：根据版本号尝试获取commit =======
    async def _commit_from_version(self, github_client: GitHubAPIClient, repo_url: str, version_info: Dict[str, Any]) -> Optional[str]:
        """若 version_info 中 commit 均缺失，尝试根据版本号字段推断 commit sha。"""
        version = version_info.get("patched", {}).get("version") or version_info.get("vulnerable", {}).get("version")
        if not version or version == "unknown":
            return None

        owner, repo = self.parse_github_url(repo_url)
        # 常见 tag 命名候选，如 4.17 -> v4.17, 4.17, v4.17.0, 4.17.0
        candidates = [version, f"v{version}", f"{version}.0", f"v{version}.0"]
        for tag in candidates:
            sha = await github_client.get_commit_by_tag(owner, repo, tag)
            if sha:
                self.logger.info(f"通过 tag {tag} 找到 commit {sha} ({owner}/{repo})")
                return sha
        self.logger.warning(f"根据版本号 {version} 未能找到对应 commit ({owner}/{repo})")
        return None
    
    async def fetch_commit_data(self, github_client: GitHubAPIClient, 
                               repo_url: str, commit_hash: str) -> Optional[Dict[str, Any]]:
        """
        获取单个commit的完整数据
        
        Args:
            github_client: GitHub API客户端
            repo_url: 仓库URL
            commit_hash: commit哈希
            
        Returns:
            包含文件路径、代码内容、diff等信息的字典
        """
        try:
            # 解析仓库信息
            owner, repo = self.parse_github_url(repo_url)
            
            # 获取commit信息
            commit_info = await github_client.get_commit_info(owner, repo, commit_hash)
            if not commit_info:
                return None
            
            # 获取修改的文件（选择第一个C文件）
            files = commit_info.get('files', [])
            c_files = [f for f in files if f['filename'].endswith(('.c', '.h'))]
            
            if not c_files:
                self.logger.warning(f"Commit {commit_hash} 没有C文件变更")
                return None
            
            # 选择第一个C文件
            target_file = c_files[0]
            file_path = target_file['filename']
            diff = target_file.get('patch', '')
            
            # 获取父commit（用于获取修改前的代码）
            parents = commit_info.get('parents', [])
            if not parents:
                self.logger.warning(f"Commit {commit_hash} 没有父commit")
                return None
            
            parent_sha = parents[0]['sha']
            
            # 获取修改前后的文件内容
            func_before, func_after = await asyncio.gather(
                github_client.get_file_content(owner, repo, parent_sha, file_path),
                github_client.get_file_content(owner, repo, commit_hash, file_path)
            )
            
            # 处理文件不存在的情况
            if func_before is None:
                func_before = ""  # 新文件
            if func_after is None:
                func_after = ""   # 删除文件
            
            return {
                'file_path': file_path,
                'func_before': func_before,
                'func_after': func_after,
                'diff': diff,
                'date': commit_info['commit']['committer']['date']
            }
            
        except Exception as e:
            self.logger.error(f"获取commit数据失败 {repo_url}@{commit_hash}: {e}")
            return None
    
    async def adapt_single_record(self, github_client: GitHubAPIClient, 
                                 record: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        适配单条数据记录为PPatHF格式
        
        Args:
            github_client: GitHub API客户端
            record: 输入数据记录
            
        Returns:
            适配后的PPatHF格式记录，失败时返回None
        """
        unified_id = record.get('unified_id', '')
        
        try:
            self.logger.info(f"开始适配记录: {unified_id}")
            
            # 提取基础信息
            source_repo = record['projects']['source']['repo']
            target_repo = record['projects']['target']['repo']
            
            # 选择最佳commit
            source_commit = self.select_best_commit(record['versions']['source'])
            target_commit = self.select_best_commit(record['versions']['target'])

            # 若commit缺失，尝试根据版本号获取
            if not source_commit:
                source_commit = await self._commit_from_version(github_client, source_repo, record['versions']['source'])
            if not target_commit:
                target_commit = await self._commit_from_version(github_client, target_repo, record['versions']['target'])

            if not source_commit or not target_commit:
                self.logger.warning(f"记录 {unified_id} 缺少必要的commit信息")
                return None
            
            # 获取源仓库和目标仓库的数据
            source_data, target_data = await asyncio.gather(
                self.fetch_commit_data(github_client, source_repo, source_commit),
                self.fetch_commit_data(github_client, target_repo, target_commit)
            )
            
            if not source_data or not target_data:
                self.logger.warning(f"记录 {unified_id} 无法获取完整的commit数据")
                return None
            
            # 构建PPatHF格式的输出
            ppathf_record = {
                # 源仓库信息
                "commit_id_source": source_commit,
                "file_path_source": source_data['file_path'],
                "func_before_source": source_data['func_before'],
                "func_after_source": source_data['func_after'],
                "diff_source": source_data['diff'],
                
                # 目标仓库信息
                "commit_id_target": target_commit,
                "file_path_target": target_data['file_path'],
                "func_before_target": target_data['func_before'],
                "func_after_target": target_data['func_after'],  # 这是预测目标
                "diff_target": target_data['diff'],
                
                # 其他必要字段
                "neovim_committer_date": target_data['date'],  # 用于原工具的时间过滤
                
                # 保留原始信息（可选，用于调试）
                "original_unified_id": unified_id,
                "original_cve_id": record.get('cve_id', 'unknown'),
                "original_patch_type": record.get('patch_type', 'unknown')
            }
            
            self.stats['successful_adaptations'] += 1
            self.logger.info(f"记录 {unified_id} 适配成功")
            return ppathf_record
            
        except Exception as e:
            self.stats['failed_adaptations'] += 1
            self.logger.error(f"记录 {unified_id} 适配失败: {e}")
            return None
    
    def load_processed_records(self) -> set:
        """加载已处理的记录ID（用于断点续传）"""
        if not self.output_file.exists():
            return set()
        
        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_ids = set()
            for record in data:
                original_id = record.get('original_unified_id')
                if original_id:
                    processed_ids.add(original_id)
            
            self.logger.info(f"从输出文件中加载了 {len(processed_ids)} 个已处理记录")
            return processed_ids
            
        except Exception as e:
            self.logger.error(f"加载已处理记录失败: {e}")
            return set()
    
    async def save_progress(self, adapted_records: List[Dict[str, Any]]):
        """保存适配进度"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(adapted_records, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"已保存 {len(adapted_records)} 条适配记录")
            
        except Exception as e:
            self.logger.error(f"保存进度失败: {e}")
    
    async def adapt_dataset(self, max_records: int = None):
        """
        适配整个数据集
        
        Args:
            max_records: 最大处理记录数（用于测试）
        """
        self.stats['start_time'] = datetime.now()
        self.logger.info("开始数据适配流程")
        
        # 加载输入数据
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                input_data = json.load(f)
        except Exception as e:
            self.logger.error(f"加载输入文件失败: {e}")
            return
        
        self.stats['total_records'] = len(input_data)
        if max_records:
            input_data = input_data[:max_records]
            self.stats['total_records'] = len(input_data)
        
        self.logger.info(f"准备处理 {self.stats['total_records']} 条记录")
        
        # 断点续传：加载已处理的记录
        processed_ids = self.load_processed_records()
        
        # 初始化GitHub API客户端
        async with GitHubAPIClient(self.github_tokens) as github_client:
            adapted_records = []
            
            # 如果存在输出文件，先加载已有记录
            if self.output_file.exists():
                try:
                    with open(self.output_file, 'r', encoding='utf-8') as f:
                        adapted_records = json.load(f)
                    self.logger.info(f"加载了 {len(adapted_records)} 条已适配记录")
                except Exception as e:
                    self.logger.error(f"加载已有输出文件失败: {e}")
                    adapted_records = []
            
            # 处理每条记录
            for i, record in enumerate(input_data):
                unified_id = record.get('unified_id', f'record_{i}')
                
                # 检查是否已处理
                if unified_id in processed_ids:
                    self.stats['skipped_records'] += 1
                    continue
                
                self.stats['processed_records'] += 1
                
                # 适配单条记录
                adapted_record = await self.adapt_single_record(github_client, record)
                
                if adapted_record:
                    adapted_records.append(adapted_record)
                
                # 每处理50条记录保存一次进度
                if len(adapted_records) % 50 == 0:
                    await self.save_progress(adapted_records)
                
                # 控制请求频率，避免过快请求
                await asyncio.sleep(0.5)
                
                # 打印进度
                if self.stats['processed_records'] % 10 == 0:
                    progress = (self.stats['processed_records'] / self.stats['total_records']) * 100
                    self.logger.info(f"适配进度: {self.stats['processed_records']}/{self.stats['total_records']} ({progress:.2f}%)")
                    self.logger.info(f"API统计: 请求{github_client.request_count}次, 错误{github_client.error_count}次")
            
            # 保存最终结果
            await self.save_progress(adapted_records)
        
        self.stats['end_time'] = datetime.now()
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终的适配报告"""
        duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        
        report = {
            "适配统计": {
                "总记录数": self.stats['total_records'],
                "已处理记录数": self.stats['processed_records'],
                "成功适配数": self.stats['successful_adaptations'],
                "失败适配数": self.stats['failed_adaptations'],
                "跳过记录数": self.stats['skipped_records'],
                "成功率": f"{(self.stats['successful_adaptations'] / max(1, self.stats['processed_records'])) * 100:.2f}%"
            },
            "时间统计": {
                "开始时间": self.stats['start_time'].isoformat(),
                "结束时间": self.stats['end_time'].isoformat(),
                "总耗时": f"{duration:.2f}秒",
                "平均每条记录耗时": f"{duration / max(1, self.stats['processed_records']):.2f}秒"
            }
        }
        
        # 保存报告
        report_file = self.output_file.parent / "adaptation_report.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
        
        # 打印摘要
        self.logger.info("=" * 80)
        self.logger.info("数据适配完成！")
        self.logger.info(f"处理记录: {self.stats['processed_records']}/{self.stats['total_records']}")
        self.logger.info(f"成功适配: {self.stats['successful_adaptations']}")
        self.logger.info(f"失败适配: {self.stats['failed_adaptations']}")
        self.logger.info(f"总耗时: {duration:.2f}秒")
        self.logger.info(f"输出文件: {self.output_file}")
        self.logger.info(f"详细报告: {report_file}")
        self.logger.info("=" * 80)
        
        # 下一步提示
        self.logger.info("🎯 下一步可以使用原工具处理适配后的数据：")
        self.logger.info("1. 将数据复制到原工具数据目录")
        self.logger.info("2. 使用原工具的utils.py进行数据预处理")
        self.logger.info("3. 使用原工具的predict.py进行推理")
        self.logger.info("4. 使用原工具的test.py进行评估")


async def main():
    """主程序入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="自建数据集轻量级适配器")
    parser.add_argument("--input", "-i", 
                       default="../data/data/enhanced_data/enhanced_and_nvd_dataset.json",
                       help="输入的自建数据集文件路径")
    parser.add_argument("--output", "-o", 
                       default="data/custom/enhanced_ppathf_adapted.json",
                       help="输出的PPatHF格式文件路径")
    parser.add_argument("--max-records", "-m", type=int,
                       help="最大处理记录数（用于测试）")
    
    args = parser.parse_args()
    
    # 创建适配器
    adapter = CustomDataAdapter(
        input_file=args.input,
        output_file=args.output
    )
    
    # 执行适配
    await adapter.adapt_dataset(max_records=args.max_records)


if __name__ == "__main__":
    asyncio.run(main()) 