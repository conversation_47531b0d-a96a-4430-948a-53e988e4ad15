#!/usr/bin/env python3
"""
PPatHF Evaluation脚本 - 使用预过滤的短样本数据集
解决Django代码生成问题的核心方案：回归原始设计
"""

import os
import sys
import json
import pandas as pd
import time
from pathlib import Path

# 添加原项目路径到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent / "PPatHF-main"
sys.path.insert(0, str(project_root))

# 从原项目导入配置和函数
from config import API_BASE_URL, API_MODEL_NAME, USE_API_SERVICE, DATA_PATH, OUTPUT_PATH, EXTRACTED_METRIC_PATH, LLM_PATH, LLM_NAME_PATH_MAPPING
from test import cal_metrics, analyze_test_results, align_test_metrics

class PPatHFShortSampleEvaluator:
    """PPatHF评估器 - 使用预过滤的短样本数据集"""
    
    def __init__(self, max_length="8192"):
        """
        初始化评估器
        Args:
            max_length: 选择数据集大小 ("2048", "4096", "8192")
        """
        self.max_length = max_length
        self.data_file = f"vim_neovim_test_{max_length}.json"
        
        # 数据路径
        data_root = Path("/home/<USER>/nas-files/Re-paper/ppathf/data/data")
        self.data_path = str(data_root / self.data_file)
        self.model_name = "starcoder"
        
        # 输出路径
        self.output_dir = Path(OUTPUT_PATH)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建专门的输出文件名
        self.result_file = f"out_{self.model_name}_short_{max_length}_result.json"
        self.result_path = str(self.output_dir / self.result_file)
        
        print(f"✅ 短样本评估器初始化完成")
        print(f"📁 数据文件: {self.data_path}")
        print(f"📤 输出路径: {self.result_path}")
        print(f"🔧 API服务: {API_BASE_URL}")
        
    def check_data_availability(self):
        """检查数据文件是否存在"""
        if not os.path.exists(self.data_path):
            print(f"❌ 数据文件不存在: {self.data_path}")
            return False
            
        with open(self.data_path, 'r') as f:
            data = json.load(f)
            
        print(f"✅ 预过滤数据文件检查通过")
        print(f"📊 数据总数: {len(data)} 个短样本")
        print(f"🔑 数据字段: {list(data[0].keys()) if data else '空数据'}")
        
        # 检查样本长度分布
        if data:
            prompt_lengths = []
            for sample in data[:5]:  # 检查前5个样本
                prompt = self.create_patch_prompt(sample)
                prompt_lengths.append(len(prompt))
            
            print(f"📏 样本prompt长度分布 (前5个): {prompt_lengths}")
            print(f"📏 平均长度: {sum(prompt_lengths)/len(prompt_lengths):.0f} 字符")
            
        return True
        
    def create_patch_prompt(self, sample):
        """创建patch porting的prompt - 使用原始仓库的正确格式"""
        prompt = f"""Below is a patch (including function before and function after) from vim, paired with a corresponding function before from neovim. Adapt the patch from vim to neovim by generating the function after based on the given function before.

### Function Before (vim):
{sample['func_before_source']}

### Function After (vim):
{sample['func_after_source']}

### Function Before (neovim):
{sample['func_before_target']}

### Function After (neovim):
"""
        return prompt
    
    def call_api_simple(self, prompt, max_tokens=2048, timeout=120):
        """调用API进行生成 - 移除截断逻辑，直接处理短样本"""
        import requests
        import time
        
        # 不进行截断！相信预过滤数据集的质量
        print(f"📏 Prompt长度: {len(prompt)} 字符 (无需截断)")
        
        for attempt in range(3):  # 减少重试次数，因为短样本成功率高
            try:
                print(f"  🔄 尝试{attempt+1}/3...")
                
                payload = {
                    "model": "starcoder",
                    "prompt": prompt,
                    "max_tokens": max_tokens,
                    "temperature": 0.1,
                    "stop": ["### Function", "\n\n\n", "```"]
                }
                
                response = requests.post(
                    f"{API_BASE_URL}/v1/completions",
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    text = result['choices'][0]['text'] if result['choices'] else ""
                    print(f"  ✅ API调用成功")
                    return True, text
                else:
                    print(f"  ⚠️ 尝试{attempt+1}: HTTP {response.status_code}")
                    time.sleep(1)
                    
            except requests.exceptions.Timeout:
                print(f"  ⚠️ 尝试{attempt+1}: 超时 (>{timeout}秒)")
                time.sleep(1)
            except requests.exceptions.ConnectionError as e:
                print(f"  ⚠️ 尝试{attempt+1}: 连接错误 - {e}")
                time.sleep(2)
            except Exception as e:
                print(f"  ⚠️ 尝试{attempt+1}: 未知错误 - {e}")
                time.sleep(1)
        
        print(f"  ❌ 所有重试失败")
        return False, ""
    
    def run_prediction(self):
        """运行LLM预测生成 - 针对短样本优化"""
        print(f"\n🚀 开始运行短样本预测生成...")
        print(f"🎯 使用预过滤数据集，无需截断处理")
        
        # 加载数据
        try:
            with open(self.data_path, 'r') as f:
                data = json.load(f)
            print(f"📊 加载短样本数据: {len(data)} 个样本")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
        
        # 详细结果列表
        detailed_results = []
        simple_results = []
        successful_count = 0
        django_count = 0
        c_code_count = 0
        
        # 创建详细结果文件路径
        detailed_result_path = self.result_path.replace("_result.json", "_detailed_result.json")
        
        print(f"🔄 开始API调用生成 (短样本优化版)...")
        print(f"💾 详细结果将保存到: {detailed_result_path}")
        print(f"💾 简单结果将保存到: {self.result_path}")
        
        for i, sample in enumerate(data):
            print(f"\n📝 处理样本 {i+1}/{len(data)}")
            print(f"🗂️ 文件: {sample['file_path_target']}")
            print(f"🔧 Commit: {sample['commit_id_target'][:8]}...")
            
            try:
                # 创建prompt（不截断）
                prompt = self.create_patch_prompt(sample)
                prompt_length = len(prompt)
                
                # 调用API
                start_time = time.time()
                success, generated_text = self.call_api_simple(prompt)
                end_time = time.time()
                generation_time = end_time - start_time
                
                if success:
                    print(f"✅ 生成成功 ({generation_time:.2f}s)")
                    print(f"📝 生成长度: {len(generated_text)} 字符")
                    successful_count += 1
                    
                    # 分析生成内容类型
                    is_django = 'django' in generated_text.lower() or 'migration' in generated_text.lower()
                    is_c_code = any(kw in generated_text for kw in ['static', 'void', 'int ', 'char ', '#include'])
                    
                    if is_django:
                        django_count += 1
                        print(f"⚠️ 检测到Django代码")
                    elif is_c_code:
                        c_code_count += 1
                        print(f"✅ 检测到C代码")
                    
                    # 显示生成预览
                    preview = generated_text[:100].replace('\n', '\\n')
                    print(f"🔍 生成预览: {preview}...")
                else:
                    print(f"❌ 生成失败 ({generation_time:.2f}s)")
                    generated_text = ""
                
                # 创建详细结果记录
                detailed_result = {
                    "sample_index": i,
                    "commit_id_source": sample['commit_id_source'],
                    "commit_id_target": sample['commit_id_target'],
                    "file_path_source": sample['file_path_source'],
                    "file_path_target": sample['file_path_target'],
                    "func_before_source": sample['func_before_source'],
                    "func_after_source": sample['func_after_source'],
                    "func_before_target": sample['func_before_target'],
                    "func_after_target": sample['func_after_target'],
                    "generated_func_after_target": generated_text,
                    "generation_success": success,
                    "generation_time": generation_time,
                    "prompt_length": prompt_length,
                    "generated_length": len(generated_text),
                    "is_django_code": 'django' in generated_text.lower(),
                    "is_c_code": any(kw in generated_text for kw in ['static', 'void', 'int ', 'char ']),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                detailed_results.append(detailed_result)
                simple_results.append(generated_text)
                
                # 实时保存
                try:
                    with open(detailed_result_path, 'w', encoding='utf-8') as f:
                        json.dump(detailed_results, f, indent=2, ensure_ascii=False)
                    
                    with open(self.result_path, 'w', encoding='utf-8') as f:
                        json.dump(simple_results, f, indent=2, ensure_ascii=False)
                    
                    print(f"💾 实时保存完成 ({i+1}/{len(data)})")
                except Exception as save_error:
                    print(f"⚠️ 实时保存失败: {save_error}")
                
                # 进度报告
                if (i + 1) % 5 == 0:  # 短样本更频繁报告
                    print(f"\n📊 进度报告: {i+1}/{len(data)} ({(i+1)/len(data)*100:.1f}%)")
                    print(f"✅ 成功: {successful_count}/{i+1} ({successful_count/(i+1)*100:.1f}%)")
                    print(f"🐍 Django代码: {django_count} ({django_count/(i+1)*100:.1f}%)")
                    print(f"🔧 C代码: {c_code_count} ({c_code_count/(i+1)*100:.1f}%)")
                
                # 短延迟
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ 处理样本 {i+1} 时出错: {e}")
                # 继续处理下一个样本
        
        # 最终总结
        print(f"\n✅ 短样本预测生成完成")
        print(f"📊 详细结果文件: {detailed_result_path}")
        print(f"📊 兼容格式文件: {self.result_path}")
        print(f"📈 总样本数: {len(detailed_results)}")
        print(f"🎯 成功生成: {successful_count}/{len(detailed_results)} ({successful_count/len(detailed_results)*100:.1f}%)")
        print(f"🐍 Django代码: {django_count} ({django_count/successful_count*100:.1f}% of successful)")
        print(f"🔧 C代码: {c_code_count} ({c_code_count/successful_count*100:.1f}% of successful)")
        
        return True
    
    def calculate_metrics(self):
        """计算评估指标"""
        print(f"\n📊 开始计算评估指标...")
        
        try:
            cal_metrics(
                data_path=self.data_path,
                output_path=self.result_path,
                do_recover=False,
                reference_key="func_after_target",
                reference_before_key="func_before_target", 
                save_sample_wise_results=True
            )
            
            print(f"✅ 基础指标计算完成")
            
            # 分析测试结果
            processed_path = self.result_path.replace("result.json", "result_processed.json")
            analyze_test_results(
                result_path=processed_path,
                save_analyzed_results=True
            )
            
            print(f"✅ 详细分析完成")
            print(f"📋 处理后结果: {processed_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 指标计算失败: {e}")
            return False
    
    def run_full_evaluation(self):
        """运行完整的短样本evaluation流程"""
        print("🎯 开始PPatHF短样本评估流程")
        print("🎯 目标：解决Django代码生成问题")
        print("=" * 50)
        
        # 第1步：检查数据
        if not self.check_data_availability():
            return False
            
        # 第2步：运行预测
        if not self.run_prediction():
            return False
            
        # 第3步：计算指标
        if not self.calculate_metrics():
            return False
            
        print("\n🎉 短样本评估流程完成!")
        print("=" * 50)
        return True

def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description='PPatHF短样本评估')
    parser.add_argument('--max_length', default='8192', choices=['2048', '4096', '8192'], 
                       help='选择数据集大小')
    args = parser.parse_args()
    
    evaluator = PPatHFShortSampleEvaluator(max_length=args.max_length)
    success = evaluator.run_full_evaluation()
    
    if success:
        print("\n✅ 短样本评估完成!")
        print("🎯 Django代码生成问题应该得到显著改善")
    else:
        print("\n❌ 评估过程中出现错误")
        sys.exit(1)

if __name__ == "__main__":
    main() 