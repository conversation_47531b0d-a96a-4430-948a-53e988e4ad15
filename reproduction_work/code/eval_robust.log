nohup: 忽略输入
✅ 初始化完成
📁 数据文件: /home/<USER>/nas-files/Re-paper/ppathf/data/data/vim_neovim_test_all.json
📤 输出路径: outputs/out_starcoder_all_data_result.json
🔧 API服务: http://10.150.10.76:5002
🎯 开始PPatHF完整评估流程
==================================================
✅ 数据文件检查通过
📊 数据总数: 310
🔑 数据字段: ['commit_id_source', 'file_path_source', 'func_before_source', 'func_after_source', 'diff_source', 'commit_id_target', 'file_path_target', 'func_before_target', 'func_after_target', 'diff_target', 'neovim_committer_date']

🚀 开始运行预测生成...
🔧 使用直接API调用模式，绕过predict.py
📊 加载数据: 310 个样本
🔄 开始API调用生成...
💾 详细结果将保存到: outputs/out_starcoder_all_data_detailed_result.json
💾 简单结果将保存到: outputs/out_starcoder_all_data_result.json

📝 处理样本 1/310
🗂️ 文件: src/nvim/menu.c
🔧 Commit: 236947ab...
📏 Prompt长度: 7624 字符
  ⚠️ Prompt过长(7624字符)，尝试截断...
  📏 截断后长度: 3973字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.61s)
📝 生成长度: 5737 字符
🔍 生成预览: t_name,\n            last_name=last_name\n        )\n\n        return user\n\n    def create_superuser(sel...
💾 实时保存完成 (1/310)

📝 处理样本 2/310
🗂️ 文件: src/nvim/popupmnu.c
🔧 Commit: 5551a29d...
📏 Prompt长度: 6602 字符
  ⚠️ Prompt过长(6602字符)，尝试截断...
  📏 截断后长度: 4064字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (119.93s)
📝 生成长度: 2372 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (2/310)

📝 处理样本 3/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 300490f2...
📏 Prompt长度: 1092 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.92s)
📝 生成长度: 1349 字符
🔍 生成预览: static int help_compare(const void *s1, const void *s2)\n{\n  char *p1;\n  char *p2;\n  int cmp;\n  p1 = ...
💾 实时保存完成 (3/310)

📝 处理样本 4/310
🗂️ 文件: src/nvim/ex_getln.c
🔧 Commit: 72775437...
📏 Prompt长度: 2300 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.43s)
📝 生成长度: 1371 字符
🔍 生成预览: static int calc_hist_idx(int histype, int num)\n{\n  int i;\n  histentry_T *hist;\n  int wrapped = FALSE...
💾 实时保存完成 (4/310)

📝 处理样本 5/310
🗂️ 文件: src/nvim/getchar.c
🔧 Commit: 312f1b65...
📏 Prompt长度: 28305 字符
  ⚠️ Prompt过长(28305字符)，尝试截断...
  📏 截断后长度: 10932字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (14.94s)
💾 实时保存完成 (5/310)

📝 处理样本 6/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: 0313aba7...
📏 Prompt长度: 4865 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (118.08s)
📝 生成长度: 6982 字符
🔍 生成预览: self._data = data\n        self._index = index\n\n    def __repr__(self):\n        return f"{type(self)....
💾 实时保存完成 (6/310)

📝 处理样本 7/310
🗂️ 文件: src/nvim/spellfile.c
🔧 Commit: ed64c8f6...
📏 Prompt长度: 13782 字符
  ⚠️ Prompt过长(13782字符)，尝试截断...
  📏 截断后长度: 6181字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.04s)
💾 实时保存完成 (7/310)

📝 处理样本 8/310
🗂️ 文件: src/nvim/spell.c
🔧 Commit: 1ff86aa6...
📏 Prompt长度: 18059 字符
  ⚠️ Prompt过长(18059字符)，尝试截断...
  📏 截断后长度: 7456字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.06s)
💾 实时保存完成 (8/310)

📝 处理样本 9/310
🗂️ 文件: src/nvim/spell.c
🔧 Commit: a4215a0a...
📏 Prompt长度: 11620 字符
  ⚠️ Prompt过长(11620字符)，尝试截断...
  📏 截断后长度: 5340字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (118.62s)
📝 生成长度: 1706 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (9/310)

📝 处理样本 10/310
🗂️ 文件: src/nvim/spell.c
🔧 Commit: d07a39c5...
📏 Prompt长度: 10867 字符
  ⚠️ Prompt过长(10867字符)，尝试截断...
  📏 截断后长度: 5195字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.62s)
📝 生成长度: 567 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=30)),\n                ('descript...
💾 实时保存完成 (10/310)

📊 进度报告: 10/310 (3.2%)
✅ 成功: 7/10 (70.0%)

📝 处理样本 11/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: 04ba3777...
📏 Prompt长度: 17235 字符
  ⚠️ Prompt过长(17235字符)，尝试截断...
  📏 截断后长度: 7121字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (118.56s)
📝 生成长度: 854 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (11/310)

📝 处理样本 12/310
🗂️ 文件: src/nvim/indent.c
🔧 Commit: d9e5737f...
📏 Prompt长度: 10803 字符
  ⚠️ Prompt过长(10803字符)，尝试截断...
  📏 截断后长度: 5259字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (120.02s)
📝 生成长度: 794 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (12/310)

📝 处理样本 13/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: 5fcf701b...
📏 Prompt长度: 47066 字符
  ⚠️ Prompt过长(47066字符)，尝试截断...
  📏 截断后长度: 16525字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.08s)
💾 实时保存完成 (13/310)

📝 处理样本 14/310
🗂️ 文件: src/nvim/normal.c
🔧 Commit: f42657cb...
📏 Prompt长度: 27450 字符
  ⚠️ Prompt过长(27450字符)，尝试截断...
  📏 截断后长度: 10334字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (15.47s)
💾 实时保存完成 (14/310)

📝 处理样本 15/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: 4ffe5d01...
📏 Prompt长度: 1868 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.55s)
📝 生成长度: 1587 字符
🔍 生成预览: static void append_command(char *cmd)\n{\n  size_t len = STRLEN(IObuff);\n  char *s = cmd;\n  char *d;\n ...
💾 实时保存完成 (15/310)

📝 处理样本 16/310
🗂️ 文件: src/nvim/ex_getln.c
🔧 Commit: d4db87f4...
📏 Prompt长度: 15351 字符
  ⚠️ Prompt过长(15351字符)，尝试截断...
  📏 截断后长度: 6286字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.09s)
📝 生成长度: 411 字符
🔍 生成预览: t_id: str,\n        *,\n        name: Optional[str] = None,\n        description: Optional[str] = None,...
💾 实时保存完成 (16/310)

📝 处理样本 17/310
🗂️ 文件: src/nvim/memline.c
🔧 Commit: 4bb01262...
📏 Prompt长度: 4734 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.82s)
📝 生成长度: 354 字符
🔍 生成预览: e_path, 'w') as f:\n            json.dump(self._config, f, indent=2)\n\n    def _load_config(self):\n   ...
💾 实时保存完成 (17/310)

📝 处理样本 18/310
🗂️ 文件: src/nvim/memline.c
🔧 Commit: 8ce60ddd...
📏 Prompt长度: 4868 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.81s)
📝 生成长度: 90 字符
🔍 生成预览: nfig.get('port', 8080)\n\n    app.run(host=host, port=port)from flask import Flask\nimport os...
💾 实时保存完成 (18/310)

📝 处理样本 19/310
🗂️ 文件: src/nvim/ex_cmds2.c
🔧 Commit: a890c019...
📏 Prompt长度: 16091 字符
  ⚠️ Prompt过长(16091字符)，尝试截断...
  📏 截断后长度: 8465字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.06s)
💾 实时保存完成 (19/310)

📝 处理样本 20/310
🗂️ 文件: src/nvim/ex_cmds2.c
🔧 Commit: 159a0b65...
📏 Prompt长度: 17114 字符
  ⚠️ Prompt过长(17114字符)，尝试截断...
  📏 截断后长度: 9633字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.07s)
💾 实时保存完成 (20/310)

📊 进度报告: 20/310 (6.5%)
✅ 成功: 13/20 (65.0%)

📝 处理样本 21/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 1abdb322...
📏 Prompt长度: 60986 字符
  ⚠️ Prompt过长(60986字符)，尝试截断...
  📏 截断后长度: 19762字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.90s)
💾 实时保存完成 (21/310)

📝 处理样本 22/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: 77854106...
📏 Prompt长度: 5537 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.16s)
📝 生成长度: 700 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (22/310)

📝 处理样本 23/310
🗂️ 文件: src/nvim/window.c
🔧 Commit: 08235b6f...
📏 Prompt长度: 26511 字符
  ⚠️ Prompt过长(26511字符)，尝试截断...
  📏 截断后长度: 9636字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (118.97s)
📝 生成长度: 8192 字符
🔍 生成预览: self._check_input_shape(inputs)\n\n        # get input shape\n        batch_size, num_channels, height,...
💾 实时保存完成 (23/310)

📝 处理样本 24/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 0cf5cd1a...
📏 Prompt长度: 6399 字符
  ⚠️ Prompt过长(6399字符)，尝试截断...
  📏 截断后长度: 3474字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (119.32s)
📝 生成长度: 737 字符
🔍 生成预览: t_id=self._client_id, client_secret=self._client_secret)\n\n        self._session = OAuth2Session(clie...
💾 实时保存完成 (24/310)

📝 处理样本 25/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: 3ea45a2c...
📏 Prompt长度: 13753 字符
  ⚠️ Prompt过长(13753字符)，尝试截断...
  📏 截断后长度: 5561字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.81s)
📝 生成长度: 1116 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (25/310)

📝 处理样本 26/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: 8921035f...
📏 Prompt长度: 1572 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (35.16s)
📝 生成长度: 1130 字符
🔍 生成预览: static void set_option_from_tv(const char *varname, typval_T *varp)\n{\n  bool error = false;\n  char n...
💾 实时保存完成 (26/310)

📝 处理样本 27/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: 963ea726...
📏 Prompt长度: 1804 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.10s)
📝 生成长度: 1141 字符
🔍 生成预览: static void set_option_from_tv(const char *varname, typval_T *varp)\n{\n  long numval = 0;\n  const cha...
💾 实时保存完成 (27/310)

📝 处理样本 28/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: d8df9afa...
📏 Prompt长度: 15822 字符
  ⚠️ Prompt过长(15822字符)，尝试截断...
  📏 截断后长度: 6161字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 超时 (>120秒)
  🔄 尝试2/5...
  ✅ API调用成功
✅ 生成成功 (236.08s)
📝 生成长度: 747 字符
🔍 生成预览: t_id,\n            'project_id': self._project_id\n        }\n\n    def _create_resource(self, propertie...
💾 实时保存完成 (28/310)

📝 处理样本 29/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: ad57610a...
📏 Prompt长度: 5811 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.26s)
📝 生成长度: 9996 字符
🔍 生成预览: return self._data\n\n    @property\n    def data_size(self):\n        """\n        :return: The size of t...
💾 实时保存完成 (29/310)

📝 处理样本 30/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 611b4336...
📏 Prompt长度: 4455 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.93s)
📝 生成长度: 2226 字符
🔍 生成预览: void do_check_cursorbind(void)\n{\n  linenr_T line    = curwin->w_cursor.lnum;\n  colnr_T col      = cu...
💾 实时保存完成 (30/310)

📊 进度报告: 30/310 (9.7%)
✅ 成功: 22/30 (73.3%)

📝 处理样本 31/310
🗂️ 文件: src/nvim/diff.c
🔧 Commit: 771564b0...
📏 Prompt长度: 13332 字符
  ⚠️ Prompt过长(13332字符)，尝试截断...
  📏 截断后长度: 5296字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.13s)
📝 生成长度: 379 字符
🔍 生成预览: t_name='test',\n            first_name='test',\n            email='<EMAIL>',\n            password='<PA...
💾 实时保存完成 (31/310)

📝 处理样本 32/310
🗂️ 文件: src/nvim/popupmnu.c
🔧 Commit: ee8606d3...
📏 Prompt长度: 6801 字符
  ⚠️ Prompt过长(6801字符)，尝试截断...
  📏 截断后长度: 3977字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.95s)
📝 生成长度: 1441 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (32/310)

📝 处理样本 33/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: bbad7371...
📏 Prompt长度: 13913 字符
  ⚠️ Prompt过长(13913字符)，尝试截断...
  📏 截断后长度: 5521字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (35.95s)
📝 生成长度: 713 字符
🔍 生成预览: return self._get_value('name')\n\n    @property\n    def description(self):\n        """Description of t...
💾 实时保存完成 (33/310)

📝 处理样本 34/310
🗂️ 文件: src/nvim/window.c
🔧 Commit: 394d6549...
📏 Prompt长度: 1524 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (119.17s)
📝 生成长度: 1170 字符
🔍 生成预览: void reset_lnums(void)\n{\n  FOR_ALL_TAB_WINDOWS(tp, wp) {\n    if (wp->w_buffer == curbuf) {\n      if ...
💾 实时保存完成 (34/310)

📝 处理样本 35/310
🗂️ 文件: src/nvim/window.c
🔧 Commit: 0134a2cb...
📏 Prompt长度: 2439 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.64s)
📝 生成长度: 1501 字符
🔍 生成预览: static void check_lnums_both(bool do_curwin, bool nested)\n{\n  FOR_ALL_TAB_WINDOWS(tp, wp) {\n    if (...
💾 实时保存完成 (35/310)

📝 处理样本 36/310
🗂️ 文件: src/nvim/quickfix.c
🔧 Commit: 9cb8b5f8...
📏 Prompt长度: 6176 字符
  ⚠️ Prompt过长(6176字符)，尝试截断...
  📏 截断后长度: 4686字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.41s)
📝 生成长度: 728 字符
🔍 生成预览: t_name='ID')),\n                ('name', models.CharField(max_length=50)),\n            ],\n        ),\n...
💾 实时保存完成 (36/310)

📝 处理样本 37/310
🗂️ 文件: src/nvim/spell.c
🔧 Commit: 9511faa8...
📏 Prompt长度: 16751 字符
  ⚠️ Prompt过长(16751字符)，尝试截断...
  📏 截断后长度: 7212字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (47.97s)
📝 生成长度: 3372 字符
🔍 生成预览: self._check_type(value, str)\n        self._name = value\n\n    @property\n    def description(self):\n  ...
💾 实时保存完成 (37/310)

📝 处理样本 38/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: 29d5ca7d...
📏 Prompt长度: 7501 字符
  ⚠️ Prompt过长(7501字符)，尝试截断...
  📏 截断后长度: 4637字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.86s)
📝 生成长度: 959 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=30)),\n            ],\n        ),...
💾 实时保存完成 (38/310)

📝 处理样本 39/310
🗂️ 文件: src/nvim/mapping.c
🔧 Commit: 7d45f1a5...
📏 Prompt长度: 6809 字符
  ⚠️ Prompt过长(6809字符)，尝试截断...
  📏 截断后长度: 4490字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.51s)
📝 生成长度: 900 字符
🔍 生成预览: t_name = 'test'\n        self._test_user = UserFactory(username=self._test_name)\n\n    def test_create...
💾 实时保存完成 (39/310)

📝 处理样本 40/310
🗂️ 文件: src/nvim/mapping.c
🔧 Commit: 5c72640b...
📏 Prompt长度: 6431 字符
  ⚠️ Prompt过长(6431字符)，尝试截断...
  📏 截断后长度: 4298字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.02s)
📝 生成长度: 81 字符
🔍 生成预览: t_name,\n            'last_name': self.last_name\n        }from django import forms...
💾 实时保存完成 (40/310)

📊 进度报告: 40/310 (12.9%)
✅ 成功: 32/40 (80.0%)

📝 处理样本 41/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: 50672e38...
📏 Prompt长度: 4164 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.41s)
📝 生成长度: 293 字符
🔍 生成预览: t_name = 'test'\n\n    def test_get_user_by_username(self):\n        user = User.objects.create(**self....
💾 实时保存完成 (41/310)

📝 处理样本 42/310
🗂️ 文件: src/nvim/menu.c
🔧 Commit: 93649cef...
📏 Prompt长度: 6240 字符
  ⚠️ Prompt过长(6240字符)，尝试截断...
  📏 截断后长度: 3992字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.05s)
💾 实时保存完成 (42/310)

📝 处理样本 43/310
🗂️ 文件: src/nvim/message.c
🔧 Commit: 3aef2b8b...
📏 Prompt长度: 3529 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (118.84s)
📝 生成长度: 763 字符
🔍 生成预览: e_id=None,\n        )\n\n        self._assert_response(\n            response,\n            400,\n        ...
💾 实时保存完成 (43/310)

📝 处理样本 44/310
🗂️ 文件: src/nvim/usercmd.c
🔧 Commit: 5eb8119d...
📏 Prompt长度: 5005 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.78s)
📝 生成长度: 572 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (44/310)

📝 处理样本 45/310
🗂️ 文件: src/nvim/usercmd.c
🔧 Commit: 0df1418f...
📏 Prompt长度: 5089 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.84s)
📝 生成长度: 3919 字符
🔍 生成预览: static char *uc_split_args(char *arg, char **args, size_t *arglens, size_t argc, size_t *lenp)\n{\n  c...
💾 实时保存完成 (45/310)

📝 处理样本 46/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: fa8b2b4c...
📏 Prompt长度: 10918 字符
  ⚠️ Prompt过长(10918字符)，尝试截断...
  📏 截断后长度: 7186字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.29s)
📝 生成长度: 430 字符
🔍 生成预览: self._set_state(self.STATE_IDLE)\n\n    def _set_state(self, state):\n        self.__state = state\n    ...
💾 实时保存完成 (46/310)

📝 处理样本 47/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: de72f909...
📏 Prompt长度: 9715 字符
  ⚠️ Prompt过长(9715字符)，尝试截断...
  📏 截断后长度: 6289字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (119.39s)
📝 生成长度: 1947 字符
🔍 生成预览: e_id=None, **kwargs):\n        """\n        :param str resource_name: The name of the resource.\n      ...
💾 实时保存完成 (47/310)

📝 处理样本 48/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: 3a8b8591...
📏 Prompt长度: 44938 字符
  ⚠️ Prompt过长(44938字符)，尝试截断...
  📏 截断后长度: 15065字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.54s)
💾 实时保存完成 (48/310)

📝 处理样本 49/310
🗂️ 文件: src/nvim/spellfile.c
🔧 Commit: a10a23aa...
📏 Prompt长度: 13355 字符
  ⚠️ Prompt过长(13355字符)，尝试截断...
  📏 截断后长度: 6025字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.04s)
💾 实时保存完成 (49/310)

📝 处理样本 50/310
🗂️ 文件: src/nvim/highlight.c
🔧 Commit: fa8fde99...
📏 Prompt长度: 10342 字符
  ⚠️ Prompt过长(10342字符)，尝试截断...
  📏 截断后长度: 5269字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.26s)
📝 生成长度: 143 字符
🔍 生成预览: t_name,\n            'last_name': self.last_name\n        }\n\n    def __repr__(self):\n        return '<...
💾 实时保存完成 (50/310)

📊 进度报告: 50/310 (16.1%)
✅ 成功: 39/50 (78.0%)

📝 处理样本 51/310
🗂️ 文件: src/nvim/message.c
🔧 Commit: c96020b2...
📏 Prompt长度: 2058 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.50s)
📝 生成长度: 1564 字符
🔍 生成预览: void msg_source(int attr)\n{\n  static int recursive = false;\n  if (recursive) { return; }\n  recursive...
💾 实时保存完成 (51/310)

📝 处理样本 52/310
🗂️ 文件: src/nvim/runtime.c
🔧 Commit: d6a6adf7...
📏 Prompt长度: 5135 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.87s)
📝 生成长度: 591 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (52/310)

📝 处理样本 53/310
🗂️ 文件: src/nvim/edit.c
🔧 Commit: 53b0688a...
📏 Prompt长度: 6691 字符
  ⚠️ Prompt过长(6691字符)，尝试截断...
  📏 截断后长度: 3650字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (118.66s)
📝 生成长度: 640 字符
🔍 生成预览: e:\n            self._logger.info("No data found for %s", self.__class__.__name__)\n\n    def _get_data...
💾 实时保存完成 (53/310)

📝 处理样本 54/310
🗂️ 文件: src/nvim/ex_getln.c
🔧 Commit: 747dec79...
📏 Prompt长度: 21275 字符
  ⚠️ Prompt过长(21275字符)，尝试截断...
  📏 截断后长度: 9201字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.05s)
📝 生成长度: 7345 字符
🔍 生成预览: e_id=None,\n            name="test-name",\n            description="test-description",\n            tag...
💾 实时保存完成 (54/310)

📝 处理样本 55/310
🗂️ 文件: src/nvim/runtime.c
🔧 Commit: 1d28bbf1...
📏 Prompt长度: 23378 字符
  ⚠️ Prompt过长(23378字符)，尝试截断...
  📏 截断后长度: 9964字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.06s)
💾 实时保存完成 (55/310)

📝 处理样本 56/310
🗂️ 文件: src/nvim/message.c
🔧 Commit: aa96a80d...
📏 Prompt长度: 749 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.53s)
📝 生成长度: 6340 字符
🔍 生成预览: void iemsg(const char *s)\n{\n  emsg(s);\n#ifdef ABORT_ON_INTERNAL_ERROR\n  set_vim_var_string(VV_ERRMSG...
💾 实时保存完成 (56/310)

📝 处理样本 57/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: a9e6cf0e...
📏 Prompt长度: 6234 字符
  ⚠️ Prompt过长(6234字符)，尝试截断...
  📏 截断后长度: 4303字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.48s)
📝 生成长度: 631 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (57/310)

📝 处理样本 58/310
🗂️ 文件: src/nvim/runtime.c
🔧 Commit: 64d147b4...
📏 Prompt长度: 7768 字符
  ⚠️ Prompt过长(7768字符)，尝试截断...
  📏 截断后长度: 4437字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.73s)
📝 生成长度: 824 字符
🔍 生成预览: n_id=None, **kwargs):\n        """\n        Get all the data of an object\n\n        :param obj_id: ID o...
💾 实时保存完成 (58/310)

📝 处理样本 59/310
🗂️ 文件: src/nvim/insexpand.c
🔧 Commit: b0569f58...
📏 Prompt长度: 5990 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.96s)
📝 生成长度: 685 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (59/310)

📝 处理样本 60/310
🗂️ 文件: src/nvim/insexpand.c
🔧 Commit: 5d1f0c3e...
📏 Prompt长度: 7817 字符
  ⚠️ Prompt过长(7817字符)，尝试截断...
  📏 截断后长度: 4858字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.28s)
📝 生成长度: 313 字符
🔍 生成预览: self._set_attr('name', name)\n\n    @property\n    def description(self):\n        """Description of the...
💾 实时保存完成 (60/310)

📊 进度报告: 60/310 (19.4%)
✅ 成功: 48/60 (80.0%)

📝 处理样本 61/310
🗂️ 文件: src/nvim/insexpand.c
🔧 Commit: dd77a006...
📏 Prompt长度: 6100 字符
  ⚠️ Prompt过长(6100字符)，尝试截断...
  📏 截断后长度: 4128字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.82s)
📝 生成长度: 325 字符
🔍 生成预览: t_name = "test"\n\n    def test_create_user(self):\n        user = UserFactory(first_name=self.first_na...
💾 实时保存完成 (61/310)

📝 处理样本 62/310
🗂️ 文件: src/nvim/insexpand.c
🔧 Commit: 66800021...
📏 Prompt长度: 6874 字符
  ⚠️ Prompt过长(6874字符)，尝试截断...
  📏 截断后长度: 3403字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.47s)
📝 生成长度: 1636 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=30)),\n            ],\n        ),...
💾 实时保存完成 (62/310)

📝 处理样本 63/310
🗂️ 文件: src/nvim/insexpand.c
🔧 Commit: 45d09b46...
📏 Prompt长度: 1169 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.01s)
📝 生成长度: 1098 字符
🔍 生成预览: static void get_next_spell_completion(linenr_T lnum)\n{\n  char **matches;\n  int num_matches = expand_...
💾 实时保存完成 (63/310)

📝 处理样本 64/310
🗂️ 文件: src/nvim/arabic.c
🔧 Commit: 71163587...
📏 Prompt长度: 3634 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.91s)
📝 生成长度: 1409 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (64/310)

📝 处理样本 65/310
🗂️ 文件: src/nvim/search.c
🔧 Commit: 88738bd2...
📏 Prompt长度: 4400 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.03s)
📝 生成长度: 2423 字符
🔍 生成预览: static int fuzzy_match_compute_score(const char_u *const str, const int strSz,\n                     ...
💾 实时保存完成 (65/310)

📝 处理样本 66/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: 7656cd52...
📏 Prompt长度: 5251 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.24s)
📝 生成长度: 898 字符
🔍 生成预览: nfig(AppConfig):
\n    name ='myproject'
\nfrom django.apps import AppConfig
\n
\n
\nclass MyprojectCojas...
💾 实时保存完成 (66/310)

📝 处理样本 67/310
🗂️ 文件: src/nvim/buffer.c
🔧 Commit: cd2d3aa4...
📏 Prompt长度: 11652 字符
  ⚠️ Prompt过长(11652字符)，尝试截断...
  📏 截断后长度: 5286字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.37s)
📝 生成长度: 757 字符
🔍 生成预览: self._logger.info("Starting %s", self.__class__.__name__)\n        self._running = True\n\n        whil...
💾 实时保存完成 (67/310)

📝 处理样本 68/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 94a2bc59...
📏 Prompt长度: 6396 字符
  ⚠️ Prompt过长(6396字符)，尝试截断...
  📏 截断后长度: 3646字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.71s)
📝 生成长度: 8311 字符
🔍 生成预览: t_id=self._client_id, client_secret=self._client_secret)\n\n        self._session = OAuth2Session(clie...
💾 实时保存完成 (68/310)

📝 处理样本 69/310
🗂️ 文件: src/nvim/highlight_group.c
🔧 Commit: 8740e0bd...
📏 Prompt长度: 2420 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.09s)
📝 生成长度: 6115 字符
🔍 生成预览: e_path, 'w') as f:\n        json.dump(data, f)\n\nimport os\nimport json\nfrom typing import Dict, List\n\n...
💾 实时保存完成 (69/310)

📝 处理样本 70/310
🗂️ 文件: src/nvim/path.c
🔧 Commit: c62e5b50...
📏 Prompt长度: 2788 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.84s)
📝 生成长度: 679 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (70/310)

📊 进度报告: 70/310 (22.6%)
✅ 成功: 58/70 (82.9%)

📝 处理样本 71/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: 7243b1cb...
📏 Prompt长度: 38422 字符
  ⚠️ Prompt过长(38422字符)，尝试截断...
  📏 截断后长度: 13709字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.35s)
💾 实时保存完成 (71/310)

📝 处理样本 72/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 4a67f9d3...
📏 Prompt长度: 9497 字符
  ⚠️ Prompt过长(9497字符)，尝试截断...
  📏 截断后长度: 4753字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.78s)
📝 生成长度: 128 字符
🔍 生成预览: t_name = models.CharField(max_length=256)\n\n    def __unicode__(self):\n        return self.first_name...
💾 实时保存完成 (72/310)

📝 处理样本 73/310
🗂️ 文件: src/nvim/plines.c
🔧 Commit: 80a566b5...
📏 Prompt长度: 3548 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.11s)
📝 生成长度: 1003 字符
🔍 生成预览: void init_chartabsize_arg(chartabsize_T *cts, win_T *wp, linenr_T lnum, colnr_T col, char *line,\n   ...
💾 实时保存完成 (73/310)

📝 处理样本 74/310
🗂️ 文件: src/nvim/plines.c
🔧 Commit: 06f9da54...
📏 Prompt长度: 3505 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.26s)
📝 生成长度: 1486 字符
🔍 生成预览: void init_chartabsize_arg(chartabsize_T *cts, win_T *wp, linenr_T lnum, colnr_T col, char *line,\n   ...
💾 实时保存完成 (74/310)

📝 处理样本 75/310
🗂️ 文件: src/nvim/debugger.c
🔧 Commit: ec1f153d...
📏 Prompt长度: 16382 字符
  ⚠️ Prompt过长(16382字符)，尝试截断...
  📏 截断后长度: 7142字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.21s)
📝 生成长度: 656 字符
🔍 生成预览: e_path = os.path.join(self._output_dir,'model.ckpt-{}'.format(global_step))\n            self._saver....
💾 实时保存完成 (75/310)

📝 处理样本 76/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: f19e91ac...
📏 Prompt长度: 65274 字符
  ⚠️ Prompt过长(65274字符)，尝试截断...
  📏 截断后长度: 21976字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.60s)
💾 实时保存完成 (76/310)

📝 处理样本 77/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: 72e10414...
📏 Prompt长度: 15685 字符
  ⚠️ Prompt过长(15685字符)，尝试截断...
  📏 截断后长度: 7178字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (68.54s)
📝 生成长度: 1120 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (77/310)

📝 处理样本 78/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 18139a69...
📏 Prompt长度: 2349 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (75.11s)
📝 生成长度: 1858 字符
🔍 生成预览: void set_option_sctx_idx(int opt_idx, int opt_flags, sctx_T script_ctx)\n{\n  int both = (opt_flags & ...
💾 实时保存完成 (78/310)

📝 处理样本 79/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: 9413f754...
📏 Prompt长度: 1430 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.96s)
📝 生成长度: 491 字符
🔍 生成预览: static void ex_redrawstatus(exarg_T *eap)\n{\n  if (cmdpreview) {\n    return;  \n  }\n  int r = Redrawin...
💾 实时保存完成 (79/310)

📝 处理样本 80/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: 2e4532be...
📏 Prompt长度: 1511 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.16s)
📝 生成长度: 891 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (80/310)

📊 进度报告: 80/310 (25.8%)
✅ 成功: 66/80 (82.5%)

📝 处理样本 81/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: ad1f353f...
📏 Prompt长度: 1580 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.09s)
📝 生成长度: 1520 字符
🔍 生成预览: static void ex_redrawstatus(exarg_T *eap)\n{\n  if (cmdpreview) {\n    return;  \n  }\n  int r = Redrawin...
💾 实时保存完成 (81/310)

📝 处理样本 82/310
🗂️ 文件: src/nvim/statusline.c
🔧 Commit: 1bced9bf...
📏 Prompt长度: 8468 字符
  ⚠️ Prompt过长(8468字符)，尝试截断...
  📏 截断后长度: 4689字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.35s)
📝 生成长度: 3647 字符
🔍 生成预览: return self._data\n\n    def __getitem__(self, key: str) -> Any:\n        return self._data[key]\n\nfrom ...
💾 实时保存完成 (82/310)

📝 处理样本 83/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 33f14714...
📏 Prompt长度: 22848 字符
  ⚠️ Prompt过长(22848字符)，尝试截断...
  📏 截断后长度: 8430字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.43s)
📝 生成长度: 1644 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (83/310)

📝 处理样本 84/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 43718862...
📏 Prompt长度: 22828 字符
  ⚠️ Prompt过长(22828字符)，尝试截断...
  📏 截断后长度: 8491字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.97s)
📝 生成长度: 745 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=50)),\n            ],\n        ),\n...
💾 实时保存完成 (84/310)

📝 处理样本 85/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: 0f2ead02...
📏 Prompt长度: 54845 字符
  ⚠️ Prompt过长(54845字符)，尝试截断...
  📏 截断后长度: 20123字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.47s)
💾 实时保存完成 (85/310)

📝 处理样本 86/310
🗂️ 文件: src/nvim/arglist.c
🔧 Commit: dcdb7dca...
📏 Prompt长度: 17005 字符
  ⚠️ Prompt过长(17005字符)，尝试截断...
  📏 截断后长度: 6871字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.50s)
📝 生成长度: 1293 字符
🔍 生成预览: self._test_get_by_id('1', '1')\n        self._test_get_by_id('1', '2')\n\n    def test_get_by_id_not_fo...
💾 实时保存完成 (86/310)

📝 处理样本 87/310
🗂️ 文件: src/nvim/arglist.c
🔧 Commit: a66b1237...
📏 Prompt长度: 3507 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.63s)
📝 生成长度: 41 字符
🔍 生成预览: s.path.join(os.getcwd(), 'data')import os...
💾 实时保存完成 (87/310)

📝 处理样本 88/310
🗂️ 文件: src/nvim/autocmd.c
🔧 Commit: 42afb915...
📏 Prompt长度: 8294 字符
  ⚠️ Prompt过长(8294字符)，尝试截断...
  📏 截断后长度: 4385字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (53.02s)
📝 生成长度: 1002 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (88/310)

📝 处理样本 89/310
🗂️ 文件: src/nvim/help.c
🔧 Commit: 30104695...
📏 Prompt长度: 9975 字符
  ⚠️ Prompt过长(9975字符)，尝试截断...
  📏 截断后长度: 5192字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.79s)
📝 生成长度: 327 字符
🔍 生成预览: t_name = "test_project"\n\n    def test_create_project(self):\n        project = ProjectFactory()\n\n    ...
💾 实时保存完成 (89/310)

📝 处理样本 90/310
🗂️ 文件: src/nvim/autocmd.c
🔧 Commit: 81058119...
📏 Prompt长度: 22903 字符
  ⚠️ Prompt过长(22903字符)，尝试截断...
  📏 截断后长度: 8882字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.44s)
📝 生成长度: 534 字符
🔍 生成预览: t_id,\n            'user': user\n        })\n\n    def test_create_user_with_invalid_email(self):\n      ...
💾 实时保存完成 (90/310)

📊 进度报告: 90/310 (29.0%)
✅ 成功: 75/90 (83.3%)

📝 处理样本 91/310
🗂️ 文件: src/nvim/eval/typval.c
🔧 Commit: 62a0c9f8...
📏 Prompt长度: 1534 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.54s)
📝 生成长度: 875 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (91/310)

📝 处理样本 92/310
🗂️ 文件: src/nvim/window.c
🔧 Commit: 3b562535...
📏 Prompt长度: 1603 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.43s)
📝 生成长度: 761 字符
🔍 生成预览: void win_new_screensize(void)\n{\n  static long old_Rows = 0;\n  static long old_Columns = 0;\n  if (old...
💾 实时保存完成 (92/310)

📝 处理样本 93/310
🗂️ 文件: src/nvim/quickfix.c
🔧 Commit: 5740b3e0...
📏 Prompt长度: 3320 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.85s)
📝 生成长度: 1356 字符
🔍 生成预览: static list_T *call_qftf_func(qf_list_T *qfl, int qf_winid, long start_idx, long end_idx)\n{\n  Callba...
💾 实时保存完成 (93/310)

📝 处理样本 94/310
🗂️ 文件: src/nvim/indent.c
🔧 Commit: eaac0958...
📏 Prompt长度: 1424 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.42s)
📝 生成长度: 1008 字符
🔍 生成预览: static int lisp_match(char_u *p)\n{\n  char_u buf[LSIZE];\n  int len;\n  char *word = (char *)(*curbuf->...
💾 实时保存完成 (94/310)

📝 处理样本 95/310
🗂️ 文件: src/nvim/change.c
🔧 Commit: 32ced1f0...
📏 Prompt长度: 53964 字符
  ⚠️ Prompt过长(53964字符)，尝试截断...
  📏 截断后长度: 18051字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.42s)
💾 实时保存完成 (95/310)

📝 处理样本 96/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: bc798dfd...
📏 Prompt长度: 51270 字符
  ⚠️ Prompt过长(51270字符)，尝试截断...
  📏 截断后长度: 17251字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.47s)
💾 实时保存完成 (96/310)

📝 处理样本 97/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: c8fbf39d...
📏 Prompt长度: 46257 字符
  ⚠️ Prompt过长(46257字符)，尝试截断...
  📏 截断后长度: 16167字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.09s)
💾 实时保存完成 (97/310)

📝 处理样本 98/310
🗂️ 文件: src/nvim/buffer.c
🔧 Commit: 66933b45...
📏 Prompt长度: 19397 字符
  ⚠️ Prompt过长(19397字符)，尝试截断...
  📏 截断后长度: 7565字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.57s)
📝 生成长度: 415 字符
🔍 生成预览: t_name='test_project',\n            project_description='test_description'\n        )\n\n        self.as...
💾 实时保存完成 (98/310)

📝 处理样本 99/310
🗂️ 文件: src/nvim/getchar.c
🔧 Commit: a288b4f2...
📏 Prompt长度: 29527 字符
  ⚠️ Prompt过长(29527字符)，尝试截断...
  📏 截断后长度: 11489字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.42s)
💾 实时保存完成 (99/310)

📝 处理样本 100/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 46a54dd6...
📏 Prompt长度: 12030 字符
  ⚠️ Prompt过长(12030字符)，尝试截断...
  📏 截断后长度: 5908字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.30s)
📝 生成长度: 539 字符
🔍 生成预览: t_name = 'test'\n        self._test_email = '<EMAIL>'\n\n    def test_create_user(self):\n        user =...
💾 实时保存完成 (100/310)

📊 进度报告: 100/310 (32.3%)
✅ 成功: 81/100 (81.0%)

📝 处理样本 101/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: 1f438b23...
📏 Prompt长度: 9626 字符
  ⚠️ Prompt过长(9626字符)，尝试截断...
  📏 截断后长度: 4451字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.86s)
📝 生成长度: 702 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (101/310)

📝 处理样本 102/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: acbfbbb6...
📏 Prompt长度: 4378 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.54s)
📝 生成长度: 7771 字符
🔍 生成预览: t_id, self._client_secret)\n\n        # Get an access token for the user\n        auth_url = 'https://a...
💾 实时保存完成 (102/310)

📝 处理样本 103/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 807c6bb9...
📏 Prompt长度: 14215 字符
  ⚠️ Prompt过长(14215字符)，尝试截断...
  📏 截断后长度: 6641字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.19s)
📝 生成长度: 5893 字符
🔍 生成预览: n_id=None,\n            user_id=None,\n            project_id=None,\n            domain_id=None,\n      ...
💾 实时保存完成 (103/310)

📝 处理样本 104/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: b7933950...
📏 Prompt长度: 13242 字符
  ⚠️ Prompt过长(13242字符)，尝试截断...
  📏 截断后长度: 5808字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.29s)
📝 生成长度: 1150 字符
🔍 生成预览: return self._name\n\n    @property\n    def description(self):\n        """Return the description of thi...
💾 实时保存完成 (104/310)

📝 处理样本 105/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: e3acf913...
📏 Prompt长度: 14575 字符
  ⚠️ Prompt过长(14575字符)，尝试截断...
  📏 截断后长度: 6748字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.06s)
📝 生成长度: 446 字符
🔍 生成预览: self._set_attr('is_active', value)\n\n    @property\n    def is_deleted(self):\n        """Gets whether ...
💾 实时保存完成 (105/310)

📝 处理样本 106/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: 762ca670...
📏 Prompt长度: 805 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.24s)
📝 生成长度: 477 字符
🔍 生成预览: t_id = self._get_next_id()\n        return self._id_to_node[next_id]\n\n    def _get_next_id(self):\n   ...
💾 实时保存完成 (106/310)

📝 处理样本 107/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: 39f85cdf...
📏 Prompt长度: 1738 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.22s)
📝 生成长度: 899 字符
🔍 生成预览: t_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (107/310)

📝 处理样本 108/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: a86295cd...
📏 Prompt长度: 7256 字符
  ⚠️ Prompt过长(7256字符)，尝试截断...
  📏 截断后长度: 4242字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.36s)
📝 生成长度: 2044 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (108/310)

📝 处理样本 109/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: 8ba7a966...
📏 Prompt长度: 9356 字符
  ⚠️ Prompt过长(9356字符)，尝试截断...
  📏 截断后长度: 5164字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.01s)
📝 生成长度: 627 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=255)),\n            ],\n        ),...
💾 实时保存完成 (109/310)

📝 处理样本 110/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: 6956971e...
📏 Prompt长度: 10890 字符
  ⚠️ Prompt过长(10890字符)，尝试截断...
  📏 截断后长度: 5434字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.26s)
📝 生成长度: 1114 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (110/310)

📊 进度报告: 110/310 (35.5%)
✅ 成功: 91/110 (82.7%)

📝 处理样本 111/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: 30cfdd0e...
📏 Prompt长度: 38401 字符
  ⚠️ Prompt过长(38401字符)，尝试截断...
  📏 截断后长度: 13650字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.42s)
💾 实时保存完成 (111/310)

📝 处理样本 112/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: 38c113ae...
📏 Prompt长度: 1538 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.55s)
📝 生成长度: 968 字符
🔍 生成预览: static void f_deepcopy(typval_T *argvars, typval_T *rettv, EvalFuncData fptr)\n{\n  bool noref = false...
💾 实时保存完成 (112/310)

📝 处理样本 113/310
🗂️ 文件: src/nvim/plines.c
🔧 Commit: 63741205...
📏 Prompt长度: 9981 字符
  ⚠️ Prompt过长(9981字符)，尝试截断...
  📏 截断后长度: 5346字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.04s)
📝 生成长度: 635 字符
🔍 生成预览: t_id,\n                "name": self._name,\n                "description": self._description,\n        ...
💾 实时保存完成 (113/310)

📝 处理样本 114/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 8b430913...
📏 Prompt长度: 846 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.08s)
📝 生成长度: 814 字符
🔍 生成预览: char *get_flp_value(buf_T *buf)\n{\n  if (buf->b_p_flp == NULL || *buf->b_p_flp == NUL) {\n    return p...
💾 实时保存完成 (114/310)

📝 处理样本 115/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: cb8bc9b3...
📏 Prompt长度: 39992 字符
  ⚠️ Prompt过长(39992字符)，尝试截断...
  📏 截断后长度: 14408字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.37s)
💾 实时保存完成 (115/310)

📝 处理样本 116/310
🗂️ 文件: src/nvim/quickfix.c
🔧 Commit: d5dd7573...
📏 Prompt长度: 2852 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.33s)
📝 生成长度: 3841 字符
🔍 生成预览: static int vgr_process_args(exarg_T *eap, vgr_args_T *args)\n{\n  CLEAR_POINTER(args);\n  args->regmatc...
💾 实时保存完成 (116/310)

📝 处理样本 117/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 7d7208a8...
📏 Prompt长度: 2734 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.95s)
📝 生成长度: 1770 字符
🔍 生成预览: int eval0(char *arg, typval_T *rettv, char **nextcmd, int evaluate)\n{\n  int ret;\n  char *p;\n  const ...
💾 实时保存完成 (117/310)

📝 处理样本 118/310
🗂️ 文件: src/nvim/eval/typval.c
🔧 Commit: 4740672b...
📏 Prompt长度: 1278 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.64s)
📝 生成长度: 1589 字符
🔍 生成预览: int tv_dict_wrong_func_name(dict_T *d, typval_T *tv, const char *name)\n{\n  return ((d == &globvardic...
💾 实时保存完成 (118/310)

📝 处理样本 119/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 7683199a...
📏 Prompt长度: 14256 字符
  ⚠️ Prompt过长(14256字符)，尝试截断...
  📏 截断后长度: 6367字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.26s)
📝 生成长度: 7930 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (119/310)

📝 处理样本 120/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 7404c601...
📏 Prompt长度: 25195 字符
  ⚠️ Prompt过长(25195字符)，尝试截断...
  📏 截断后长度: 9072字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.44s)
📝 生成长度: 1607 字符
🔍 生成预览: t_id,\n            "project": self._project_id\n        })\n\n        # Create a new instance of the API...
💾 实时保存完成 (120/310)

📊 进度报告: 120/310 (38.7%)
✅ 成功: 99/120 (82.5%)

📝 处理样本 121/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: ed01ef7f...
📏 Prompt长度: 2069 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.54s)
📝 生成长度: 2557 字符
🔍 生成预览: bool var_wrong_func_name(const char *const name, const bool new_var)\n  FUNC_ATTR_NONNULL_ALL FUNC_AT...
💾 实时保存完成 (121/310)

📝 处理样本 122/310
🗂️ 文件: src/nvim/shada.c
🔧 Commit: 728c69bc...
📏 Prompt长度: 7194 字符
  ⚠️ Prompt过长(7194字符)，尝试截断...
  📏 截断后长度: 2882字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.60s)
📝 生成长度: 1753 字符
🔍 生成预览: int shada_write_file(const char *const file, bool nomerge)\n{\n  if (shada_disabled()) {\n    return FA...
💾 实时保存完成 (122/310)

📝 处理样本 123/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: 3b3611a3...
📏 Prompt长度: 6404 字符
  ⚠️ Prompt过长(6404字符)，尝试截断...
  📏 截断后长度: 4323字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.13s)
📝 生成长度: 700 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (123/310)

📝 处理样本 124/310
🗂️ 文件: src/nvim/popupmenu.c
🔧 Commit: 2425fe2d...
📏 Prompt长度: 7126 字符
  ⚠️ Prompt过长(7126字符)，尝试截断...
  📏 截断后长度: 3923字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.82s)
📝 生成长度: 1445 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (124/310)

📝 处理样本 125/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 7335a67b...
📏 Prompt长度: 7455 字符
  ⚠️ Prompt过长(7455字符)，尝试截断...
  📏 截断后长度: 3881字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.49s)
📝 生成长度: 180 字符
🔍 生成预览: self._set_attr("name", name)\n        self._set_attr("type", type_)\n\n    def _get_children(self):\n   ...
💾 实时保存完成 (125/310)

📝 处理样本 126/310
🗂️ 文件: src/nvim/ex_eval.c
🔧 Commit: 2cb08601...
📏 Prompt长度: 4785 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.38s)
📝 生成长度: 1851 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (126/310)

📝 处理样本 127/310
🗂️ 文件: src/nvim/ex_eval.c
🔧 Commit: b2519725...
📏 Prompt长度: 5186 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.09s)
📝 生成长度: 1041 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (127/310)

📝 处理样本 128/310
🗂️ 文件: src/nvim/arglist.c
🔧 Commit: 9d7dc506...
📏 Prompt长度: 2219 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.75s)
📝 生成长度: 478 字符
🔍 生成预览: nfig.get('port', '5432')\n\n    def get_connection(self):\n        """\n        Get connection object\n\n ...
💾 实时保存完成 (128/310)

📝 处理样本 129/310
🗂️ 文件: src/nvim/cmdhist.c
🔧 Commit: 30604320...
📏 Prompt长度: 3976 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.97s)
📝 生成长度: 1863 字符
🔍 生成预览: void init_history(void)\n{\n  assert(p_hi >= 0 && p_hi <= INT_MAX);\n  int newlen = (int)p_hi;\n  int ol...
💾 实时保存完成 (129/310)

📝 处理样本 130/310
🗂️ 文件: src/nvim/cmdexpand.c
🔧 Commit: 2685d27c...
📏 Prompt长度: 7796 字符
  ⚠️ Prompt过长(7796字符)，尝试截断...
  📏 截断后长度: 4255字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.31s)
📝 生成长度: 794 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (130/310)

📊 进度报告: 130/310 (41.9%)
✅ 成功: 109/130 (83.8%)

📝 处理样本 131/310
🗂️ 文件: src/nvim/spell.c
🔧 Commit: 74399738...
📏 Prompt长度: 18096 字符
  ⚠️ Prompt过长(18096字符)，尝试截断...
  📏 截断后长度: 7488字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.07s)
💾 实时保存完成 (131/310)

📝 处理样本 132/310
🗂️ 文件: src/nvim/memline.c
🔧 Commit: 8cf38c2f...
📏 Prompt长度: 3159 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.13s)
📝 生成长度: 843 字符
🔍 生成预览: self._logger.info("Starting %s", self.__class__.__name__)\n        self._logger.debug("Using config f...
💾 实时保存完成 (132/310)

📝 处理样本 133/310
🗂️ 文件: src/nvim/os/process.c
🔧 Commit: a8489308...
📏 Prompt长度: 639 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (109.76s)
📝 生成长度: 1093 字符
🔍 生成预览: bool os_proc_running(int pid)\n{\n  int err = uv_kill(pid, 0);\n  if (!err && errno!= ESRCH) {\n    retu...
💾 实时保存完成 (133/310)

📝 处理样本 134/310
🗂️ 文件: src/nvim/match.c
🔧 Commit: e38ae3b7...
📏 Prompt长度: 7431 字符
  ⚠️ Prompt过长(7431字符)，尝试截断...
  📏 截断后长度: 3906字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.97s)
📝 生成长度: 1065 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (134/310)

📝 处理样本 135/310
🗂️ 文件: src/nvim/search.c
🔧 Commit: 3098064f...
📏 Prompt长度: 29533 字符
  ⚠️ Prompt过长(29533字符)，尝试截断...
  📏 截断后长度: 10296字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.73s)
📝 生成长度: 836 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (135/310)

📝 处理样本 136/310
🗂️ 文件: src/nvim/eval/buffer.c
🔧 Commit: 1282d6d5...
📏 Prompt长度: 8194 字符
  ⚠️ Prompt过长(8194字符)，尝试截断...
  📏 截断后长度: 4539字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.72s)
📝 生成长度: 678 字符
🔍 生成预览: t_name='test_project',\n            project_description='test description'\n        )\n\n        self.as...
💾 实时保存完成 (136/310)

📝 处理样本 137/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: ec52658a...
📏 Prompt长度: 14458 字符
  ⚠️ Prompt过长(14458字符)，尝试截断...
  📏 截断后长度: 5750字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.74s)
📝 生成长度: 1135 字符
🔍 生成预览: return self._name\n\n    @property\n    def description(self):\n        """Return the description of thi...
💾 实时保存完成 (137/310)

📝 处理样本 138/310
🗂️ 文件: src/nvim/window.c
🔧 Commit: 47d3d010...
📏 Prompt长度: 1666 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.37s)
📝 生成长度: 1640 字符
🔍 生成预览: static win_T *win_free_mem(win_T *win, int *dirp, tabpage_T *tp)\n{\n  win_T *wp;\n  if (!win->w_floati...
💾 实时保存完成 (138/310)

📝 处理样本 139/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 7d101944...
📏 Prompt长度: 3189 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.64s)
📝 生成长度: 1044 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (139/310)

📝 处理样本 140/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 1aad5af6...
📏 Prompt长度: 2581 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.99s)
📝 生成长度: 8721 字符
🔍 生成预览: e:\n        print("No")shinkeonkim/today-psBOJ/17000~17999/17600~17699/17643.py1-10\nfor _ in range(in...
💾 实时保存完成 (140/310)

📊 进度报告: 140/310 (45.2%)
✅ 成功: 118/140 (84.3%)

📝 处理样本 141/310
🗂️ 文件: src/nvim/quickfix.c
🔧 Commit: 1e6d5fdf...
📏 Prompt长度: 3116 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.34s)
📝 生成长度: 9058 字符
🔍 生成预览: t_name, 'w') as f:\n            json.dump(self._data, f)\n\n    def get_data(self):\n        """Get data...
💾 实时保存完成 (141/310)

📝 处理样本 142/310
🗂️ 文件: src/nvim/quickfix.c
🔧 Commit: aa492127...
📏 Prompt长度: 6405 字符
  ⚠️ Prompt过长(6405字符)，尝试截断...
  📏 截断后长度: 4145字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.55s)
📝 生成长度: 679 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (142/310)

📝 处理样本 143/310
🗂️ 文件: src/nvim/regexp.c
🔧 Commit: f6caa35e...
📏 Prompt长度: 5506 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (12.17s)
📝 生成长度: 797 字符
🔍 生成预览: t_name = "test"\n\n    def test_create_user(self):\n        user = UserFactory()\n\n        self.assertEq...
💾 实时保存完成 (143/310)

📝 处理样本 144/310
🗂️ 文件: src/nvim/help.c
🔧 Commit: b69c5817...
📏 Prompt长度: 14378 字符
  ⚠️ Prompt过长(14378字符)，尝试截断...
  📏 截断后长度: 5676字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.06s)
💾 实时保存完成 (144/310)

📝 处理样本 145/310
🗂️ 文件: src/nvim/regexp.c
🔧 Commit: 742d95a6...
📏 Prompt长度: 5544 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.85s)
📝 生成长度: 797 字符
🔍 生成预览: t_name = "test"\n\n    def test_create_user(self):\n        user = UserFactory()\n\n        self.assertEq...
💾 实时保存完成 (145/310)

📝 处理样本 146/310
🗂️ 文件: src/nvim/search.c
🔧 Commit: 3f1ee12d...
📏 Prompt长度: 2191 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (110.84s)
📝 生成长度: 2708 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (146/310)

📝 处理样本 147/310
🗂️ 文件: src/nvim/search.c
🔧 Commit: 9476dd2f...
📏 Prompt长度: 2702 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.37s)
📝 生成长度: 249 字符
🔍 生成预览: t_name = 'test_project'\n\n    def test_create_project(self):\n        project = Project.objects.get(na...
💾 实时保存完成 (147/310)

📝 处理样本 148/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: 32810c08...
📏 Prompt长度: 16319 字符
  ⚠️ Prompt过长(16319字符)，尝试截断...
  📏 截断后长度: 6487字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.89s)
📝 生成长度: 211 字符
🔍 生成预览: self._data = data\n        self._index = index\n\n    def __repr__(self):\n        return '<{} {}>'.form...
💾 实时保存完成 (148/310)

📝 处理样本 149/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: d93b9062...
📏 Prompt长度: 16471 字符
  ⚠️ Prompt过长(16471字符)，尝试截断...
  📏 截断后长度: 6542字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.65s)
📝 生成长度: 386 字符
🔍 生成预览: self._set_value('max_length', value)\n\n    @property\n    def min_length(self):\n        """Gets the mi...
💾 实时保存完成 (149/310)

📝 处理样本 150/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 11d27042...
📏 Prompt长度: 3831 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.44s)
📝 生成长度: 1124 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (150/310)

📊 进度报告: 150/310 (48.4%)
✅ 成功: 127/150 (84.7%)

📝 处理样本 151/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 6f9cda0f...
📏 Prompt长度: 3932 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.85s)
📝 生成长度: 1059 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (151/310)

📝 处理样本 152/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 10af0549...
📏 Prompt长度: 4278 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.19s)
📝 生成长度: 976 字符
🔍 生成预览: t_name = 'test'\n        self._test_email = '<EMAIL>'\n\n    def test_create_user(self):\n        user =...
💾 实时保存完成 (152/310)

📝 处理样本 153/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 0909d987...
📏 Prompt长度: 2810 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (84.67s)
📝 生成长度: 404 字符
🔍 生成预览: t_name='test', last_name='user')\n        self.assertEqual(str(user), user.email)\n\n    def test_new_s...
💾 实时保存完成 (153/310)

📝 处理样本 154/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 52b3e8bd...
📏 Prompt长度: 4910 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.68s)
📝 生成长度: 1570 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (154/310)

📝 处理样本 155/310
🗂️ 文件: src/nvim/fold.c
🔧 Commit: 6d7b94ea...
📏 Prompt长度: 5842 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (110.94s)
📝 生成长度: 2766 字符
🔍 生成预览: static void foldMarkAdjustRecurse(win_T *wp, garray_T *gap, linenr_T line1, linenr_T line2,\n        ...
💾 实时保存完成 (155/310)

📝 处理样本 156/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 4f64aef2...
📏 Prompt长度: 12991 字符
  ⚠️ Prompt过长(12991字符)，尝试截断...
  📏 截断后长度: 5635字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.76s)
📝 生成长度: 1207 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (156/310)

📝 处理样本 157/310
🗂️ 文件: src/nvim/edit.c
🔧 Commit: 70d6c335...
📏 Prompt长度: 48402 字符
  ⚠️ Prompt过长(48402字符)，尝试截断...
  📏 截断后长度: 20642字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.45s)
💾 实时保存完成 (157/310)

📝 处理样本 158/310
🗂️ 文件: src/nvim/ex_getln.c
🔧 Commit: 3dadd3ac...
📏 Prompt长度: 3346 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (60.94s)
📝 生成长度: 1751 字符
🔍 生成预览: static int command_line_insert_reg(CommandLineState *s)\n{\n  const int save_new_cmdpos = new_cmdpos;\n...
💾 实时保存完成 (158/310)

📝 处理样本 159/310
🗂️ 文件: src/nvim/window.c
🔧 Commit: 090048be...
📏 Prompt长度: 26852 字符
  ⚠️ Prompt过长(26852字符)，尝试截断...
  📏 截断后长度: 9910字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (50.63s)
📝 生成长度: 328 字符
🔍 生成预览: # noqa: F401\nfrom. import base  # noqa: F401\nfrom. import utilsyutiansut/RxPYrx/concurrency/mainloop...
💾 实时保存完成 (159/310)

📝 处理样本 160/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 6b3ae24a...
📏 Prompt长度: 4996 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.67s)
📝 生成长度: 1059 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (160/310)

📊 进度报告: 160/310 (51.6%)
✅ 成功: 136/160 (85.0%)

📝 处理样本 161/310
🗂️ 文件: src/nvim/indent.c
🔧 Commit: b201cbfc...
📏 Prompt长度: 1164 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.43s)
📝 生成长度: 1470 字符
🔍 生成预览: int get_indent_buf(buf_T *buf, linenr_T lnum)\n{\n  return get_indent_str_vtab(ml_get_buf(buf, lnum, f...
💾 实时保存完成 (161/310)

📝 处理样本 162/310
🗂️ 文件: src/nvim/usercmd.c
🔧 Commit: ff62d761...
📏 Prompt长度: 5089 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.89s)
📝 生成长度: 206 字符
🔍 生成预览: s.path.join(os.getcwd(),'src'))import os
\nimport sys
\n
\nsys.path.append(ojoseph-njogu/Projects-from-...
💾 实时保存完成 (162/310)

📝 处理样本 163/310
🗂️ 文件: src/nvim/memline.c
🔧 Commit: 2d759924...
📏 Prompt长度: 8582 字符
  ⚠️ Prompt过长(8582字符)，尝试截断...
  📏 截断后长度: 4670字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.04s)
💾 实时保存完成 (163/310)

📝 处理样本 164/310
🗂️ 文件: src/nvim/main.c
🔧 Commit: dae21358...
📏 Prompt长度: 9810 字符
  ⚠️ Prompt过长(9810字符)，尝试截断...
  📏 截断后长度: 5983字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.72s)
📝 生成长度: 905 字符
🔍 生成预览: static void init_startuptime(mparm_T *paramp)\n{\n  for (int i = 1; i < paramp->argc - 1; i++) {\n    i...
💾 实时保存完成 (164/310)

📝 处理样本 165/310
🗂️ 文件: src/nvim/hashtab.c
🔧 Commit: 59012a18...
📏 Prompt长度: 7417 字符
  ⚠️ Prompt过长(7417字符)，尝试截断...
  📏 截断后长度: 4427字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.59s)
📝 生成长度: 760 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=64)),\n                ('descrip...
💾 实时保存完成 (165/310)

📝 处理样本 166/310
🗂️ 文件: src/nvim/hashtab.c
🔧 Commit: 9bab4b72...
📏 Prompt长度: 7534 字符
  ⚠️ Prompt过长(7534字符)，尝试截断...
  📏 截断后长度: 4480字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.52s)
📝 生成长度: 746 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (166/310)

📝 处理样本 167/310
🗂️ 文件: src/nvim/normal.c
🔧 Commit: b884d0b3...
📏 Prompt长度: 6524 字符
  ⚠️ Prompt过长(6524字符)，尝试截断...
  📏 截断后长度: 3851字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.04s)
📝 生成长度: 1426 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (167/310)

📝 处理样本 168/310
🗂️ 文件: src/nvim/statusline.c
🔧 Commit: ef77598b...
📏 Prompt长度: 1519 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (109.52s)
📝 生成长度: 6550 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (168/310)

📝 处理样本 169/310
🗂️ 文件: src/nvim/spellsuggest.c
🔧 Commit: 89f45dc1...
📏 Prompt长度: 16173 字符
  ⚠️ Prompt过长(16173字符)，尝试截断...
  📏 截断后长度: 7203字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.59s)
📝 生成长度: 8444 字符
🔍 生成预览: t_id,\n            'name': self._name\n        })\n\n    def __repr__(self):\n        return '<{} {}>'.fo...
💾 实时保存完成 (169/310)

📝 处理样本 170/310
🗂️ 文件: src/nvim/cmdexpand.c
🔧 Commit: 245522db...
📏 Prompt长度: 10560 字符
  ⚠️ Prompt过长(10560字符)，尝试截断...
  📏 截断后长度: 5666字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.85s)
📝 生成长度: 1703 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=150)),\n                ('descri...
💾 实时保存完成 (170/310)

📊 进度报告: 170/310 (54.8%)
✅ 成功: 145/170 (85.3%)

📝 处理样本 171/310
🗂️ 文件: src/nvim/cmdexpand.c
🔧 Commit: 15e42dd4...
📏 Prompt长度: 6622 字符
  ⚠️ Prompt过长(6622字符)，尝试截断...
  📏 截断后长度: 4401字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.66s)
📝 生成长度: 229 字符
🔍 生成预览: t_id,\n            'name': self._name\n        }\n\n    def __repr__(self):\n        return '<{} {}>'.for...
💾 实时保存完成 (171/310)

📝 处理样本 172/310
🗂️ 文件: src/nvim/cmdexpand.c
🔧 Commit: 8abf53be...
📏 Prompt长度: 4823 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (28.40s)
📝 生成长度: 1188 字符
🔍 生成预览: self._data = data\n\n    def __repr__(self):\n        return f"<{type(self).__name__}: {self._data}>"\n\n...
💾 实时保存完成 (172/310)

📝 处理样本 173/310
🗂️ 文件: src/nvim/ex_docmd.c
🔧 Commit: c416da9d...
📏 Prompt长度: 10267 字符
  ⚠️ Prompt过长(10267字符)，尝试截断...
  📏 截断后长度: 4712字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.28s)
📝 生成长度: 653 字符
🔍 生成预览: rue)\n        self._test_get_all_by_filters(\n            filters={'name': 'fake-name'}, expected=[sel...
💾 实时保存完成 (173/310)

📝 处理样本 174/310
🗂️ 文件: src/nvim/popupmenu.c
🔧 Commit: 6e3890f4...
📏 Prompt长度: 18787 字符
  ⚠️ Prompt过长(18787字符)，尝试截断...
  📏 截断后长度: 7599字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.28s)
📝 生成长度: 482 字符
🔍 生成预览: t_id=self._client_id, client_secret=self._client_secret)\n\n        self._session = OAuth2Session(clie...
💾 实时保存完成 (174/310)

📝 处理样本 175/310
🗂️ 文件: src/nvim/indent.c
🔧 Commit: 51d082b4...
📏 Prompt长度: 8193 字符
  ⚠️ Prompt过长(8193字符)，尝试截断...
  📏 截断后长度: 4733字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.77s)
📝 生成长度: 1126 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (175/310)

📝 处理样本 176/310
🗂️ 文件: src/nvim/textformat.c
🔧 Commit: 108452aa...
📏 Prompt长度: 4206 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.13s)
📝 生成长度: 3066 字符
🔍 生成预览: t_name = 'test'\n        self._test_user = UserFactory()\n\n    def test_create_user_with_email_success...
💾 实时保存完成 (176/310)

📝 处理样本 177/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: 0f633ff4...
📏 Prompt长度: 8693 字符
  ⚠️ Prompt过长(8693字符)，尝试截断...
  📏 截断后长度: 4742字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.22s)
📝 生成长度: 7897 字符
🔍 生成预览: t_id,\n            'name': self._name,\n          'status': self._status,\n            'created_at': se...
💾 实时保存完成 (177/310)

📝 处理样本 178/310
🗂️ 文件: src/nvim/textformat.c
🔧 Commit: dbb6c7f1...
📏 Prompt长度: 4394 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (73.20s)
📝 生成长度: 2984 字符
🔍 生成预览: static bool same_leader(linenr_T lnum, int leader1_len, char *leader1_flags, int leader2_len,\n      ...
💾 实时保存完成 (178/310)

📝 处理样本 179/310
🗂️ 文件: src/nvim/getchar.c
🔧 Commit: c94d8e7f...
📏 Prompt长度: 27068 字符
  ⚠️ Prompt过长(27068字符)，尝试截断...
  📏 截断后长度: 9613字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.66s)
📝 生成长度: 1520 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=64)),\n            ],\n        ),...
💾 实时保存完成 (179/310)

📝 处理样本 180/310
🗂️ 文件: src/nvim/optionstr.c
🔧 Commit: 4c90a84c...
📏 Prompt长度: 1796 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.36s)
📝 生成长度: 1413 字符
🔍 生成预览: static void did_set_virtualedit(win_T *win, int opt_flags, char *oldval, char **errmsg)\n{\n  char *ve...
💾 实时保存完成 (180/310)

📊 进度报告: 180/310 (58.1%)
✅ 成功: 155/180 (86.1%)

📝 处理样本 181/310
🗂️ 文件: src/nvim/search.c
🔧 Commit: 7880eeb2...
📏 Prompt长度: 5661 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.52s)
📝 生成长度: 1656 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (181/310)

📝 处理样本 182/310
🗂️ 文件: src/nvim/normal.c
🔧 Commit: fa35d3c3...
📏 Prompt长度: 7109 字符
  ⚠️ Prompt过长(7109字符)，尝试截断...
  📏 截断后长度: 4365字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.11s)
📝 生成长度: 230 字符
🔍 生成预览: t_name = "test"\n\n    def test_get_all_users(self):\n        self._create_user()\n\n        users = User...
💾 实时保存完成 (182/310)

📝 处理样本 183/310
🗂️ 文件: src/nvim/autocmd.c
🔧 Commit: 90333b24...
📏 Prompt长度: 7659 字符
  ⚠️ Prompt过长(7659字符)，尝试截断...
  📏 截断后长度: 4232字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (5.61s)
📝 生成长度: 324 字符
🔍 生成预览: t_name = 'test'\n\n    def test_get_user_profile(self):\n        user = UserFactory()\n\n        self.ass...
💾 实时保存完成 (183/310)

📝 处理样本 184/310
🗂️ 文件: src/nvim/change.c
🔧 Commit: 5c4b503d...
📏 Prompt长度: 12283 字符
  ⚠️ Prompt过长(12283字符)，尝试截断...
  📏 截断后长度: 5272字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.49s)
📝 生成长度: 278 字符
🔍 生成预览: t_name = 'test'\n\n    def test_get_user_by_username(self):\n        user = User.objects.create_user('t...
💾 实时保存完成 (184/310)

📝 处理样本 185/310
🗂️ 文件: src/nvim/normal.c
🔧 Commit: cbd4480f...
📏 Prompt长度: 10752 字符
  ⚠️ Prompt过长(10752字符)，尝试截断...
  📏 截断后长度: 5451字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.02s)
📝 生成长度: 8878 字符
🔍 生成预览: t_id,\n                'name': name,\n                'description': description,\n                'tag...
💾 实时保存完成 (185/310)

📝 处理样本 186/310
🗂️ 文件: src/nvim/buffer.c
🔧 Commit: 93c627b9...
📏 Prompt长度: 10223 字符
  ⚠️ Prompt过长(10223字符)，尝试截断...
  📏 截断后长度: 4908字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.22s)
📝 生成长度: 343 字符
🔍 生成预览: t_name = 'test'\n\n    def test_get_user_by_username(self):\n        user = User.objects.create_user('t...
💾 实时保存完成 (186/310)

📝 处理样本 187/310
🗂️ 文件: src/nvim/usercmd.c
🔧 Commit: 35074746...
📏 Prompt长度: 8008 字符
  ⚠️ Prompt过长(8008字符)，尝试截断...
  📏 截断后长度: 4195字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.42s)
📝 生成长度: 790 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (187/310)

📝 处理样本 188/310
🗂️ 文件: src/nvim/eval/typval.c
🔧 Commit: c76dfe14...
📏 Prompt长度: 3016 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.57s)
📝 生成长度: 1148 字符
🔍 生成预览: void tv_list_flatten(list_T *list, listitem_T *first, long maxitems, long maxdepth)\n  FUNC_ATTR_NONN...
💾 实时保存完成 (188/310)

📝 处理样本 189/310
🗂️ 文件: src/nvim/window.c
🔧 Commit: 1b632e99...
📏 Prompt长度: 7546 字符
  ⚠️ Prompt过长(7546字符)，尝试截断...
  📏 截断后长度: 4927字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.22s)
📝 生成长度: 1070 字符
🔍 生成预览: t_name = "test"\n        self._test_email = "<EMAIL>"\n\n    def test_create_user(self):\n        user =...
💾 实时保存完成 (189/310)

📝 处理样本 190/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 55d30c45...
📏 Prompt长度: 13843 字符
  ⚠️ Prompt过长(13843字符)，尝试截断...
  📏 截断后长度: 5848字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.01s)
📝 生成长度: 962 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (190/310)

📊 进度报告: 190/310 (61.3%)
✅ 成功: 165/190 (86.8%)

📝 处理样本 191/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 7aad75e2...
📏 Prompt长度: 3274 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.34s)
📝 生成长度: 712 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (191/310)

📝 处理样本 192/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 4bd0611d...
📏 Prompt长度: 2985 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.47s)
📝 生成长度: 790 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (192/310)

📝 处理样本 193/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: a974d151...
📏 Prompt长度: 173176 字符
  ⚠️ Prompt过长(173176字符)，尝试截断...
  📏 截断后长度: 58341字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.82s)
💾 实时保存完成 (193/310)

📝 处理样本 194/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: c2e602b9...
📏 Prompt长度: 173338 字符
  ⚠️ Prompt过长(173338字符)，尝试截断...
  📏 截断后长度: 58338字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.84s)
💾 实时保存完成 (194/310)

📝 处理样本 195/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: 4a3594f6...
📏 Prompt长度: 176509 字符
  ⚠️ Prompt过长(176509字符)，尝试截断...
  📏 截断后长度: 59908字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.78s)
💾 实时保存完成 (195/310)

📝 处理样本 196/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: bcf07741...
📏 Prompt长度: 179095 字符
  ⚠️ Prompt过长(179095字符)，尝试截断...
  📏 截断后长度: 61205字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.79s)
💾 实时保存完成 (196/310)

📝 处理样本 197/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: a88c18c2...
📏 Prompt长度: 6577 字符
  ⚠️ Prompt过长(6577字符)，尝试截断...
  📏 截断后长度: 3490字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.75s)
📝 生成长度: 705 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (197/310)

📝 处理样本 198/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: 446c353a...
📏 Prompt长度: 51266 字符
  ⚠️ Prompt过长(51266字符)，尝试截断...
  📏 截断后长度: 17427字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.49s)
💾 实时保存完成 (198/310)

📝 处理样本 199/310
🗂️ 文件: src/nvim/indent.c
🔧 Commit: 98e84643...
📏 Prompt长度: 8002 字符
  ⚠️ Prompt过长(8002字符)，尝试截断...
  📏 截断后长度: 4654字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.05s)
📝 生成长度: 1218 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (199/310)

📝 处理样本 200/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: 089f962d...
📏 Prompt长度: 19280 字符
  ⚠️ Prompt过长(19280字符)，尝试截断...
  📏 截断后长度: 8198字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.63s)
📝 生成长度: 358 字符
🔍 生成预览: e_path = os.path.join(os.getcwd(),'src', 'data','sample_data.csv')\n        self._df = pd.read_csv(fi...
💾 实时保存完成 (200/310)

📊 进度报告: 200/310 (64.5%)
✅ 成功: 170/200 (85.0%)

📝 处理样本 201/310
🗂️ 文件: src/nvim/quickfix.c
🔧 Commit: e0bbe8cc...
📏 Prompt长度: 4438 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.34s)
📝 生成长度: 7661 字符
🔍 生成预览: static int qf_buf_add_line(qf_list_T *qfl, buf_T *buf, linenr_T lnum, const qfline_T *qfp,\n         ...
💾 实时保存完成 (201/310)

📝 处理样本 202/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 2882b154...
📏 Prompt长度: 8281 字符
  ⚠️ Prompt过长(8281字符)，尝试截断...
  📏 截断后长度: 6235字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.34s)
📝 生成长度: 952 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (202/310)

📝 处理样本 203/310
🗂️ 文件: src/nvim/getchar.c
🔧 Commit: eaccd0de...
📏 Prompt长度: 27267 字符
  ⚠️ Prompt过长(27267字符)，尝试截断...
  📏 截断后长度: 9755字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.96s)
📝 生成长度: 741 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=64)),\n            ],\n        ),\n...
💾 实时保存完成 (203/310)

📝 处理样本 204/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 714f6bf2...
📏 Prompt长度: 9561 字符
  ⚠️ Prompt过长(9561字符)，尝试截断...
  📏 截断后长度: 5022字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.33s)
📝 生成长度: 4257 字符
🔍 生成预览: t_id=None,\n        )\n\n        # Create an instance of the API class\n        api_instance = openapi_c...
💾 实时保存完成 (204/310)

📝 处理样本 205/310
🗂️ 文件: src/nvim/indent_c.c
🔧 Commit: aab4443b...
📏 Prompt长度: 3198 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.81s)
📝 生成长度: 1315 字符
🔍 生成预览: static bool cin_is_cpp_namespace(const char *s)\n{\n  const char *p;\n  bool has_name = false;\n  bool h...
💾 实时保存完成 (205/310)

📝 处理样本 206/310
🗂️ 文件: src/nvim/edit.c
🔧 Commit: bad218cd...
📏 Prompt长度: 6885 字符
  ⚠️ Prompt过长(6885字符)，尝试截断...
  📏 截断后长度: 4403字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.00s)
📝 生成长度: 680 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (206/310)

📝 处理样本 207/310
🗂️ 文件: src/nvim/runtime.c
🔧 Commit: 4f66530a...
📏 Prompt长度: 9250 字符
  ⚠️ Prompt过长(9250字符)，尝试截断...
  📏 截断后长度: 5606字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.44s)
📝 生成长度: 1795 字符
🔍 生成预览: e_id=None, **kwargs):\n        """\n        Get all of the users that are in this group\n\n        :para...
💾 实时保存完成 (207/310)

📝 处理样本 208/310
🗂️ 文件: src/nvim/ex_getln.c
🔧 Commit: 23e9d625...
📏 Prompt长度: 1865 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.77s)
📝 生成长度: 1551 字符
🔍 生成预览: void f_setcmdline(typval_T *argvars, typval_T *rettv, EvalFuncData fptr)\n{\n  if (tv_check_for_string...
💾 实时保存完成 (208/310)

📝 处理样本 209/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: e8c25aac...
📏 Prompt长度: 10191 字符
  ⚠️ Prompt过长(10191字符)，尝试截断...
  📏 截断后长度: 5072字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.87s)
📝 生成长度: 938 字符
🔍 生成预览: t_name = 'test_project'\n        self._create_project()\n\n        # Create an experiment for our proje...
💾 实时保存完成 (209/310)

📝 处理样本 210/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 9ecfb42e...
📏 Prompt长度: 4424 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.47s)
📝 生成长度: 352 字符
🔍 生成预览: t_name = "test"\n\n    def test_create_user(self):\n        user = UserFactory()\n\n        self.assertEq...
💾 实时保存完成 (210/310)

📊 进度报告: 210/310 (67.7%)
✅ 成功: 180/210 (85.7%)

📝 处理样本 211/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 51f99a34...
📏 Prompt长度: 4943 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.30s)
📝 生成长度: 678 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=64)),\n                ('descrip...
💾 实时保存完成 (211/310)

📝 处理样本 212/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 64a91f5e...
📏 Prompt长度: 3901 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.24s)
📝 生成长度: 155 字符
🔍 生成预览: t_name = "test"\n\n    def test_get_all_users(self):\n        users = self._client.get_all_users()\n\n   ...
💾 实时保存完成 (212/310)

📝 处理样本 213/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: ff963d69...
📏 Prompt长度: 1901 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.14s)
📝 生成长度: 1527 字符
🔍 生成预览: char *eval_to_string_skip(char *arg, exarg_T *eap, const bool skip)\n  FUNC_ATTR_MALLOC FUNC_ATTR_NON...
💾 实时保存完成 (213/310)

📝 处理样本 214/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 9c65a187...
📏 Prompt长度: 3896 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.77s)
📝 生成长度: 379 字符
🔍 生成预览: return self._get_data('user/following', params=params)\n\n    def get_followers(self, user: str, **kwa...
💾 实时保存完成 (214/310)

📝 处理样本 215/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: c804c7df...
📏 Prompt长度: 9769 字符
  ⚠️ Prompt过长(9769字符)，尝试截断...
  📏 截断后长度: 5244字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.91s)
📝 生成长度: 834 字符
🔍 生成预览: e_id=None,\n        )\n\n        self._assert_request_is_correctly_built()\n\n    def test_build_request_...
💾 实时保存完成 (215/310)

📝 处理样本 216/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: aaacfd4a...
📏 Prompt长度: 6790 字符
  ⚠️ Prompt过长(6790字符)，尝试截断...
  📏 截断后长度: 3571字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.90s)
📝 生成长度: 955 字符
🔍 生成预览: t_name = 'test'\n        self._test_user = UserFactory(username=self._test_name)\n\n    def test_create...
💾 实时保存完成 (216/310)

📝 处理样本 217/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: ae9654dd...
📏 Prompt长度: 6076 字符
  ⚠️ Prompt过长(6076字符)，尝试截断...
  📏 截断后长度: 3501字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.42s)
📝 生成长度: 5637 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (217/310)

📝 处理样本 218/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 031cf60d...
📏 Prompt长度: 7648 字符
  ⚠️ Prompt过长(7648字符)，尝试截断...
  📏 截断后长度: 4321字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.22s)
📝 生成长度: 2829 字符
🔍 生成预览: self._set_status('error', message=message)\n\n    def _set_status(self, status, message=''):\n        "...
💾 实时保存完成 (218/310)

📝 处理样本 219/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 9636ae6a...
📏 Prompt长度: 2390 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.74s)
📝 生成长度: 8895 字符
🔍 生成预览: static int eval_env_var(char **arg, typval_T *rettv, int evaluate)\n{\n  (*arg)++;\n  char *name = *arg...
💾 实时保存完成 (219/310)

📝 处理样本 220/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 700152fb...
📏 Prompt长度: 9906 字符
  ⚠️ Prompt过长(9906字符)，尝试截断...
  📏 截断后长度: 5228字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.84s)
📝 生成长度: 5611 字符
🔍 生成预览: e_path, 'w', encoding='utf-8') as f:\n            json.dump(self._data, f, indent=indent)\n\n    def lo...
💾 实时保存完成 (220/310)

📊 进度报告: 220/310 (71.0%)
✅ 成功: 190/220 (86.4%)

📝 处理样本 221/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: 2cf8f01e...
📏 Prompt长度: 8997 字符
  ⚠️ Prompt过长(8997字符)，尝试截断...
  📏 截断后长度: 4292字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.49s)
📝 生成长度: 955 字符
🔍 生成预览: t_name = 'test'\n        self._test_user = UserFactory(username=self._test_name)\n\n    def test_create...
💾 实时保存完成 (221/310)

📝 处理样本 222/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: 3c16e75a...
📏 Prompt长度: 8296 字符
  ⚠️ Prompt过长(8296字符)，尝试截断...
  📏 截断后长度: 4182字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.67s)
📝 生成长度: 1720 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (222/310)

📝 处理样本 223/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: c6ebcd52...
📏 Prompt长度: 9370 字符
  ⚠️ Prompt过长(9370字符)，尝试截断...
  📏 截断后长度: 4994字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.33s)
📝 生成长度: 795 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=255)),\n                ('descri...
💾 实时保存完成 (223/310)

📝 处理样本 224/310
🗂️ 文件: src/nvim/runtime.c
🔧 Commit: c5ec823a...
📏 Prompt长度: 5715 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (5.31s)
📝 生成长度: 237 字符
🔍 生成预览: t_data_path('test_data/test_data_2.csv')\n\n    def test_read_csv(self):\n\n        df = read_csv(self._...
💾 实时保存完成 (224/310)

📝 处理样本 225/310
🗂️ 文件: src/nvim/eval/funcs.c
🔧 Commit: d1322264...
📏 Prompt长度: 7661 字符
  ⚠️ Prompt过长(7661字符)，尝试截断...
  📏 截断后长度: 4244字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (64.79s)
📝 生成长度: 1760 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (225/310)

📝 处理样本 226/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 2e8cec5f...
📏 Prompt长度: 3582 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.64s)
📝 生成长度: 1498 字符
🔍 生成预览: t_id=None):\n        """\n        Get all the users in the system\n\n        :param tenant_id: Tenant ID...
💾 实时保存完成 (226/310)

📝 处理样本 227/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: f4d3e279...
📏 Prompt长度: 3331 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.12s)
📝 生成长度: 487 字符
🔍 生成预览: t_id=None,\n            name="test-name",\n            description="test-description",\n        )\n\n    ...
💾 实时保存完成 (227/310)

📝 处理样本 228/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 227f06b7...
📏 Prompt长度: 6235 字符
  ⚠️ Prompt过长(6235字符)，尝试截断...
  📏 截断后长度: 3790字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.14s)
📝 生成长度: 728 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=50)),\n            ],\n        ),\n...
💾 实时保存完成 (228/310)

📝 处理样本 229/310
🗂️ 文件: src/nvim/statusline.c
🔧 Commit: 88c3d890...
📏 Prompt长度: 66528 字符
  ⚠️ Prompt过长(66528字符)，尝试截断...
  📏 截断后长度: 22542字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.54s)
💾 实时保存完成 (229/310)

📝 处理样本 230/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 9cf59acf...
📏 Prompt长度: 2192 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.68s)
📝 生成长度: 1565 字符
🔍 生成预览: void eval_diff(const char *const origfile, const char *const newfile, const char *const outfile)\n{\n ...
💾 实时保存完成 (230/310)

📊 进度报告: 230/310 (74.2%)
✅ 成功: 199/230 (86.5%)

📝 处理样本 231/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 481c6e6c...
📏 Prompt长度: 2225 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.47s)
📝 生成长度: 1413 字符
🔍 生成预览: list_T *eval_spell_expr(char *badword, char *expr)\n{\n  typval_T save_val;\n  typval_T rettv;\n  list_T...
💾 实时保存完成 (231/310)

📝 处理样本 232/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: cec42e07...
📏 Prompt长度: 1880 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.57s)
📝 生成长度: 1161 字符
🔍 生成预览: void eval_patch(const char *const origfile, const char *const difffile, const char *const outfile)\n{...
💾 实时保存完成 (232/310)

📝 处理样本 233/310
🗂️ 文件: src/nvim/indent.c
🔧 Commit: 5d3ad6fd...
📏 Prompt长度: 3639 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.41s)
📝 生成长度: 701 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (233/310)

📝 处理样本 234/310
🗂️ 文件: src/nvim/textformat.c
🔧 Commit: 36f1e982...
📏 Prompt长度: 1997 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.34s)
📝 生成长度: 1290 字符
🔍 生成预览: int fex_format(linenr_T lnum, long count, int c)\n{\n  int use_sandbox = was_set_insecurely(curwin, "f...
💾 实时保存完成 (234/310)

📝 处理样本 235/310
🗂️ 文件: src/nvim/path.c
🔧 Commit: 96451e7f...
📏 Prompt长度: 1380 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.80s)
📝 生成长度: 1316 字符
🔍 生成预览: static char *eval_includeexpr(const char *const ptr, const size_t len)\n{\n  set_vim_var_string(VV_FNA...
💾 实时保存完成 (235/310)

📝 处理样本 236/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: ba575666...
📏 Prompt长度: 1873 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.85s)
📝 生成长度: 2316 字符
🔍 生成预览: void eval_diff(const char *const origfile, const char *const newfile, const char *const outfile)\n{\n ...
💾 实时保存完成 (236/310)

📝 处理样本 237/310
🗂️ 文件: src/nvim/ex_getln.c
🔧 Commit: 9e5f9c25...
📏 Prompt长度: 1529 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.28s)
📝 生成长度: 701 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (237/310)

📝 处理样本 238/310
🗂️ 文件: src/nvim/statusline.c
🔧 Commit: 7095f8ff...
📏 Prompt长度: 11471 字符
  ⚠️ Prompt过长(11471字符)，尝试截断...
  📏 截断后长度: 5559字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.77s)
📝 生成长度: 5597 字符
🔍 生成预览: e_path)\n        self._file_path = file_path\n\n    def __repr__(self):\n        return f"<File {self._f...
💾 实时保存完成 (238/310)

📝 处理样本 239/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: c0f10d3f...
📏 Prompt长度: 7248 字符
  ⚠️ Prompt过长(7248字符)，尝试截断...
  📏 截断后长度: 3925字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.83s)
📝 生成长度: 564 字符
🔍 生成预览: t_id,\n            "name": self._name,\n        }\n\n    def __repr__(self):\n        """Return string re...
💾 实时保存完成 (239/310)

📝 处理样本 240/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 5b77dde8...
📏 Prompt长度: 7362 字符
  ⚠️ Prompt过长(7362字符)，尝试截断...
  📏 截断后长度: 3949字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.68s)
📝 生成长度: 780 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (240/310)

📊 进度报告: 240/310 (77.4%)
✅ 成功: 209/240 (87.1%)

📝 处理样本 241/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 187ba3ef...
📏 Prompt长度: 7401 字符
  ⚠️ Prompt过长(7401字符)，尝试截断...
  📏 截断后长度: 3962字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.60s)
📝 生成长度: 289 字符
🔍 生成预览: s.path.join(os.getcwd(), "data", "test_data")\n        self._test_dir = os.path.join(self._root_dir, ...
💾 实时保存完成 (241/310)

📝 处理样本 242/310
🗂️ 文件: src/nvim/ex_cmds.c
🔧 Commit: 2a94dcf0...
📏 Prompt长度: 7385 字符
  ⚠️ Prompt过长(7385字符)，尝试截断...
  📏 截断后长度: 3922字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.95s)
📝 生成长度: 143 字符
🔍 生成预览: self._set_attr("name", name)\n        self._set_attr("value", value)\n\n    def _get_control_type(self)...
💾 实时保存完成 (242/310)

📝 处理样本 243/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: 8e0ad6e2...
📏 Prompt长度: 2142 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.86s)
📝 生成长度: 8787 字符
🔍 生成预览: e_id=None, **kwargs):\n        """\n        :param str resource_name: The name of the resource.\n      ...
💾 实时保存完成 (243/310)

📝 处理样本 244/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: 8bed0735...
📏 Prompt长度: 174005 字符
  ⚠️ Prompt过长(174005字符)，尝试截断...
  📏 截断后长度: 60264字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.80s)
💾 实时保存完成 (244/310)

📝 处理样本 245/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: 0d7bed34...
📏 Prompt长度: 1315 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.56s)
📝 生成长度: 1005 字符
🔍 生成预览: void invoke_all_defer(void)\n{\n  for (funccall_T *fc = current_funccal; fc!= NULL; fc = fc->fc_caller...
💾 实时保存完成 (245/310)

📝 处理样本 246/310
🗂️ 文件: src/nvim/arglist.c
🔧 Commit: f1b415b3...
📏 Prompt长度: 5092 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.24s)
📝 生成长度: 352 字符
🔍 生成预览: s.path.join(os.getcwd(), 'data', 'test_data')\n        self.assertTrue(os.path.exists(self.test_dir))...
💾 实时保存完成 (246/310)

📝 处理样本 247/310
🗂️ 文件: src/nvim/memline.c
🔧 Commit: 95839a23...
📏 Prompt长度: 42447 字符
  ⚠️ Prompt过长(42447字符)，尝试截断...
  📏 截断后长度: 15525字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.45s)
💾 实时保存完成 (247/310)

📝 处理样本 248/310
🗂️ 文件: src/nvim/undo.c
🔧 Commit: 715587f8...
📏 Prompt长度: 14596 字符
  ⚠️ Prompt过长(14596字符)，尝试截断...
  📏 截断后长度: 5839字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (119.00s)
📝 生成长度: 8383 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (248/310)

📝 处理样本 249/310
🗂️ 文件: src/nvim/eval/vars.c
🔧 Commit: 2eb1f62e...
📏 Prompt长度: 7039 字符
  ⚠️ Prompt过长(7039字符)，尝试截断...
  📏 截断后长度: 4454字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.89s)
📝 生成长度: 1406 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (249/310)

📝 处理样本 250/310
🗂️ 文件: src/nvim/buffer.c
🔧 Commit: 65fdd019...
📏 Prompt长度: 2705 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.69s)
📝 生成长度: 1568 字符
🔍 生成预览: void get_rel_pos(win_T *wp, char *buf, int buflen)\n{\n  if (buflen < 3) {\n    return;\n  }\n  long abov...
💾 实时保存完成 (250/310)

📊 进度报告: 250/310 (80.6%)
✅ 成功: 217/250 (86.8%)

📝 处理样本 251/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: 1757ffad...
📏 Prompt长度: 6864 字符
  ⚠️ Prompt过长(6864字符)，尝试截断...
  📏 截断后长度: 3701字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.13s)
📝 生成长度: 1309 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (251/310)

📝 处理样本 252/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: 7dc2c087...
📏 Prompt长度: 6770 字符
  ⚠️ Prompt过长(6770字符)，尝试截断...
  📏 截断后长度: 3672字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.25s)
📝 生成长度: 1010 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (252/310)

📝 处理样本 253/310
🗂️ 文件: src/nvim/normal.c
🔧 Commit: e51e63c9...
📏 Prompt长度: 3411 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.40s)
📝 生成长度: 1513 字符
🔍 生成预览: void scroll_redraw(int up, long count)\n{\n  linenr_T prev_topline = curwin->w_topline;\n  int prev_ski...
💾 实时保存完成 (253/310)

📝 处理样本 254/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: 34a4f372...
📏 Prompt长度: 7118 字符
  ⚠️ Prompt过长(7118字符)，尝试截断...
  📏 截断后长度: 3706字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.32s)
📝 生成长度: 352 字符
🔍 生成预览: t_name = "test"\n\n    def test_create_user(self):\n        user = UserFactory()\n\n        self.assertEq...
💾 实时保存完成 (254/310)

📝 处理样本 255/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 3a1973de...
📏 Prompt长度: 12797 字符
  ⚠️ Prompt过长(12797字符)，尝试截断...
  📏 截断后长度: 5611字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.41s)
📝 生成长度: 1478 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (255/310)

📝 处理样本 256/310
🗂️ 文件: src/nvim/grid.c
🔧 Commit: 2918720a...
📏 Prompt长度: 5624 字符
  🔄 尝试1/5...
  ⚠️ 尝试1: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试2/5...
  ⚠️ 尝试2: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试3/5...
  ⚠️ 尝试3: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试4/5...
  ⚠️ 尝试4: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  🔄 尝试5/5...
  ⚠️ 尝试5: 连接错误 - ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))
  ❌ 所有重试失败
❌ 生成失败 (10.04s)
💾 实时保存完成 (256/310)

📝 处理样本 257/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 9128fc79...
📏 Prompt长度: 7777 字符
  ⚠️ Prompt过长(7777字符)，尝试截断...
  📏 截断后长度: 4477字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.37s)
📝 生成长度: 777 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=256)),\n            ],\n        ),...
💾 实时保存完成 (257/310)

📝 处理样本 258/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 69af5e87...
📏 Prompt长度: 9193 字符
  ⚠️ Prompt过长(9193字符)，尝试截断...
  📏 截断后长度: 4407字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.17s)
📝 生成长度: 591 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (258/310)

📝 处理样本 259/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: 7cc3062e...
📏 Prompt长度: 3920 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.16s)
📝 生成长度: 367 字符
🔍 生成预览: t_name='user',\n            name='is_active',\n        ),\n        migrations.AddField(\n            mod...
💾 实时保存完成 (259/310)

📝 处理样本 260/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 35ed79a9...
📏 Prompt长度: 991 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.72s)
📝 生成长度: 988 字符
🔍 生成预览: static int scrolling_screenlines(bool byfold)\n{\n  return (curwin->w_p_wrap && curwin->w_p_sms)\n     ...
💾 实时保存完成 (260/310)

📊 进度报告: 260/310 (83.9%)
✅ 成功: 226/260 (86.9%)

📝 处理样本 261/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: c25fd85c...
📏 Prompt长度: 7416 字符
  ⚠️ Prompt过长(7416字符)，尝试截断...
  📏 截断后长度: 4363字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.62s)
📝 生成长度: 1125 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (261/310)

📝 处理样本 262/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 223c7173...
📏 Prompt长度: 18163 字符
  ⚠️ Prompt过长(18163字符)，尝试截断...
  📏 截断后长度: 7402字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.88s)
📝 生成长度: 745 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=50)),\n            ],\n        ),\n...
💾 实时保存完成 (262/310)

📝 处理样本 263/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 46646a9b...
📏 Prompt长度: 14942 字符
  ⚠️ Prompt过长(14942字符)，尝试截断...
  📏 截断后长度: 6127字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.48s)
📝 生成长度: 404 字符
🔍 生成预览: t_name='test', last_name='user')\n\n        self.assertEqual(str(user), user.email)\n\n    def test_crea...
💾 实时保存完成 (263/310)

📝 处理样本 264/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: e8dfff5f...
📏 Prompt长度: 17719 字符
  ⚠️ Prompt过长(17719字符)，尝试截断...
  📏 截断后长度: 7364字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.93s)
📝 生成长度: 143 字符
🔍 生成预览: t_name,\n            'last_name': self.last_name\n        }\n\n    def __repr__(self):\n        return '<...
💾 实时保存完成 (264/310)

📝 处理样本 265/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 6fd7e3be...
📏 Prompt长度: 4448 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.95s)
📝 生成长度: 149 字符
🔍 生成预览: t_name,\n            'last_name': self.last_name\n        }\n\n    def __repr__(self):\n        return '<...
💾 实时保存完成 (265/310)

📝 处理样本 266/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 4e4383ff...
📏 Prompt长度: 4645 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.73s)
📝 生成长度: 1407 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (266/310)

📝 处理样本 267/310
🗂️ 文件: src/nvim/mouse.c
🔧 Commit: 5ba11087...
📏 Prompt长度: 5299 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.45s)
📝 生成长度: 3191 字符
🔍 生成预览: bool mouse_comp_pos(win_T *win, int *rowp, int *colp, linenr_T *lnump)\n{\n  int col = *colp;\n  int ro...
💾 实时保存完成 (267/310)

📝 处理样本 268/310
🗂️ 文件: src/nvim/grid.c
🔧 Commit: 72c525d5...
📏 Prompt长度: 6361 字符
  ⚠️ Prompt过长(6361字符)，尝试截断...
  📏 截断后长度: 3096字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.33s)
📝 生成长度: 2269 字符
🔍 生成预览: void grid_put_linebuf(ScreenGrid *grid, int row, int coloff, int endcol, int clear_width,\n          ...
💾 实时保存完成 (268/310)

📝 处理样本 269/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 7d5673b1...
📏 Prompt长度: 10927 字符
  ⚠️ Prompt过长(10927字符)，尝试截断...
  📏 截断后长度: 4913字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.33s)
📝 生成长度: 1451 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (269/310)

📝 处理样本 270/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: a2f38552...
📏 Prompt长度: 18470 字符
  ⚠️ Prompt过长(18470字符)，尝试截断...
  📏 截断后长度: 7553字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.01s)
📝 生成长度: 1762 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (270/310)

📊 进度报告: 270/310 (87.1%)
✅ 成功: 236/270 (87.4%)

📝 处理样本 271/310
🗂️ 文件: src/nvim/spellfile.c
🔧 Commit: 7ddf235d...
📏 Prompt长度: 4893 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.59s)
📝 生成长度: 559 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (271/310)

📝 处理样本 272/310
🗂️ 文件: src/nvim/eval/typval.c
🔧 Commit: 47132823...
📏 Prompt长度: 3071 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.38s)
📝 生成长度: 4290 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (272/310)

📝 处理样本 273/310
🗂️ 文件: src/nvim/strings.c
🔧 Commit: 3724e65c...
📏 Prompt长度: 3975 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.63s)
📝 生成长度: 2849 字符
🔍 生成预览: self._check_for_errors()\n\n    def _check_for_errors(self):\n        """Check for errors in the respon...
💾 实时保存完成 (273/310)

📝 处理样本 274/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: d79e7262...
📏 Prompt长度: 2647 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.43s)
📝 生成长度: 1425 字符
🔍 生成预览: static void ga_concat_shorten_esc(garray_T *gap, const char *str)\n  FUNC_ATTR_NONNULL_ARG(1)\n{\n  cha...
💾 实时保存完成 (274/310)

📝 处理样本 275/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: 49a2bb91...
📏 Prompt长度: 13994 字符
  ⚠️ Prompt过长(13994字符)，尝试截断...
  📏 截断后长度: 6217字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.83s)
📝 生成长度: 1432 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (275/310)

📝 处理样本 276/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: 17c8e39f...
📏 Prompt长度: 13311 字符
  ⚠️ Prompt过长(13311字符)，尝试截断...
  📏 截断后长度: 5942字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.23s)
📝 生成长度: 3066 字符
🔍 生成预览: t_name = 'test'\n        self._test_user = UserFactory()\n\n    def test_create_user_with_email_success...
💾 实时保存完成 (276/310)

📝 处理样本 277/310
🗂️ 文件: src/nvim/testing.c
🔧 Commit: 5fb6b343...
📏 Prompt长度: 13631 字符
  ⚠️ Prompt过长(13631字符)，尝试截断...
  📏 截断后长度: 6026字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.37s)
📝 生成长度: 960 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (277/310)

📝 处理样本 278/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: aa5f3a79...
📏 Prompt长度: 2464 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.78s)
📝 生成长度: 252 字符
🔍 生成预览: t_name = 'test'\n\n    def test_get_user_by_username(self):\n        user = User.objects.create_user('t...
💾 实时保存完成 (278/310)

📝 处理样本 279/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: ad7f9a70...
📏 Prompt长度: 2574 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.71s)
📝 生成长度: 8342 字符
🔍 生成预览: self._set_attribute(self._SDM_ATT_MAP['EnableVlanTagging'], value)\n\n    @property\n    def EnableVlan...
💾 实时保存完成 (279/310)

📝 处理样本 280/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: 6b912dec...
📏 Prompt长度: 3996 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.26s)
📝 生成长度: 1124 字符
🔍 生成预览: int eval0(char *arg, typval_T *rettv, exarg_T *eap, evalarg_T *const evalarg)\n{\n  int ret;\n  char *p...
💾 实时保存完成 (280/310)

📊 进度报告: 280/310 (90.3%)
✅ 成功: 246/280 (87.9%)

📝 处理样本 281/310
🗂️ 文件: src/nvim/getchar.c
🔧 Commit: f7c1e460...
📏 Prompt长度: 4664 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.20s)
📝 生成长度: 156 字符
🔍 生成预览: self._set_value("max_length", value)\n\n    @property\n    def min_length(self):\n        """Gets the mi...
💾 实时保存完成 (281/310)

📝 处理样本 282/310
🗂️ 文件: src/nvim/eval.c
🔧 Commit: ca344b71...
📏 Prompt长度: 9444 字符
  ⚠️ Prompt过长(9444字符)，尝试截断...
  📏 截断后长度: 4272字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.88s)
📝 生成长度: 7739 字符
🔍 生成预览: t_name = 'test'\n        self._test_email = '<EMAIL>'\n\n    def test_create_user(self):\n        user =...
💾 实时保存完成 (282/310)

📝 处理样本 283/310
🗂️ 文件: src/nvim/eval/userfunc.c
🔧 Commit: 03828536...
📏 Prompt长度: 42055 字符
  ⚠️ Prompt过长(42055字符)，尝试截断...
  📏 截断后长度: 15015字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.40s)
💾 实时保存完成 (283/310)

📝 处理样本 284/310
🗂️ 文件: src/nvim/optionstr.c
🔧 Commit: 7ed4274f...
📏 Prompt长度: 18440 字符
  ⚠️ Prompt过长(18440字符)，尝试截断...
  📏 截断后长度: 6723字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.78s)
📝 生成长度: 1161 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (284/310)

📝 处理样本 285/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 15c684b3...
📏 Prompt长度: 9510 字符
  ⚠️ Prompt过长(9510字符)，尝试截断...
  📏 截断后长度: 4329字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.05s)
📝 生成长度: 445 字符
🔍 生成预览: t_name='test', last_name='user')\n        self.assertEqual(str(user), user.email)\n\n    def test_new_s...
💾 实时保存完成 (285/310)

📝 处理样本 286/310
🗂️ 文件: src/nvim/change.c
🔧 Commit: 6f41eaa2...
📏 Prompt长度: 12592 字符
  ⚠️ Prompt过长(12592字符)，尝试截断...
  📏 截断后长度: 5402字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.16s)
📝 生成长度: 731 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (286/310)

📝 处理样本 287/310
🗂️ 文件: src/nvim/normal.c
🔧 Commit: c8dc3479...
📏 Prompt长度: 1833 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.98s)
📝 生成长度: 1827 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (287/310)

📝 处理样本 288/310
🗂️ 文件: src/nvim/edit.c
🔧 Commit: 41115308...
📏 Prompt长度: 1800 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.03s)
📝 生成长度: 1169 字符
🔍 生成预览: void display_dollar(colnr_T col_arg)\n{\n  colnr_T col = col_arg < 0? 0 : col_arg;\n  colnr_T save_col;...
💾 实时保存完成 (288/310)

📝 处理样本 289/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 02ef104d...
📏 Prompt长度: 13799 字符
  ⚠️ Prompt过长(13799字符)，尝试截断...
  📏 截断后长度: 5908字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.37s)
📝 生成长度: 1020 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (289/310)

📝 处理样本 290/310
🗂️ 文件: src/nvim/plines.c
🔧 Commit: 8eaf3c4f...
📏 Prompt长度: 13778 字符
  ⚠️ Prompt过长(13778字符)，尝试截断...
  📏 截断后长度: 5753字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.82s)
📝 生成长度: 1291 字符
🔍 生成预览: e_name='ID')),\n                ('created', models.DateTimeField(auto_now_add=True)),\n               ...
💾 实时保存完成 (290/310)

📊 进度报告: 290/310 (93.5%)
✅ 成功: 255/290 (87.9%)

📝 处理样本 291/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: 7423d347...
📏 Prompt长度: 173140 字符
  ⚠️ Prompt过长(173140字符)，尝试截断...
  📏 截断后长度: 58670字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.74s)
💾 实时保存完成 (291/310)

📝 处理样本 292/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: e12b5882...
📏 Prompt长度: 180116 字符
  ⚠️ Prompt过长(180116字符)，尝试截断...
  📏 截断后长度: 62683字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.78s)
💾 实时保存完成 (292/310)

📝 处理样本 293/310
🗂️ 文件: src/nvim/buffer.c
🔧 Commit: 6661cdf2...
📏 Prompt长度: 1916 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.37s)
📝 生成长度: 1282 字符
🔍 生成预览: bool append_arg_number(win_T *wp, char *buf, int buflen, bool add_file)\n  FUNC_ATTR_NONNULL_ALL\n{\n  ...
💾 实时保存完成 (293/310)

📝 处理样本 294/310
🗂️ 文件: src/nvim/drawline.c
🔧 Commit: bff67c9f...
📏 Prompt长度: 180302 字符
  ⚠️ Prompt过长(180302字符)，尝试截断...
  📏 截断后长度: 62660字符
  🔄 尝试1/5...
  ⚠️ 尝试1: HTTP 500 (服务器内部错误)
  🔄 尝试2/5...
  ⚠️ 尝试2: HTTP 500 (服务器内部错误)
  🔄 尝试3/5...
  ⚠️ 尝试3: HTTP 500 (服务器内部错误)
  🔄 尝试4/5...
  ⚠️ 尝试4: HTTP 500 (服务器内部错误)
  🔄 尝试5/5...
  ⚠️ 尝试5: HTTP 500 (服务器内部错误)
  ❌ 所有重试失败
❌ 生成失败 (10.80s)
💾 实时保存完成 (294/310)

📝 处理样本 295/310
🗂️ 文件: src/nvim/autocmd.c
🔧 Commit: b46f61ac...
📏 Prompt长度: 8259 字符
  ⚠️ Prompt过长(8259字符)，尝试截断...
  📏 截断后长度: 4329字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.30s)
📝 生成长度: 361 字符
🔍 生成预览: t_name = 'test'\n        self._test_user = UserFactory()\n\n    def test_get_absolute_url(self):\n      ...
💾 实时保存完成 (295/310)

📝 处理样本 296/310
🗂️ 文件: src/nvim/grid.c
🔧 Commit: 53f30de2...
📏 Prompt长度: 6593 字符
  ⚠️ Prompt过长(6593字符)，尝试截断...
  📏 截断后长度: 3261字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (111.17s)
📝 生成长度: 1870 字符
🔍 生成预览: void grid_put_linebuf(ScreenGrid *grid, int row, int coloff, int endcol, int clear_width,\n          ...
💾 实时保存完成 (296/310)

📝 处理样本 297/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 49ede0a6...
📏 Prompt长度: 5120 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.16s)
📝 生成长度: 1126 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret,\n            refresh_token=self....
💾 实时保存完成 (297/310)

📝 处理样本 298/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 9978a999...
📏 Prompt长度: 10879 字符
  ⚠️ Prompt过长(10879字符)，尝试截断...
  📏 截断后长度: 5559字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.34s)
📝 生成长度: 926 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (298/310)

📝 处理样本 299/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: fc908b50...
📏 Prompt长度: 10984 字符
  ⚠️ Prompt过长(10984字符)，尝试截断...
  📏 截断后长度: 5633字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.03s)
📝 生成长度: 752 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (299/310)

📝 处理样本 300/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 9deef1aa...
📏 Prompt长度: 7507 字符
  ⚠️ Prompt过长(7507字符)，尝试截断...
  📏 截断后长度: 4386字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.62s)
📝 生成长度: 759 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=256)),\n                ('descri...
💾 实时保存完成 (300/310)

📊 进度报告: 300/310 (96.8%)
✅ 成功: 262/300 (87.3%)

📝 处理样本 301/310
🗂️ 文件: src/nvim/ops.c
🔧 Commit: 780ab11b...
📏 Prompt长度: 12552 字符
  ⚠️ Prompt过长(12552字符)，尝试截断...
  📏 截断后长度: 6793字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.59s)
📝 生成长度: 808 字符
🔍 生成预览: e_name='ID')),\n                ('name', models.CharField(max_length=50)),\n            ],\n        ),\n...
💾 实时保存完成 (301/310)

📝 处理样本 302/310
🗂️ 文件: src/nvim/spell.c
🔧 Commit: 9d9af4fe...
📏 Prompt长度: 5564 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.22s)
📝 生成长度: 1449 字符
🔍 生成预览: self._set_value("name", value)\n\n    @property\n    def description(self):\n        """\n        Gets an...
💾 实时保存完成 (302/310)

📝 处理样本 303/310
🗂️ 文件: src/nvim/strings.c
🔧 Commit: bbb934e7...
📏 Prompt长度: 4310 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.24s)
📝 生成长度: 952 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (303/310)

📝 处理样本 304/310
🗂️ 文件: src/nvim/cmdexpand.c
🔧 Commit: ddba917e...
📏 Prompt长度: 6786 字符
  ⚠️ Prompt过长(6786字符)，尝试截断...
  📏 截断后长度: 4402字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.37s)
📝 生成长度: 678 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (304/310)

📝 处理样本 305/310
🗂️ 文件: src/nvim/message.c
🔧 Commit: cba07dad...
📏 Prompt长度: 5037 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (115.17s)
📝 生成长度: 678 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n                ('descrip...
💾 实时保存完成 (305/310)

📝 处理样本 306/310
🗂️ 文件: src/nvim/message.c
🔧 Commit: 11060793...
📏 Prompt长度: 5253 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (113.65s)
📝 生成长度: 3128 字符
🔍 生成预览: int msg_outtrans_len_attr(const char *msgstr, int len, int attr)\n{\n  int retval = 0;\n  const char *s...
💾 实时保存完成 (306/310)

📝 处理样本 307/310
🗂️ 文件: src/nvim/fileio.c
🔧 Commit: 4e63104c...
📏 Prompt长度: 2010 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (112.50s)
📝 生成长度: 1790 字符
🔍 生成预览: bool match_file_list(char *list, char *sfname, char *ffname)\n  FUNC_ATTR_WARN_UNUSED_RESULT FUNC_ATT...
💾 实时保存完成 (307/310)

📝 处理样本 308/310
🗂️ 文件: src/nvim/autocmd.c
🔧 Commit: 7966020f...
📏 Prompt长度: 22644 字符
  ⚠️ Prompt过长(22644字符)，尝试截断...
  📏 截断后长度: 8706字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (117.52s)
📝 生成长度: 1390 字符
🔍 生成预览: t_id=self._client_id,\n            client_secret=self._client_secret\n        )\n\n        self._session...
💾 实时保存完成 (308/310)

📝 处理样本 309/310
🗂️ 文件: src/nvim/move.c
🔧 Commit: 3b6fb3fe...
📏 Prompt长度: 16580 字符
  ⚠️ Prompt过长(16580字符)，尝试截断...
  📏 截断后长度: 6664字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (116.32s)
📝 生成长度: 1279 字符
🔍 生成预览: t_name = 'test'\n        self._test_email = '<EMAIL>'\n\n    def test_create_user(self):\n        user =...
💾 实时保存完成 (309/310)

📝 处理样本 310/310
🗂️ 文件: src/nvim/option.c
🔧 Commit: 42f9573e...
📏 Prompt长度: 5514 字符
  🔄 尝试1/5...
  ✅ API调用成功
✅ 生成成功 (114.64s)
📝 生成长度: 1573 字符
🔍 生成预览: e_name='ID')),\n                ('title', models.CharField(max_length=50)),\n            ],\n        ),...
💾 实时保存完成 (310/310)

📊 进度报告: 310/310 (100.0%)
✅ 成功: 272/310 (87.7%)

✅ 预测生成完成
📊 详细结果文件: outputs/out_starcoder_all_data_detailed_result.json
📊 兼容格式文件: outputs/out_starcoder_all_data_result.json
📈 总样本数: 310
🎯 成功生成: 272/310 (87.7%)
⏱️ 平均生成时间: 111.23秒
📏 平均生成长度: 1734字符

📊 开始计算评估指标...
❌ 指标计算失败: Incompatible Language version 15. Must be between 13 and 14

❌ 评估过程中出现错误
