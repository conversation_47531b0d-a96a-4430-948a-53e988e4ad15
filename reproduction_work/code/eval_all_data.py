#!/usr/bin/env python3
"""
PPatHF Evaluation脚本 - 针对vim_neovim_test_all.json数据集
在re-ppathf目录下编写，不修改原仓库代码
"""

import os
import sys
import json
import pandas as pd
import subprocess
import time
from pathlib import Path

# 添加原项目路径到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent / "PPatHF-main"
sys.path.insert(0, str(project_root))

# 从原项目导入配置和函数
from config import API_BASE_URL, API_MODEL_NAME, USE_API_SERVICE, DATA_PATH, OUTPUT_PATH, EXTRACTED_METRIC_PATH, LLM_PATH, LLM_NAME_PATH_MAPPING
from test import cal_metrics, analyze_test_results, align_test_metrics

class PPATHFEvaluator:
    """PPatHF评估器，专门处理vim_neovim_test_all.json数据集"""
    
    def __init__(self):
        self.data_file = "vim_neovim_test_all.json"
        # 修复数据路径
        data_root = Path("/home/<USER>/nas-files/Re-paper/ppathf/data/data")
        self.data_path = str(data_root / self.data_file)
        self.model_name = "starcoder"
        self.max_length = 8192  # 使用最大长度以包含所有数据
        
        # 输出路径
        self.output_dir = Path(OUTPUT_PATH)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建专门的输出文件名
        self.result_file = f"out_{self.model_name}_all_data_result.json"
        self.result_path = str(self.output_dir / self.result_file)
        
        print(f"✅ 初始化完成")
        print(f"📁 数据文件: {self.data_path}")
        print(f"📤 输出路径: {self.result_path}")
        print(f"🔧 API服务: {API_BASE_URL}")
        
    def check_data_availability(self):
        """检查数据文件是否存在"""
        if not os.path.exists(self.data_path):
            print(f"❌ 数据文件不存在: {self.data_path}")
            return False
            
        with open(self.data_path, 'r') as f:
            data = json.load(f)
            
        print(f"✅ 数据文件检查通过")
        print(f"📊 数据总数: {len(data)}")
        print(f"🔑 数据字段: {list(data[0].keys()) if data else '空数据'}")
        return True
        
    def create_patch_prompt(self, sample):
        """创建patch porting的prompt - 使用原始仓库的正确格式"""
        prompt = f"""Below is a patch (including function before and function after) from vim, paired with a corresponding function before from neovim. Adapt the patch from vim to neovim by generating the function after based on the given function before.

### Function Before (vim):
{sample['func_before_source']}

### Function After (vim):
{sample['func_after_source']}

### Function Before (neovim):
{sample['func_before_target']}

### Function After (neovim):
"""
        return prompt
    
    def call_api_with_retry(self, prompt, max_tokens=2048, timeout=120):
        """调用API进行生成，带增强的重试机制"""
        import requests
        import time
        
        # 如果prompt太长，尝试截断
        if len(prompt) > 6000:
            print(f"  ⚠️ Prompt过长({len(prompt)}字符)，尝试截断...")
            # 简化prompt - 保留核心部分
            lines = prompt.split('\n')
            # 找到关键分隔符
            instruction_end = -1
            vim_before_start = -1
            vim_after_start = -1
            neovim_before_start = -1
            
            for i, line in enumerate(lines):
                if "### Function Before (vim):" in line:
                    vim_before_start = i
                elif "### Function After (vim):" in line:
                    vim_after_start = i
                elif "### Function Before (neovim):" in line:
                    neovim_before_start = i
            
            # 截断过长的函数内容
            if vim_before_start != -1 and vim_after_start != -1:
                vim_before_lines = lines[vim_before_start+1:vim_after_start]
                if len('\n'.join(vim_before_lines)) > 1500:
                    lines[vim_before_start+1:vim_after_start] = vim_before_lines[:30] + ["// ... 函数内容截断 ..."] + vim_before_lines[-10:]
            
            if vim_after_start != -1 and neovim_before_start != -1:
                vim_after_lines = lines[vim_after_start+1:neovim_before_start]
                if len('\n'.join(vim_after_lines)) > 1500:
                    lines[vim_after_start+1:neovim_before_start] = vim_after_lines[:30] + ["// ... 函数内容截断 ..."] + vim_after_lines[-10:]
                    
            if neovim_before_start != -1:
                neovim_before_lines = lines[neovim_before_start+1:]
                if len('\n'.join(neovim_before_lines)) > 1500:
                    lines[neovim_before_start+1:] = neovim_before_lines[:30] + ["// ... 函数内容截断 ..."] + neovim_before_lines[-10:]
            
            prompt = '\n'.join(lines)
            print(f"  📏 截断后长度: {len(prompt)}字符")
        
        for attempt in range(5):  # 增加到5次重试
            try:
                print(f"  🔄 尝试{attempt+1}/5...")
                
                payload = {
                    "model": "starcoder",
                    "prompt": prompt,
                    "max_tokens": max_tokens,  # 限制生成长度
                    "temperature": 0.1,
                    "stop": ["### Function", "\n\n\n", "```"]
                }
                
                # 使用更短的超时时间，但多次重试
                response = requests.post(
                    f"{API_BASE_URL}/v1/completions",
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    text = result['choices'][0]['text'] if result['choices'] else ""
                    print(f"  ✅ API调用成功")
                    return True, text
                elif response.status_code == 500:
                    print(f"  ⚠️ 尝试{attempt+1}: HTTP 500 (服务器内部错误)")
                    time.sleep(2)  # 服务器错误时等待更长时间
                else:
                    print(f"  ⚠️ 尝试{attempt+1}: HTTP {response.status_code}")
                    time.sleep(1)
                    
            except requests.exceptions.Timeout:
                print(f"  ⚠️ 尝试{attempt+1}: 超时 (>{timeout}秒)")
                time.sleep(1)
            except requests.exceptions.ConnectionError as e:
                print(f"  ⚠️ 尝试{attempt+1}: 连接错误 - {e}")
                time.sleep(2)
            except KeyboardInterrupt:
                print(f"  ❌ 用户中断")
                return False, "USER_INTERRUPTED"
            except Exception as e:
                print(f"  ⚠️ 尝试{attempt+1}: 未知错误 - {e}")
                time.sleep(1)
        
        print(f"  ❌ 所有重试失败")
        return False, ""
    
    def run_prediction(self):
        """运行LLM预测生成 - 直接调用API，绕过predict.py的tokenizer依赖"""
        print(f"\n🚀 开始运行预测生成...")
        print(f"🔧 使用直接API调用模式，绕过predict.py")
        
        # 加载数据
        try:
            with open(self.data_path, 'r') as f:
                data = json.load(f)
            print(f"📊 加载数据: {len(data)} 个样本")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
        
        # 详细结果列表 - 包含完整信息
        detailed_results = []
        # 简单结果列表 - 兼容原始predict.py格式  
        simple_results = []
        successful_count = 0
        
        # 创建详细结果文件路径
        detailed_result_path = self.result_path.replace("_result.json", "_detailed_result.json")
        
        print(f"🔄 开始API调用生成...")
        print(f"💾 详细结果将保存到: {detailed_result_path}")
        print(f"💾 简单结果将保存到: {self.result_path}")
        
        for i, sample in enumerate(data):
            print(f"\n📝 处理样本 {i+1}/{len(data)}")
            print(f"🗂️ 文件: {sample['file_path_target']}")
            print(f"🔧 Commit: {sample['commit_id_target'][:8]}...")
            
            try:
                # 创建prompt
                prompt = self.create_patch_prompt(sample)
                prompt_length = len(prompt)
                print(f"📏 Prompt长度: {prompt_length} 字符")
                
                # 调用API
                start_time = time.time()
                try:
                    success, generated_text = self.call_api_with_retry(prompt)
                    end_time = time.time()
                    generation_time = end_time - start_time
                    
                    if generated_text == "USER_INTERRUPTED":
                        print(f"❌ 用户中断，停止处理")
                        break
                    
                    if success:
                        print(f"✅ 生成成功 ({generation_time:.2f}s)")
                        print(f"📝 生成长度: {len(generated_text)} 字符")
                        successful_count += 1
                        
                        # 显示生成预览
                        preview = generated_text[:100].replace('\n', '\\n')
                        print(f"🔍 生成预览: {preview}...")
                    else:
                        print(f"❌ 生成失败 ({generation_time:.2f}s)")
                        generated_text = ""
                        
                except KeyboardInterrupt:
                    print(f"❌ 检测到用户中断，正在保存当前进度...")
                    generated_text = ""
                    generation_time = 0.0
                    success = False
                    # 继续保存当前结果，然后退出
                except Exception as api_error:
                    print(f"❌ API调用异常: {api_error}")
                    generated_text = ""
                    generation_time = 0.0
                    success = False
                
                # 创建详细结果记录
                detailed_result = {
                    "sample_index": i,
                    "commit_id_source": sample['commit_id_source'],
                    "commit_id_target": sample['commit_id_target'],
                    "file_path_source": sample['file_path_source'],
                    "file_path_target": sample['file_path_target'],
                    "func_before_source": sample['func_before_source'],
                    "func_after_source": sample['func_after_source'],
                    "func_before_target": sample['func_before_target'],
                    "func_after_target": sample['func_after_target'],  # 真实标签
                    "generated_func_after_target": generated_text,  # 生成结果
                    "generation_success": success,
                    "generation_time": generation_time,
                    "prompt_length": prompt_length,
                    "generated_length": len(generated_text),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                # 添加到结果列表
                detailed_results.append(detailed_result)
                simple_results.append(generated_text)  # 兼容原格式
                
                # 🔥 实时保存 - 每处理一个样本就保存一次
                try:
                    # 保存详细结果
                    with open(detailed_result_path, 'w', encoding='utf-8') as f:
                        json.dump(detailed_results, f, indent=2, ensure_ascii=False)
                    
                    # 保存简单结果（兼容原格式）
                    with open(self.result_path, 'w', encoding='utf-8') as f:
                        json.dump(simple_results, f, indent=2, ensure_ascii=False)
                    
                    print(f"💾 实时保存完成 ({i+1}/{len(data)})")
                    
                except Exception as save_error:
                    print(f"⚠️ 实时保存失败: {save_error}")
                
                # 进度报告
                if (i + 1) % 10 == 0:
                    print(f"\n📊 进度报告: {i+1}/{len(data)} ({(i+1)/len(data)*100:.1f}%)")
                    print(f"✅ 成功: {successful_count}/{i+1} ({successful_count/(i+1)*100:.1f}%)")
                
                # 添加延迟避免API压力
                time.sleep(1)
                
            except KeyboardInterrupt:
                print(f"❌ 用户中断程序，正在保存当前进度...")
                # 创建中断记录
                interrupt_result = {
                    "sample_index": i,
                    "commit_id_source": sample.get('commit_id_source', 'unknown'),
                    "commit_id_target": sample.get('commit_id_target', 'unknown'),
                    "file_path_source": sample.get('file_path_source', 'unknown'),
                    "file_path_target": sample.get('file_path_target', 'unknown'),
                    "func_before_source": sample.get('func_before_source', ''),
                    "func_after_source": sample.get('func_after_source', ''),
                    "func_before_target": sample.get('func_before_target', ''),
                    "func_after_target": sample.get('func_after_target', ''),
                    "generated_func_after_target": "",
                    "generation_success": False,
                    "generation_time": 0.0,
                    "prompt_length": 0,
                    "generated_length": 0,
                    "error_message": "USER_INTERRUPTED",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                detailed_results.append(interrupt_result)
                simple_results.append("")
                
                # 保存中断前的结果
                try:
                    with open(detailed_result_path, 'w', encoding='utf-8') as f:
                        json.dump(detailed_results, f, indent=2, ensure_ascii=False)
                    with open(self.result_path, 'w', encoding='utf-8') as f:
                        json.dump(simple_results, f, indent=2, ensure_ascii=False)
                    print(f"💾 已保存到样本 {i+1} 的进度")
                except:
                    print(f"⚠️ 保存失败，但程序将退出")
                
                break  # 退出循环
                
            except Exception as e:
                print(f"❌ 处理样本 {i+1} 时出错: {e}")
                
                # 即使出错也要记录
                error_result = {
                    "sample_index": i,
                    "commit_id_source": sample.get('commit_id_source', 'unknown'),
                    "commit_id_target": sample.get('commit_id_target', 'unknown'),
                    "file_path_source": sample.get('file_path_source', 'unknown'),
                    "file_path_target": sample.get('file_path_target', 'unknown'),
                    "func_before_source": sample.get('func_before_source', ''),
                    "func_after_source": sample.get('func_after_source', ''),
                    "func_before_target": sample.get('func_before_target', ''),
                    "func_after_target": sample.get('func_after_target', ''),
                    "generated_func_after_target": "",
                    "generation_success": False,
                    "generation_time": 0.0,
                    "prompt_length": 0,
                    "generated_length": 0,
                    "error_message": str(e),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                detailed_results.append(error_result)
                simple_results.append("")  # 空结果占位
                
                # 保存错误结果
                try:
                    with open(detailed_result_path, 'w', encoding='utf-8') as f:
                        json.dump(detailed_results, f, indent=2, ensure_ascii=False)
                    with open(self.result_path, 'w', encoding='utf-8') as f:
                        json.dump(simple_results, f, indent=2, ensure_ascii=False)
                    print(f"💾 错误记录已保存")
                except Exception as save_error:
                    print(f"⚠️ 保存错误结果失败: {save_error}")
                
                # 继续处理下一个样本，不退出
        
        # 最终总结
        try:
            print(f"\n✅ 预测生成完成")
            print(f"📊 详细结果文件: {detailed_result_path}")
            print(f"📊 兼容格式文件: {self.result_path}")
            print(f"📈 总样本数: {len(detailed_results)}")
            print(f"🎯 成功生成: {successful_count}/{len(detailed_results)} ({successful_count/len(detailed_results)*100:.1f}%)")
            
            # 生成简单统计报告
            if successful_count > 0:
                avg_time = sum(r['generation_time'] for r in detailed_results if r['generation_success']) / successful_count
                avg_length = sum(r['generated_length'] for r in detailed_results if r['generation_success']) / successful_count
                print(f"⏱️ 平均生成时间: {avg_time:.2f}秒")
                print(f"📏 平均生成长度: {avg_length:.0f}字符")
            
            return True
            
        except Exception as e:
            print(f"❌ 最终总结失败: {e}")
            return False
    
    def calculate_metrics(self):
        """计算评估指标"""
        print(f"\n📊 开始计算评估指标...")
        
        try:
            # 使用原项目的指标计算函数
            cal_metrics(
                data_path=self.data_path,
                output_path=self.result_path,
                do_recover=False,  # 不使用sliced数据
                reference_key="func_after_target",
                reference_before_key="func_before_target", 
                save_sample_wise_results=True
            )
            
            print(f"✅ 基础指标计算完成")
            
            # 分析测试结果
            processed_path = self.result_path.replace("result.json", "result_processed.json")
            analyze_test_results(
                result_path=processed_path,
                save_analyzed_results=True
            )
            
            print(f"✅ 详细分析完成")
            print(f"📋 处理后结果: {processed_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 指标计算失败: {e}")
            return False
    
    def generate_report(self):
        """生成评估报告"""
        print(f"\n📋 生成评估报告...")
        
        processed_path = self.result_path.replace("result.json", "result_processed_analyzed.json")
        
        if not os.path.exists(processed_path):
            print(f"❌ 处理结果文件不存在: {processed_path}")
            return False
            
        try:
            # 读取结果
            with open(processed_path) as f:
                results = json.load(f)
            
            df = pd.DataFrame(results)
            
            # 基本统计
            total_samples = len(df)
            exact_matches = df['exact_match'].sum() if 'exact_match' in df.columns else 0
            
            # 生成报告
            report = {
                "evaluation_summary": {
                    "dataset": self.data_file,
                    "total_samples": total_samples,
                    "exact_matches": int(exact_matches),
                    "accuracy": float(exact_matches / total_samples) if total_samples > 0 else 0.0,
                    "model": self.model_name,
                    "max_length": self.max_length
                }
            }
            
            # 如果有详细指标，添加到报告中
            if 'rel_distance' in df.columns:
                report["detailed_metrics"] = {
                    "mean_rel_distance": float(df['rel_distance'].mean()),
                    "mean_gen_distance": float(df['gen_distance'].mean()) if 'gen_distance' in df.columns else None,
                    "mean_src_distance": float(df['src_distance'].mean()) if 'src_distance' in df.columns else None
                }
            
            # 保存报告
            report_path = str(self.output_dir / f"evaluation_report_{self.model_name}_all_data.json")
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
                
            print(f"✅ 评估报告生成完成: {report_path}")
            print(f"📊 总样本数: {total_samples}")
            print(f"🎯 精确匹配: {exact_matches} ({exact_matches/total_samples*100:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")
            return False
    
    def run_full_evaluation(self):
        """运行完整的evaluation流程"""
        print("🎯 开始PPatHF完整评估流程")
        print("=" * 50)
        
        # 第1步：检查数据
        if not self.check_data_availability():
            return False
            
        # 第2步：运行预测
        if not self.run_prediction():
            return False
            
        # 第3步：计算指标
        if not self.calculate_metrics():
            return False
            
        # 第4步：生成报告
        if not self.generate_report():
            return False
            
        print("\n🎉 评估流程完成!")
        print("=" * 50)
        return True

def main():
    """主函数"""
    evaluator = PPATHFEvaluator()
    success = evaluator.run_full_evaluation()
    
    if success:
        print("\n✅ 所有评估步骤完成!")
    else:
        print("\n❌ 评估过程中出现错误")
        sys.exit(1)

if __name__ == "__main__":
    main() 