#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API输出结果类别分析脚本

功能：
1. 分析API生成的代码类别
2. 计算各类别的比例
3. 生成详细的统计报告
4. 保存分析结果

作者：AI Assistant
创建时间：2024年7月2日
"""

import json
import re
import os
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import datetime

class CodeCategoryAnalyzer:
    """代码类别分析器
    
    用于分析API生成的代码内容，识别不同类别并计算统计数据
    """
    
    def __init__(self):
        """初始化分析器"""
        self.results = {
            'analysis_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_samples': 0,
            'categories': defaultdict(int),
            'detailed_samples': [],
            'statistics': {},
            'error_analysis': {}
        }
        
    def detect_django_code(self, text: str) -> bool:
        """检测Django代码特征
        
        Args:
            text: 待检测的文本
            
        Returns:
            bool: 是否包含Django代码
        """
        django_patterns = [
            r'from django',
            r'import django',
            r'models\.CharField',
            r'models\.TextField',
            r'models\.AutoField',
            r'models\.ForeignKey',
            r'models\.ManyToManyField',
            r'migrations\.CreateModel',
            r'migrations\.AddField',
            r'django\.db\.models',
            r'django\.utils\.timezone',
            r'# Generated by Django',
            r'class Migration\(',
            r'dependencies\s*=',
            r'operations\s*=',
            r'models\.Model',
            r'admin\.site\.register',
            r'HttpResponse',
            r'render\(',
            r'redirect\(',
        ]
        
        for pattern in django_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False
    
    def detect_c_code(self, text: str) -> bool:
        """检测C代码特征
        
        Args:
            text: 待检测的文本
            
        Returns:
            bool: 是否包含C代码
        """
        c_patterns = [
            r'\bstatic\s+\w+',
            r'\bvoid\s+\w+\s*\(',
            r'\bint\s+\w+\s*\(',
            r'\bchar\s*\*',
            r'\bif\s*\(',
            r'\bfor\s*\(',
            r'\bwhile\s*\(',
            r'#include\s*<',
            r'#ifdef\s+',
            r'#ifndef\s+',
            r'#define\s+',
            r'\w+\s*\*\s*\w+',
            r'return\s+\w+;',
            r'\w+\(\)\s*\{',
            r'typedef\s+',
            r'struct\s+\w+',
            r'NULL',
            r'TRUE|FALSE',
            r'\bmalloc\s*\(',
            r'\bfree\s*\(',
        ]
        
        for pattern in c_patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def detect_python_code(self, text: str) -> bool:
        """检测Python代码特征（非Django）
        
        Args:
            text: 待检测的文本
            
        Returns:
            bool: 是否包含Python代码
        """
        python_patterns = [
            r'def\s+\w+\s*\(',
            r'class\s+\w+\s*\(',
            r'import\s+\w+',
            r'from\s+\w+\s+import',
            r'if\s+__name__\s*==\s*[\'"]__main__[\'"]',
            r'print\s*\(',
            r'self\.',
            r'__init__\s*\(',
            r'\.append\s*\(',
            r'\.format\s*\(',
            r'f[\'"].*\{.*\}',
        ]
        
        for pattern in python_patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def detect_other_languages(self, text: str) -> str:
        """检测其他编程语言
        
        Args:
            text: 待检测的文本
            
        Returns:
            str: 检测到的语言类型，如果没有则返回空字符串
        """
        language_patterns = {
            'JavaScript': [
                r'function\s+\w+\s*\(',
                r'var\s+\w+\s*=',
                r'let\s+\w+\s*=',
                r'const\s+\w+\s*=',
                r'console\.log\s*\(',
                r'\.getElementById\s*\(',
            ],
            'Java': [
                r'public\s+class\s+\w+',
                r'public\s+static\s+void\s+main',
                r'System\.out\.println',
                r'@Override',
                r'extends\s+\w+',
                r'implements\s+\w+',
            ],
            'HTML': [
                r'<html\b',
                r'<head\b',
                r'<body\b',
                r'<div\b',
                r'<p\b',
                r'</\w+>',
            ],
            'CSS': [
                r'\w+\s*\{[^}]*\}',
                r'color\s*:',
                r'background\s*:',
                r'margin\s*:',
                r'padding\s*:',
            ],
            'SQL': [
                r'SELECT\s+',
                r'FROM\s+\w+',
                r'WHERE\s+',
                r'INSERT\s+INTO',
                r'UPDATE\s+\w+',
                r'DELETE\s+FROM',
            ],
            'Shell': [
                r'#!/bin/bash',
                r'#!/bin/sh',
                r'\$\w+',
                r'echo\s+',
                r'export\s+\w+',
            ]
        }
        
        for lang, patterns in language_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    return lang
        return ''
    
    def categorize_sample(self, sample_text: str) -> Dict[str, Any]:
        """对单个样本进行分类
        
        Args:
            sample_text: 样本文本
            
        Returns:
            Dict: 分类结果
        """
        if not sample_text or sample_text.strip() == '':
            return {
                'category': 'Empty',
                'subcategory': 'No Content',
                'confidence': 1.0,
                'features': ['empty_content'],
                'length': 0
            }
        
        text_length = len(sample_text)
        has_django = self.detect_django_code(sample_text)
        has_c = self.detect_c_code(sample_text)
        has_python = self.detect_python_code(sample_text)
        other_lang = self.detect_other_languages(sample_text)
        
        features = []
        if has_django:
            features.append('django')
        if has_c:
            features.append('c_code')
        if has_python and not has_django:
            features.append('python_non_django')
        if other_lang:
            features.append(f'{other_lang.lower()}_code')
        
        # 分类逻辑
        if has_django and has_c:
            category = 'Mixed'
            subcategory = 'Django+C'
            confidence = 0.9
        elif has_django and not has_c:
            category = 'Django Only'
            subcategory = 'Pure Django'
            confidence = 0.95
        elif has_c and not has_django:
            category = 'C Only'
            subcategory = 'Pure C'
            confidence = 0.95
        elif has_python and not has_django:
            category = 'Python Only'
            subcategory = 'Pure Python'
            confidence = 0.8
        elif other_lang:
            category = f'{other_lang} Only'
            subcategory = f'Pure {other_lang}'
            confidence = 0.8
        else:
            # 文本分析兜底
            if len(sample_text.strip()) < 50:
                category = 'Short Content'
                subcategory = 'Insufficient Text'
                confidence = 0.7
            else:
                category = 'Unknown'
                subcategory = 'Unrecognized Format'
                confidence = 0.5
        
        return {
            'category': category,
            'subcategory': subcategory,
            'confidence': confidence,
            'features': features,
            'length': text_length,
            'has_django': has_django,
            'has_c': has_c,
            'has_python': has_python,
            'other_language': other_lang
        }
    
    def analyze_detailed_result_file(self, file_path: str) -> None:
        """分析详细结果文件
        
        Args:
            file_path: 详细结果文件路径
        """
        print(f"正在分析文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                print(f"警告: {file_path} 格式不正确，跳过分析")
                return
            
            for i, item in enumerate(data):
                if isinstance(item, dict) and 'generated_func_after_target' in item:
                    generated_code = item.get('generated_func_after_target', '')
                    analysis_result = self.categorize_sample(generated_code)
                    
                    # 添加样本信息
                    sample_info = {
                        'sample_id': i,
                        'source_file': file_path,
                        'function_name': item.get('func_before_target', '')[:100] + '...' if len(item.get('func_before_target', '')) > 100 else item.get('func_before_target', ''),
                        'generated_code_preview': generated_code[:200] + '...' if len(generated_code) > 200 else generated_code,
                        'generation_time': item.get('generation_time', 0),
                        **analysis_result
                    }
                    
                    self.results['detailed_samples'].append(sample_info)
                    self.results['categories'][analysis_result['category']] += 1
                    self.results['total_samples'] += 1
                    
                elif isinstance(item, str):
                    # 处理简化结果格式
                    analysis_result = self.categorize_sample(item)
                    
                    sample_info = {
                        'sample_id': i,
                        'source_file': file_path,
                        'function_name': 'N/A',
                        'generated_code_preview': item[:200] + '...' if len(item) > 200 else item,
                        'generation_time': 0,
                        **analysis_result
                    }
                    
                    self.results['detailed_samples'].append(sample_info)
                    self.results['categories'][analysis_result['category']] += 1
                    self.results['total_samples'] += 1
                    
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")
            self.results['error_analysis'][file_path] = str(e)
    
    def analyze_all_result_files(self, base_dir: str) -> None:
        """分析所有结果文件
        
        Args:
            base_dir: 基础目录路径
        """
        result_files = [
            'outputs/out_starcoder_all_data_detailed_result.json',
            'outputs/out_starcoder_all_data_result.json',
            'outputs/out_starcoder_short_8192_detailed_result.json',
            '../../../outputs/api_inference_results.json',
            '../../../outputs/streaming_eval_result_20.json',
            '../../../outputs/simple_api_test_results_3.json'
        ]
        
        for file_path in result_files:
            full_path = os.path.join(base_dir, file_path)
            if os.path.exists(full_path):
                self.analyze_detailed_result_file(full_path)
            else:
                print(f"文件不存在: {full_path}")
    
    def calculate_statistics(self) -> None:
        """计算统计数据"""
        if self.results['total_samples'] == 0:
            print("警告: 没有找到任何样本数据")
            return
        
        # 基本统计
        stats = {
            'total_samples': self.results['total_samples'],
            'category_counts': dict(self.results['categories']),
            'category_percentages': {},
            'length_statistics': {},
            'confidence_statistics': {},
            'generation_time_statistics': {}
        }
        
        # 计算百分比
        for category, count in self.results['categories'].items():
            percentage = (count / self.results['total_samples']) * 100
            stats['category_percentages'][category] = round(percentage, 2)
        
        # 长度统计
        lengths = [sample['length'] for sample in self.results['detailed_samples']]
        if lengths:
            stats['length_statistics'] = {
                'min': min(lengths),
                'max': max(lengths),
                'avg': round(sum(lengths) / len(lengths), 2),
                'median': sorted(lengths)[len(lengths) // 2]
            }
        
        # 置信度统计
        confidences = [sample['confidence'] for sample in self.results['detailed_samples']]
        if confidences:
            stats['confidence_statistics'] = {
                'min': min(confidences),
                'max': max(confidences),
                'avg': round(sum(confidences) / len(confidences), 2)
            }
        
        # 生成时间统计
        gen_times = [sample['generation_time'] for sample in self.results['detailed_samples'] if sample['generation_time'] > 0]
        if gen_times:
            stats['generation_time_statistics'] = {
                'min': min(gen_times),
                'max': max(gen_times),
                'avg': round(sum(gen_times) / len(gen_times), 2),
                'samples_with_time': len(gen_times)
            }
        
        self.results['statistics'] = stats
    
    def generate_detailed_report(self) -> str:
        """生成详细报告
        
        Returns:
            str: 详细报告文本
        """
        report_lines = []
        report_lines.append("# API输出结果类别分析报告")
        report_lines.append(f"分析时间: {self.results['analysis_time']}")
        report_lines.append("")
        
        # 总体统计
        stats = self.results['statistics']
        report_lines.append("## 总体统计")
        report_lines.append(f"- 总样本数: {stats['total_samples']}")
        report_lines.append("")
        
        # 类别分布
        report_lines.append("## 类别分布")
        for category in sorted(stats['category_counts'].keys()):
            count = stats['category_counts'][category]
            percentage = stats['category_percentages'][category]
            report_lines.append(f"- **{category}**: {count}个 ({percentage}%)")
        report_lines.append("")
        
        # 重点问题分析
        report_lines.append("## 🚨 重点问题分析")
        django_count = stats['category_counts'].get('Django Only', 0) + stats['category_counts'].get('Mixed', 0)
        if django_count > 0:
            django_percentage = (django_count / stats['total_samples']) * 100
            report_lines.append(f"- **Django代码污染**: {django_count}个样本包含Django代码 ({django_percentage:.1f}%)")
        
        c_count = stats['category_counts'].get('C Only', 0)
        if c_count > 0:
            c_percentage = (c_count / stats['total_samples']) * 100
            report_lines.append(f"- **正确C代码**: {c_count}个样本包含正确的C代码 ({c_percentage:.1f}%)")
        
        mixed_count = stats['category_counts'].get('Mixed', 0)
        if mixed_count > 0:
            mixed_percentage = (mixed_count / stats['total_samples']) * 100
            report_lines.append(f"- **混合输出**: {mixed_count}个样本包含混合代码 ({mixed_percentage:.1f}%)")
        
        empty_count = stats['category_counts'].get('Empty', 0)
        if empty_count > 0:
            empty_percentage = (empty_count / stats['total_samples']) * 100
            report_lines.append(f"- **空输出**: {empty_count}个样本为空 ({empty_percentage:.1f}%)")
        
        report_lines.append("")
        
        # 长度统计
        if 'length_statistics' in stats and stats['length_statistics']:
            length_stats = stats['length_statistics']
            report_lines.append("## 代码长度统计")
            report_lines.append(f"- 最短: {length_stats['min']} 字符")
            report_lines.append(f"- 最长: {length_stats['max']} 字符")
            report_lines.append(f"- 平均: {length_stats['avg']} 字符")
            report_lines.append(f"- 中位数: {length_stats['median']} 字符")
            report_lines.append("")
        
        # 置信度统计
        if 'confidence_statistics' in stats and stats['confidence_statistics']:
            conf_stats = stats['confidence_statistics']
            report_lines.append("## 分类置信度统计")
            report_lines.append(f"- 最低置信度: {conf_stats['min']}")
            report_lines.append(f"- 最高置信度: {conf_stats['max']}")
            report_lines.append(f"- 平均置信度: {conf_stats['avg']}")
            report_lines.append("")
        
        # 生成时间统计
        if 'generation_time_statistics' in stats and stats['generation_time_statistics']:
            time_stats = stats['generation_time_statistics']
            report_lines.append("## 生成时间统计")
            report_lines.append(f"- 最短时间: {time_stats['min']:.2f} 秒")
            report_lines.append(f"- 最长时间: {time_stats['max']:.2f} 秒")
            report_lines.append(f"- 平均时间: {time_stats['avg']:.2f} 秒")
            report_lines.append(f"- 有时间记录的样本: {time_stats['samples_with_time']}")
            report_lines.append("")
        
        # 样本示例
        report_lines.append("## 样本示例")
        
        # Django样本示例
        django_samples = [s for s in self.results['detailed_samples'] if 'django' in s['features']]
        if django_samples:
            report_lines.append("### Django代码样本")
            for i, sample in enumerate(django_samples[:3]):  # 显示前3个
                report_lines.append(f"**样本 {sample['sample_id']}**:")
                report_lines.append(f"```python")
                report_lines.append(sample['generated_code_preview'])
                report_lines.append(f"```")
                report_lines.append("")
        
        # C代码样本示例
        c_samples = [s for s in self.results['detailed_samples'] if 'c_code' in s['features']]
        if c_samples:
            report_lines.append("### C代码样本")
            for i, sample in enumerate(c_samples[:3]):  # 显示前3个
                report_lines.append(f"**样本 {sample['sample_id']}**:")
                report_lines.append(f"```c")
                report_lines.append(sample['generated_code_preview'])
                report_lines.append(f"```")
                report_lines.append("")
        
        # 错误分析
        if self.results['error_analysis']:
            report_lines.append("## 错误分析")
            for file_path, error in self.results['error_analysis'].items():
                report_lines.append(f"- {file_path}: {error}")
            report_lines.append("")
        
        # 结论和建议
        report_lines.append("## 结论和建议")
        total = stats['total_samples']
        django_rate = (stats['category_counts'].get('Django Only', 0) + stats['category_counts'].get('Mixed', 0)) / total * 100
        c_rate = stats['category_counts'].get('C Only', 0) / total * 100
        
        if django_rate > 50:
            report_lines.append("🚨 **严重问题**: Django代码生成率超过50%，表明API服务存在根本性配置问题")
        if c_rate < 20:
            report_lines.append("⚠️ **警告**: 正确C代码生成率低于20%，无法满足patch porting任务需求")
        if django_rate > c_rate:
            report_lines.append("❌ **不合格**: Django代码生成率高于C代码生成率，API服务配置严重错误")
        
        report_lines.append("")
        report_lines.append("### 改进建议")
        report_lines.append("1. 检查API服务的模型权重配置")
        report_lines.append("2. 验证LoRA适配器的训练数据正确性")
        report_lines.append("3. 调整生成参数（temperature, top_p等）")
        report_lines.append("4. 实施更严格的输出格式验证")
        report_lines.append("5. 考虑重新训练专门用于C代码patch porting的模型")
        
        return "\n".join(report_lines)
    
    def save_results(self, output_dir: str) -> None:
        """保存分析结果
        
        Args:
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存完整JSON结果
        json_path = os.path.join(output_dir, 'category_analysis_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        print(f"完整结果已保存到: {json_path}")
        
        # 保存详细报告
        report_path = os.path.join(output_dir, 'category_analysis_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(self.generate_detailed_report())
        print(f"详细报告已保存到: {report_path}")
        
        # 保存统计摘要
        summary_path = os.path.join(output_dir, 'category_statistics_summary.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(self.results['statistics'], f, ensure_ascii=False, indent=2)
        print(f"统计摘要已保存到: {summary_path}")


def main():
    """主函数"""
    print("🔍 开始分析API输出结果类别...")
    
    # 获取当前脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = script_dir
    output_dir = os.path.join(script_dir, 'analysis_results')
    
    # 创建分析器
    analyzer = CodeCategoryAnalyzer()
    
    # 分析所有结果文件
    analyzer.analyze_all_result_files(base_dir)
    
    # 计算统计数据
    analyzer.calculate_statistics()
    
    # 保存结果
    analyzer.save_results(output_dir)
    
    # 打印简要统计
    stats = analyzer.results['statistics']
    print("\n📊 分析完成！简要统计:")
    print(f"总样本数: {stats['total_samples']}")
    
    if stats['category_counts']:
        print("类别分布:")
        for category in sorted(stats['category_counts'].keys()):
            count = stats['category_counts'][category]
            percentage = stats['category_percentages'][category]
            print(f"  - {category}: {count}个 ({percentage}%)")
    
    print(f"\n📁 详细结果已保存到: {output_dir}")
    print("📋 请查看 category_analysis_report.md 获取完整分析报告")


if __name__ == '__main__':
    main() 