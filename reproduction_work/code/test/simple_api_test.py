#!/usr/bin/env python3
"""
简化的API测试脚本 - 直接调用StarCoder API进行patch porting测试
绕过predict.py的复杂依赖问题
"""

import os
import json
import requests
from pathlib import Path
import time
from typing import List, Dict

class SimpleAPITester:
    """简化的API测试器"""
    
    def __init__(self):
        self.api_base_url = "http://10.150.10.76:5005"
        self.data_path = "/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/quality_fixed/custom_vim_neovim_quality_4096.json"
        self.output_dir = Path("/home/<USER>/nas-files/Re-paper/ppathf/outputs")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"✅ 简化API测试器初始化完成")
        print(f"🔗 API地址: {self.api_base_url}")
        print(f"📁 数据文件: {self.data_path}")
        print(f"📤 输出目录: {self.output_dir}")
    
    def test_api_connection(self):
        """测试API连接"""
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=10)
            if response.status_code == 200:
                health_info = response.json()
                print(f"✅ API连接正常")
                print(f"📊 模型状态: {health_info.get('model_loaded', 'Unknown')}")
                print(f"🎯 设备: {health_info.get('device', 'Unknown')}")
                return True
            else:
                print(f"❌ API响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API连接失败: {e}")
            return False
    
    def create_prompt(self, sample: Dict) -> str:
        """创建patch porting的prompt - 使用原始仓库的正确格式"""
        # 使用原始仓库的PROMPT_TEMPLATE格式
        prompt = f"""Below is a patch (including function before and function after) from vim, paired with a corresponding function before from neovim. Adapt the patch from vim to neovim by generating the function after based on the given function before.

### Function Before (vim):
{sample['func_before_source']}

### Function After (vim):
{sample['func_after_source']}

### Function Before (neovim):
{sample['func_before_target']}

### Function After (neovim):
"""
        return prompt
    
    def call_api(self, prompt: str, max_tokens: int = 2048) -> str:
        """调用API生成响应 - 使用OpenAI兼容格式"""
        try:
            payload = {
                "model": "starcoder",
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": 0.1,
                "stop": ["### Function", "\n\n\n", "```"]
            }
            
            response = requests.post(
                f"{self.api_base_url}/v1/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                # OpenAI格式的响应
                choices = result.get("choices", [])
                if choices:
                    return choices[0].get("text", "")
                return ""
            else:
                print(f"❌ API调用失败: {response.status_code}")
                return ""
                
        except Exception as e:
            print(f"❌ API调用异常: {e}")
            return ""
    
    def run_small_test(self, sample_count: int = 5):
        """运行小规模测试"""
        print(f"\n🧪 开始小规模测试 (样本数: {sample_count})")
        print("=" * 50)
        
        # 加载数据
        try:
            with open(self.data_path, 'r') as f:
                data = json.load(f)
            print(f"📚 数据加载成功，总数: {len(data)}")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
        
        # 取前N个样本进行测试
        test_samples = data[:sample_count]
        results = []
        
        for i, sample in enumerate(test_samples):
            print(f"\n🔄 处理样本 {i+1}/{sample_count}")
            print(f"📋 Commit: {sample['commit_id_target'][:8]}...")
            print(f"📁 文件: {sample['file_path_target']}")
            
            # 创建prompt
            prompt = self.create_prompt(sample)
            
            # 调用API
            start_time = time.time()
            generated_text = self.call_api(prompt)
            end_time = time.time()
            
            if generated_text:
                print(f"✅ 生成成功 ({end_time - start_time:.2f}s)")
                print(f"📏 生成长度: {len(generated_text)} 字符")
            else:
                print(f"❌ 生成失败")
            
            # 保存结果
            result = {
                "sample_id": i,
                "commit_id": sample['commit_id_target'],
                "file_path": sample['file_path_target'],
                "prompt": prompt[:200] + "..." if len(prompt) > 200 else prompt,
                "generated": generated_text,
                "expected": sample['func_after_target'],
                "generation_time": end_time - start_time,
                "success": bool(generated_text)
            }
            results.append(result)
        
        # 保存结果
        result_file = self.output_dir / f"simple_api_test_results_{sample_count}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 测试结果保存至: {result_file}")
        
        # 统计结果
        successful = sum(1 for r in results if r['success'])
        print(f"✅ 成功生成: {successful}/{sample_count} ({successful/sample_count*100:.1f}%)")
        
        return True
    
    def analyze_results(self, result_file: str):
        """分析测试结果"""
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            print(f"\n📊 结果分析")
            print("=" * 30)
            
            total = len(results)
            successful = sum(1 for r in results if r['success'])
            avg_time = sum(r['generation_time'] for r in results) / total
            
            print(f"总样本数: {total}")
            print(f"成功生成: {successful} ({successful/total*100:.1f}%)")
            print(f"平均时间: {avg_time:.2f}秒")
            
            # 显示部分结果示例
            print(f"\n🔍 示例结果:")
            for i, result in enumerate(results[:2]):
                print(f"\n--- 样本 {i+1} ---")
                print(f"文件: {result['file_path']}")
                print(f"生成成功: {result['success']}")
                if result['generated']:
                    print(f"生成内容: {result['generated'][:200]}...")
            
        except Exception as e:
            print(f"❌ 结果分析失败: {e}")

def main():
    """主函数"""
    tester = SimpleAPITester()
    
    # 测试API连接
    if not tester.test_api_connection():
        print("❌ API连接测试失败，请检查服务状态")
        return
    
    # 运行小规模测试
    if tester.run_small_test(sample_count=10):
        print("\n🎉 小规模测试完成!")
        
        # 分析结果
        result_file = tester.output_dir / "simple_api_test_results_10.json"
        if result_file.exists():
            tester.analyze_results(str(result_file))
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main() 