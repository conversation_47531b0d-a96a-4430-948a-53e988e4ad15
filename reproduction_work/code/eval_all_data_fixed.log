nohup: 忽略输入
✅ 初始化完成
📁 数据文件: /home/<USER>/nas-files/Re-paper/ppathf/data/data/vim_neovim_test_all.json
📤 输出路径: outputs/out_starcoder_all_data_result.json
🔧 API服务: http://10.150.10.76:5002
🎯 开始PPatHF完整评估流程
==================================================
✅ 数据文件检查通过
📊 数据总数: 310
🔑 数据字段: ['commit_id_source', 'file_path_source', 'func_before_source', 'func_after_source', 'diff_source', 'commit_id_target', 'file_path_target', 'func_before_target', 'func_after_target', 'diff_target', 'neovim_committer_date']

🚀 开始运行预测生成...
📋 执行命令: python /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/predict.py --base_model_name_or_path /root/Star-Coder-Model/starcoder --data_path /home/<USER>/nas-files/Re-paper/ppathf/data/data/vim_neovim_test_all.json --output_path outputs/out_starcoder_all_data_result.json --model_max_length 8192 --max_length 8192 --api_url http://10.150.10.76:5002/v1/completions
❌ 预测生成失败
错误输出: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/predict.py", line 162, in <module>
    fire.Fire(generate)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/fire/core.py", line 135, in Fire
    component_trace = _Fire(component, args, parsed_flag_args, context, name)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/fire/core.py", line 468, in _Fire
    component, remaining_args = _CallAndUpdateTrace(
                                ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/fire/core.py", line 684, in _CallAndUpdateTrace
    component = fn(*varargs, **kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/predict.py", line 65, in generate
    tokenizer = AutoTokenizer.from_pretrained(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 643, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 487, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/utils/hub.py", line 417, in cached_file
    resolved_file = hf_hub_download(
                    ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.


❌ 评估过程中出现错误
