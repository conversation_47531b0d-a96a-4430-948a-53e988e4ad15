{"total_samples": 646, "category_counts": {"Django Only": 162, "Python Only": 210, "Mixed": 119, "Empty": 76, "C Only": 60, "Unknown": 6, "Short Content": 13}, "category_percentages": {"Django Only": 25.08, "Python Only": 32.51, "Mixed": 18.42, "Empty": 11.76, "C Only": 9.29, "Unknown": 0.93, "Short Content": 2.01}, "length_statistics": {"min": 0, "max": 9996, "avg": 1476.56, "median": 900}, "confidence_statistics": {"min": 0.5, "max": 1.0, "avg": 0.89}, "generation_time_statistics": {"min": 5.312315464019775, "max": 236.07557129859924, "avg": 98.65, "samples_with_time": 316}}