nohup: 忽略输入
✅ 初始化完成
📁 数据文件: /home/<USER>/nas-files/Re-paper/ppathf/data/data/vim_neovim_test_all.json
📤 输出路径: outputs/out_starcoder_all_data_result.json
🔧 API服务: http://10.150.10.76:5002
🎯 开始PPatHF完整评估流程
==================================================
✅ 数据文件检查通过
📊 数据总数: 310
🔑 数据字段: ['commit_id_source', 'file_path_source', 'func_before_source', 'func_after_source', 'diff_source', 'commit_id_target', 'file_path_target', 'func_before_target', 'func_after_target', 'diff_target', 'neovim_committer_date']

🚀 开始运行预测生成...
📋 执行命令: python /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/predict.py --base_model_name_or_path dummy --data_path /home/<USER>/nas-files/Re-paper/ppathf/data/data/vim_neovim_test_all.json --output_path outputs/out_starcoder_all_data_result.json --model_max_length 8192 --max_length 8192 --api_url http://10.150.10.76:5002/v1/completions
❌ 预测生成失败
错误输出: /home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py:943: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.
  warnings.warn(
Traceback (most recent call last):
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connection.py", line 419, in connect
    self.sock = ssl_wrap_socket(
                ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/util/ssl_.py", line 449, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/util/ssl_.py", line 493, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/ssl.py", line 517, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/ssl.py", line 1108, in _create
    self.do_handshake()
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/ssl.py", line 1379, in do_handshake
    self._sslobj.do_handshake()
ConnectionResetError: [Errno 104] Connection reset by peer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connectionpool.py", line 799, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/util/retry.py", line 550, in increment
    raise six.reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/packages/six.py", line 769, in reraise
    raise value.with_traceback(tb)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
                       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/connection.py", line 419, in connect
    self.sock = ssl_wrap_socket(
                ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/util/ssl_.py", line 449, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(
               ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/urllib3/util/ssl_.py", line 493, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/ssl.py", line 517, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/ssl.py", line 1108, in _create
    self.do_handshake()
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/ssl.py", line 1379, in do_handshake
    self._sslobj.do_handshake()
urllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py", line 1533, in _get_metadata_or_catch_error
    metadata = get_hf_file_metadata(
               ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py", line 1450, in get_hf_file_metadata
    r = _request_wrapper(
        ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py", line 286, in _request_wrapper
    response = _request_wrapper(
               ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py", line 309, in _request_wrapper
    response = http_backoff(method=method, url=url, **params, retry_on_exceptions=(), retry_on_status_codes=(429,))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_http.py", line 310, in http_backoff
    response = session.request(method=method, url=url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_http.py", line 96, in send
    return super().send(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/requests/adapters.py", line 682, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: (ProtocolError('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer')), '(Request ID: 86d80db2-a96e-402d-b6c7-f67ff335ff01)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/utils/hub.py", line 417, in cached_file
    resolved_file = hf_hub_download(
                    ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py", line 1008, in hf_hub_download
    return _hf_hub_download_to_cache_dir(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py", line 1115, in _hf_hub_download_to_cache_dir
    _raise_on_head_call_error(head_call_error, force_download, local_files_only)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/file_download.py", line 1648, in _raise_on_head_call_error
    raise LocalEntryNotFoundError(
huggingface_hub.errors.LocalEntryNotFoundError: An error happened while trying to locate the file on the Hub and we cannot find the requested files in the local cache. Please check your connection and try again or make sure your Internet connection is on.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/predict.py", line 162, in <module>
    fire.Fire(generate)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/fire/core.py", line 135, in Fire
    component_trace = _Fire(component, args, parsed_flag_args, context, name)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/fire/core.py", line 468, in _Fire
    component, remaining_args = _CallAndUpdateTrace(
                                ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/fire/core.py", line 684, in _CallAndUpdateTrace
    component = fn(*varargs, **kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/predict.py", line 65, in generate
    tokenizer = AutoTokenizer.from_pretrained(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 658, in from_pretrained
    config = AutoConfig.from_pretrained(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/configuration_auto.py", line 928, in from_pretrained
    config_dict, unused_kwargs = PretrainedConfig.get_config_dict(pretrained_model_name_or_path, **kwargs)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/configuration_utils.py", line 574, in get_config_dict
    config_dict, kwargs = cls._get_config_dict(pretrained_model_name_or_path, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/configuration_utils.py", line 629, in _get_config_dict
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/utils/hub.py", line 452, in cached_file
    raise EnvironmentError(
OSError: We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like dummy is not the path to a directory containing a file named config.json.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.


❌ 评估过程中出现错误
