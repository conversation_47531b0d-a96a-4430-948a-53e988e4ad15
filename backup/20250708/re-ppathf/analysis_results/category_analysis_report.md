# API输出结果类别分析报告
分析时间: 2025-07-07 14:49:43

## 总体统计
- 总样本数: 646

## 类别分布
- **C Only**: 60个 (9.29%)
- **Django Only**: 162个 (25.08%)
- **Empty**: 76个 (11.76%)
- **Mixed**: 119个 (18.42%)
- **Python Only**: 210个 (32.51%)
- **Short Content**: 13个 (2.01%)
- **Unknown**: 6个 (0.93%)

## 🚨 重点问题分析
- **Django代码污染**: 281个样本包含Django代码 (43.5%)
- **正确C代码**: 60个样本包含正确的C代码 (9.3%)
- **混合输出**: 119个样本包含混合代码 (18.4%)
- **空输出**: 76个样本为空 (11.8%)

## 代码长度统计
- 最短: 0 字符
- 最长: 9996 字符
- 平均: 1476.56 字符
- 中位数: 900 字符

## 分类置信度统计
- 最低置信度: 0.5
- 最高置信度: 1.0
- 平均置信度: 0.89

## 生成时间统计
- 最短时间: 5.31 秒
- 最长时间: 236.08 秒
- 平均时间: 98.65 秒
- 有时间记录的样本: 316

## 样本示例
### Django代码样本
**样本 0**:
```python
t_name,
            last_name=last_name
        )

        return user

    def create_superuser(self, email, password=<PASSWORD>, **extra_fields):
        """Create and save a new superuser"""
      ...
```

**样本 2**:
```python
static int help_compare(const void *s1, const void *s2)
{
  char *p1;
  char *p2;
  int cmp;
  p1 = *(char **)s1 + strlen(*(char **)s1) + 1;
  p2 = *(char **)s2 + strlen(*(char **)s2) + 1;
  cmp = str...
```

**样本 3**:
```python
static int calc_hist_idx(int histype, int num)
{
  int i;
  histentry_T *hist;
  int wrapped = FALSE;
  if (hislen == 0 || histype < 0 || histype >= HIST_COUNT
      || (i = hisidx[histype]) < 0 || nu...
```

### C代码样本
**样本 2**:
```c
static int help_compare(const void *s1, const void *s2)
{
  char *p1;
  char *p2;
  int cmp;
  p1 = *(char **)s1 + strlen(*(char **)s1) + 1;
  p2 = *(char **)s2 + strlen(*(char **)s2) + 1;
  cmp = str...
```

**样本 3**:
```c
static int calc_hist_idx(int histype, int num)
{
  int i;
  histentry_T *hist;
  int wrapped = FALSE;
  if (hislen == 0 || histype < 0 || histype >= HIST_COUNT
      || (i = hisidx[histype]) < 0 || nu...
```

**样本 8**:
```c
e_name='ID')),
                ('title', models.CharField(max_length=50)),
                ('description', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            na...
```

## 结论和建议
⚠️ **警告**: 正确C代码生成率低于20%，无法满足patch porting任务需求
❌ **不合格**: Django代码生成率高于C代码生成率，API服务配置严重错误

### 改进建议
1. 检查API服务的模型权重配置
2. 验证LoRA适配器的训练数据正确性
3. 调整生成参数（temperature, top_p等）
4. 实施更严格的输出格式验证
5. 考虑重新训练专门用于C代码patch porting的模型