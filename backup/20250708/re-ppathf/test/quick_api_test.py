#!/usr/bin/env python3
"""
快速API测试脚本 - 解决超时问题
使用较短样本和优化的prompt格式
"""

import json
import requests
import time
from pathlib import Path

class QuickAPITester:
    """快速API测试器 - 针对超时问题优化"""
    
    def __init__(self):
        self.api_base_url = "http://10.150.10.76:5002"
        self.data_path = "/home/<USER>/nas-files/Re-paper/ppathf/data/data/vim_neovim_test_all.json"
        self.output_dir = Path("/home/<USER>/nas-files/Re-paper/ppathf/outputs")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"⚡ 快速API测试器初始化")
        print(f"🎯 专门解决超时和长度问题")
    
    def test_simple_generation(self):
        """测试简单的代码生成"""
        print(f"\n🧪 测试简单代码生成...")
        
        simple_prompt = """Complete this C function:
void hello_world() {
    printf("Hello, """
        
        try:
            payload = {
                "model": "starcoder",
                "prompt": simple_prompt,
                "max_tokens": 50,
                "temperature": 0.1
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.api_base_url}/v1/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30  # 较短超时
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                text = result['choices'][0]['text'] if result['choices'] else ""
                print(f"✅ 简单测试成功 ({end_time-start_time:.2f}s)")
                print(f"📝 生成结果: {text[:100]}...")
                return True
            else:
                print(f"❌ 简单测试失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 简单测试异常: {e}")
            return False

def main():
    tester = QuickAPITester()
    
    # 简单生成测试
    if tester.test_simple_generation():
        print("\n🎉 API基本功能正常！")
    else:
        print("\n❌ API基本功能异常")

if __name__ == "__main__":
    main() 