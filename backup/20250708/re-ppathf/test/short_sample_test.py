#!/usr/bin/env python3
"""
短样本Patch Porting测试 - 针对输入长度优化
使用较短的样本测试PPatHF的实际效果
"""

import json
import requests
import time
from pathlib import Path

class ShortSampleTester:
    """短样本测试器"""
    
    def __init__(self):
        self.api_base_url = "http://10.150.10.76:5002"
        self.data_path = "/home/<USER>/nas-files/Re-paper/ppathf/data/data/vim_neovim_test_all.json"
        self.output_dir = Path("/home/<USER>/nas-files/Re-paper/ppathf/outputs")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"🎯 短样本Patch Porting测试器")
        print(f"📋 目标: 测试工具在合适输入长度下的实际效果")
        print(f"⏰ 超时设置: 5分钟 (300秒)")
    
    def find_short_samples(self, max_length=2500, count=5):
        """找到合适长度的样本"""
        with open(self.data_path, 'r') as f:
            data = json.load(f)
        
        suitable_samples = []
        for i, sample in enumerate(data):
            # 计算总长度
            total_len = (len(sample['func_before_source']) + 
                        len(sample['func_after_source']) + 
                        len(sample['func_before_target']))
            
            if total_len <= max_length:
                suitable_samples.append((i, sample, total_len))
        
        # 按长度排序并选择
        suitable_samples.sort(key=lambda x: x[2])
        selected = suitable_samples[:count]
        
        print(f"📊 从{len(data)}个样本中找到{len(suitable_samples)}个合适样本")
        print(f"🎯 选择最短的{len(selected)}个进行测试:")
        for i, (idx, sample, length) in enumerate(selected):
            print(f"  样本{i+1}: 原索引{idx}, 长度{length}字符, 文件{sample['file_path_target']}")
        
        return [sample for _, sample, _ in selected]
    
    def create_patch_prompt(self, sample):
        """创建patch porting的prompt - 使用原始仓库的正确格式"""
        # 使用原始仓库的PROMPT_TEMPLATE格式
        prompt = f"""Below is a patch (including function before and function after) from vim, paired with a corresponding function before from neovim. Adapt the patch from vim to neovim by generating the function after based on the given function before.

### Function Before (vim):
{sample['func_before_source']}

### Function After (vim):
{sample['func_after_source']}

### Function Before (neovim):
{sample['func_before_target']}

### Function After (neovim):
"""
        return prompt
    
    def call_api_with_retry(self, prompt, max_tokens=1024, timeout=300):
        """调用API with重试机制 - 5分钟超时"""
        for attempt in range(2):  # 重试2次
            try:
                payload = {
                    "model": "starcoder",
                    "prompt": prompt,
                    "max_tokens": max_tokens,
                    "temperature": 0.1,
                    "stop": ["### Function", "\n\n\n", "```"]
                }
                
                response = requests.post(
                    f"{self.api_base_url}/v1/completions",
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    text = result['choices'][0]['text'] if result['choices'] else ""
                    return True, text
                else:
                    print(f"⚠️ 尝试{attempt+1}: HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"⚠️ 尝试{attempt+1}: 超时 (>{timeout}秒)")
            except Exception as e:
                print(f"⚠️ 尝试{attempt+1}: {e}")
        
        return False, ""
    
    def run_patch_porting_test(self):
        """运行patch porting测试"""
        print(f"\n🧪 开始Patch Porting测试...")
        
        # 选择合适的样本
        samples = self.find_short_samples(max_length=2500, count=3)
        if not samples:
            print("❌ 没有找到合适的短样本")
            return False
        
        results = []
        
        for i, sample in enumerate(samples):
            print(f"\n📋 测试样本 {i+1}/{len(samples)}")
            print(f"🗂️ 文件: {sample['file_path_target']}")
            print(f"🔧 Commit: {sample['commit_id_target'][:8]}...")
            
            # 创建prompt
            prompt = self.create_patch_prompt(sample)
            print(f"📏 Prompt长度: {len(prompt)} 字符")
            
            # 调用API
            start_time = time.time()
            success, generated_text = self.call_api_with_retry(prompt)
            end_time = time.time()
            
            if success:
                print(f"✅ 生成成功 ({end_time-start_time:.2f}s)")
                print(f"📝 生成长度: {len(generated_text)} 字符")
                
                # 显示部分结果
                print(f"🔍 生成预览:")
                preview = generated_text[:300].replace('\n', '\\n')
                print(f"   {preview}...")
                
                # 基本质量检查
                contains_function = 'void' in generated_text or 'int' in generated_text
                reasonable_length = 50 < len(generated_text) < 2000
                
                print(f"📊 基本质量:")
                print(f"   包含函数结构: {'✅' if contains_function else '❌'}")
                print(f"   长度合理: {'✅' if reasonable_length else '❌'}")
                
            else:
                print(f"❌ 生成失败")
                generated_text = ""
            
            # 保存结果
            result = {
                "sample_id": i,
                "original_index": samples.index(sample),
                "file_path": sample['file_path_target'],
                "commit_id": sample['commit_id_target'],
                "success": success,
                "generated": generated_text,
                "expected": sample['func_after_target'],
                "generation_time": end_time - start_time,
                "prompt_length": len(prompt),
                "quality_checks": {
                    "contains_function": contains_function if success else False,
                    "reasonable_length": reasonable_length if success else False
                } if success else {}
            }
            results.append(result)
        
        # 保存详细结果
        result_file = self.output_dir / "short_sample_test_5min_timeout.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 测试总结:")
        successful = sum(1 for r in results if r['success'])
        print(f"✅ 成功生成: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
        
        if successful > 0:
            avg_time = sum(r['generation_time'] for r in results if r['success']) / successful
            print(f"⏱️ 平均生成时间: {avg_time:.2f}秒")
        
        print(f"💾 详细结果: {result_file}")
        
        return successful > 0

def main():
    tester = ShortSampleTester()
    
    if tester.run_patch_porting_test():
        print("\n🎉 5分钟超时测试成功!")
        print("💡 证明了工具在充足时间下的表现")
    else:
        print("\n❌ 5分钟超时测试失败")

if __name__ == "__main__":
    main() 