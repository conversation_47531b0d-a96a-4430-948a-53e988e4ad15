# PPatHF 项目复现指南

## 项目简介

PPatHF（Patch Porting across Hard Forks）是一个自动化补丁跨硬分叉移植的研究项目。该项目使用大型语言模型（LLM）来自动将安全补丁和bug修复从原项目（如Vim）移植到其硬分叉项目（如Neovim）。

## 环境配置

### 1. Python环境设置

```bash
# 创建虚拟环境
python -m venv ppathf_env
source ppathf_env/bin/activate  # Linux/Mac
# 或
ppathf_env\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. Tree-sitter配置

```bash
# 克隆tree-sitter-c仓库
git clone https://github.com/tree-sitter/tree-sitter-c
cd tree-sitter-c

# 返回项目根目录
cd ..
```

### 3. 下载预训练模型

从Huggingface下载StarCoder模型：已经对该模型进行了在线部署（************:5002）

```bash
# 使用git-lfs
git lfs install
git clone https://huggingface.co/bigcode/starcoder

# 或使用huggingface-cli
pip install huggingface-hub
huggingface-cli download bigcode/starcoder --local-dir ./models/starcoder
```

### 4. 数据准备

解压数据文件：
```bash
cd src/PPatHF-main/data
unzip "PPatHF - Data-20250525T095818Z-1-001.zip"
```

## 配置文件设置

### 1. 更新主配置文件

编辑 `src/PPatHF-main/PPatHF-main/config.py`：

```python
# 设置模型路径
LLM_PATH = "/path/to/your/models/"
LLM_NAME_PATH_MAPPING = {"starcoder": "starcoder"}

# 设置数据和输出路径
MODEL_PATH = "./models/"
DATA_PATH = "./src/PPatHF-main/data/PPatHF - Data/data/"
OUTPUT_PATH = "./outputs/"

# 设置指标提取路径
EXTRACTED_METRIC_PATH = "./metrics/"
```

### 2. 配置Reduction模块

编辑 `src/PPatHF-main/PPatHF-main/reduction/reducerConfig.py`：

```python
# tree-sitter-c路径
TREE_SITTER_C_PATH = "./tree-sitter-c"
enough_alpha_threshold = 3
```

## 运行实验

### 1. 数据预处理（Reduction模块）

```bash
cd src/PPatHF-main/PPatHF-main

# 运行reduction模块处理数据
python -c "
from reduction.reducer import Reducer
from utils import process_dataset_with_reduction
import json

# 读取原始数据
with open('${DATA_PATH}/vim_neovim_test_all.json', 'r') as f:
    data = json.load(f)

# 应用reduction
reducer = Reducer(tolerant=True)
reduced_data = process_dataset_with_reduction(data, reducer)

# 保存处理后的数据
with open('${DATA_PATH}/vim_neovim_test_sliced_all.json', 'w') as f:
    json.dump(reduced_data, f, indent=2)
"
```

### 2. 模型微调

```bash
cd src/PPatHF-main/PPatHF-main/porting

# 修改run.sh中的路径
export MODEL_PATH="/path/to/starcoder"
export DATA_PATH="${DATA_PATH}/finetune.json"
export OUTDIR="./finetuned_model"

# 运行微调（需要GPU）
bash run.sh
```

### 3. 模型推理

```bash
cd src/PPatHF-main/PPatHF-main

# 运行预测
python predict.py \
    --base_model_name_or_path "/path/to/starcoder" \
    --data_path "${DATA_PATH}/vim_neovim_test_sliced_4096.json" \
    --output_path "./outputs/predictions_4096.json" \
    --peft_output "./porting/finetuned_model" \
    --model_max_length 4096 \
    --max_length 4096 \
    --device "cuda:0"
```

### 4. 评估结果

```bash
# 运行测试工具进行批量评估
python test_tools.py
```

或手动运行评估：

```bash
# 计算指标
python -c "
from test import cal_metrics

cal_metrics(
    data_path='${DATA_PATH}/vim_neovim_test_sliced_4096.json',
    output_path='./outputs/predictions_4096.json',
    do_recover=True,
    reference_key='func_after_target_origin',
    reference_before_key='func_before_target_origin',
    save_sample_wise_results=True
)
"
```

## 实验流程总结

1. **数据准备阶段**
   - 原始数据：310个Vim到Neovim的补丁
   - Reduction处理：移除无关代码，提高样本通过率
   - 长度筛选：生成2048/4096/8192不同长度限制的数据集

2. **模型训练阶段**
   - 基座模型：StarCoder
   - 微调方法：LoRA (r=8, alpha=16)
   - 训练数据：4829个历史补丁样本

3. **推理评估阶段**
   - 零样本移植：在测试集上生成Neovim补丁
   - 评估指标：
     - Exact Match：完全匹配率
     - AED：绝对编辑距离
     - RED：相对编辑距离

## 常见问题

### 1. CUDA内存不足

减小batch size或使用更长的gradient_accumulation_steps：

```bash
# 修改run.sh
--per_device_train_batch_size 1 \
--gradient_accumulation_steps 32 \
```

### 2. tree-sitter安装问题

确保已安装C编译器：

```bash
# Ubuntu/Debian
sudo apt-get install build-essential

# CentOS/RHEL
sudo yum install gcc gcc-c++ make
```

### 3. 模型下载速度慢

使用镜像或代理：

```bash
export HF_ENDPOINT=https://hf-mirror.com
```

## 预期结果

根据论文，PPatHF在不同长度限制下的性能：

- 2048 tokens：约30-40%的exact match率
- 4096 tokens：约40-50%的exact match率  
- 8192 tokens：约50-60%的exact match率

微调后的模型相比原始StarCoder有显著提升（约15-20%）。

## 扩展实验

1. **其他硬分叉项目**
   - CPython-Cinder
   - FreeBSD-OpenBSD
   - Bitcoin-Litecoin

2. **不同基座模型**
   - CodeLlama
   - DeepSeek-Coder
   - WizardCoder

3. **改进方向**
   - 更智能的reduction策略
   - 多轮迭代的移植方法
   - 结合静态分析工具 