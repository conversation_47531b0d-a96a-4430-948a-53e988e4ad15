nohup: 忽略输入
======================================
🚀 多API并发推理系统启动
开始时间: 2025年 07月 18日 星期五 17:12:04 UTC
======================================
🔧 检测到 2 个API服务器
   API-1: http://10.150.10.76:5001
   API-2: http://10.150.10.76:5002
🔍 检查API服务器可用性...
测试 API-1 (http://10.150.10.76:5001)... ✅ 可用
测试 API-2 (http://10.150.10.76:5002)... ✅ 可用

请选择处理模式:
1) 传统模式: 不同API处理不同数据文件
2) 并发模式: 多个API并发处理同一数据文件 (新功能)
src_enhancedz/batch_multi_api_concurrent_inference.sh: 第 469 行： read: 读错误: 0: 错误的文件描述符
🔄 启动传统模式...
📊 任务分配策略:
   总数据集: 1 个
   API服务器: 2 个
   每个API平均处理: 0 个数据集

🔄 启动Worker-1 (API: http://10.150.10.76:5001)
   处理数据集索引: 0 到 -1
   输出目录: reproduction_work/inference_results
   日志目录: reproduction_work/logs/worker_1_api_1
   Worker-1 PID: 22763

🔄 启动Worker-2 (API: http://10.150.10.76:5002)
   处理数据集索引: 0 到 0
   输出目录: reproduction_work/inference_results
   日志目录: reproduction_work/logs/worker_2_api_2
   Worker-2 PID: 22803

请选择监控方式:
1) 实时监控面板 (推荐)
2) 后台运行，仅等待完成
3) 后台运行，立即退出
src_enhancedz/batch_multi_api_concurrent_inference.sh: 第 501 行： read: 读错误: 0: 错误的文件描述符
默认选择: 等待完成
⏳ 等待所有Worker完成...
等待Worker-1 (PID: 22763)...
✅ Worker-1 成功完成
等待Worker-2 (PID: 22803)...
✅ Worker-2 成功完成

======================================
📊 生成汇总报告
======================================
📋 汇总报告已生成:
   Markdown: reproduction_work/inference_results/concurrent_inference_summary.md
   查看结果: cat reproduction_work/inference_results/concurrent_inference_summary.md

🎉 多API并发推理完成！
📁 主输出目录: reproduction_work/inference_results
📝 主日志目录: reproduction_work/logs
