2025-07-10 12:43:58,820 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 12:43:58,820 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 12:43:58,821 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 12:43:58,821 - EnhancedDataProcessor - INFO - 🔄 开始原工具预处理步骤...
2025-07-10 12:43:58,821 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 12:44:05,265 - EnhancedDataProcessor - ERROR - ❌ 原工具预处理失败: [Errno 2] No such file or directory: ''
2025-07-10 12:44:05,265 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:44:05,267 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: [Errno 2] No such file or directory: ''
2025-07-10 12:44:05,267 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:44:05,269 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: [Errno 2] No such file or directory: ''
2025-07-10 12:44:05,269 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 500, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 275, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 182, in apply_original_tool_preprocessing
    with open(adapted_data_file, 'r', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-07-10 12:44:05,271 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:48:18,984 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 12:48:18,984 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 12:48:18,984 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 12:48:18,984 - EnhancedDataProcessor - INFO - 🔄 开始原工具预处理步骤...
2025-07-10 12:48:18,985 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 12:48:23,407 - EnhancedDataProcessor - ERROR - ❌ 原工具预处理失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:23,407 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:48:23,409 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:23,409 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:48:23,411 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:23,411 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 510, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 285, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 182, in apply_original_tool_preprocessing
    with open(adapted_data_file, 'r', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json'

2025-07-10 12:48:23,413 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:48:56,564 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 12:48:56,564 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 12:48:56,564 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 12:48:56,565 - EnhancedDataProcessor - INFO - 🔄 开始原工具预处理步骤...
2025-07-10 12:48:56,565 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 12:48:58,874 - EnhancedDataProcessor - ERROR - ❌ 原工具预处理失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:58,874 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:48:58,875 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:58,875 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:48:58,877 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:58,877 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 182, in apply_original_tool_preprocessing
    with open(adapted_data_file, 'r', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json'

2025-07-10 12:48:58,879 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:50:22,468 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 12:50:22,468 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 12:50:22,468 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 12:50:22,468 - EnhancedDataProcessor - INFO - 🔄 开始原工具预处理步骤...
2025-07-10 12:50:22,469 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 12:50:37,471 - EnhancedDataProcessor - INFO - 📊 加载数据集: 3103 条记录
2025-07-10 12:50:37,471 - EnhancedDataProcessor - INFO - 📅 应用日期过滤: 2022-07-01 00:00:00+00:00
2025-07-10 12:50:37,471 - EnhancedDataProcessor - ERROR - ❌ 原工具预处理失败: time data '2018-06-26T00:08:08Z' does not match format '%Y-%m-%d %H:%M:%S%z'
2025-07-10 12:50:37,471 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:50:37,473 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: time data '2018-06-26T00:08:08Z' does not match format '%Y-%m-%d %H:%M:%S%z'
2025-07-10 12:50:37,473 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:50:37,475 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: time data '2018-06-26T00:08:08Z' does not match format '%Y-%m-%d %H:%M:%S%z'
2025-07-10 12:50:37,477 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 192, in apply_original_tool_preprocessing
    _, dataset = filter_by_date(dataset, split_date, "neovim_committer_date")
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 50, in filter_by_date
    dataset.sort(key=lambda x: datetime.strptime(x[date_key], DATE_FORMAT))
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 50, in <lambda>
    dataset.sort(key=lambda x: datetime.strptime(x[date_key], DATE_FORMAT))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/_strptime.py", line 568, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/_strptime.py", line 349, in _strptime
    raise ValueError("time data %r does not match format %r" %
ValueError: time data '2018-06-26T00:08:08Z' does not match format '%Y-%m-%d %H:%M:%S%z'

2025-07-10 12:50:37,478 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:51:40,366 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 12:51:40,366 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 12:51:40,366 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 12:51:40,366 - EnhancedDataProcessor - INFO - 🔄 开始原工具预处理步骤...
2025-07-10 12:51:40,366 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 12:51:49,120 - EnhancedDataProcessor - INFO - 📊 加载数据集: 3103 条记录
2025-07-10 12:51:49,121 - EnhancedDataProcessor - INFO - 🔄 标准化日期格式...
2025-07-10 12:51:49,296 - EnhancedDataProcessor - INFO - 📅 应用日期过滤: 2022-07-01 00:00:00+00:00
2025-07-10 12:51:49,347 - EnhancedDataProcessor - INFO - 📊 日期过滤后: 868 条记录
2025-07-10 12:51:49,347 - EnhancedDataProcessor - INFO - 🔄 生成 2048 tokens 限制的数据集...
2025-07-10 12:51:49,347 - EnhancedDataProcessor - ERROR - ❌ 原工具预处理失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:51:49,348 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:51:49,349 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:51:49,349 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:51:49,351 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:51:49,355 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 216, in apply_original_tool_preprocessing
    filtered_dataset = filter_by_max_length(
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 28, in filter_by_max_length
    tokenizer = AutoTokenizer.from_pretrained(model_path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 643, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 487, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/utils/hub.py", line 417, in cached_file
    resolved_file = hf_hub_download(
                    ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.

2025-07-10 12:51:49,356 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:58:17,521 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 12:58:17,521 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 12:58:17,521 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 12:58:17,522 - EnhancedDataProcessor - INFO - 🔄 开始原工具预处理步骤...
2025-07-10 12:58:17,523 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 12:58:27,931 - EnhancedDataProcessor - INFO - 📊 加载数据集: 3103 条记录
2025-07-10 12:58:27,931 - EnhancedDataProcessor - INFO - 🔄 标准化日期格式...
2025-07-10 12:58:28,109 - EnhancedDataProcessor - INFO - 📅 应用日期过滤: 2022-07-01 00:00:00+00:00
2025-07-10 12:58:28,154 - EnhancedDataProcessor - INFO - 📊 日期过滤后: 868 条记录
2025-07-10 12:58:28,154 - EnhancedDataProcessor - INFO - 🔄 生成 2048 tokens 限制的数据集...
2025-07-10 12:58:28,154 - EnhancedDataProcessor - ERROR - ❌ 原工具预处理失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:58:28,154 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:58:28,157 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:58:28,157 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:58:28,159 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:58:28,163 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 216, in apply_original_tool_preprocessing
    filtered_dataset = filter_by_max_length(
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 28, in filter_by_max_length
    tokenizer = AutoTokenizer.from_pretrained(model_path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 643, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 487, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/utils/hub.py", line 417, in cached_file
    resolved_file = hf_hub_download(
                    ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.

2025-07-10 12:58:28,165 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:59:52,989 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 12:59:52,990 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 12:59:52,990 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 12:59:52,990 - EnhancedDataProcessor - INFO - 🔄 开始原工具预处理步骤...
2025-07-10 12:59:52,990 - EnhancedDataProcessor - INFO - 🔄 设置最大记录数限制: 20
2025-07-10 12:59:52,990 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:00:03,144 - EnhancedDataProcessor - INFO - 📊 加载数据集: 3103 条记录
2025-07-10 13:00:03,144 - EnhancedDataProcessor - INFO - 🔄 标准化日期格式...
2025-07-10 13:00:03,310 - EnhancedDataProcessor - INFO - 📅 应用日期过滤: 2022-07-01 00:00:00+00:00
2025-07-10 13:00:03,353 - EnhancedDataProcessor - INFO - 📊 日期过滤后: 868 条记录
2025-07-10 13:00:03,353 - EnhancedDataProcessor - INFO - 🔄 限制处理记录数: 20/868
2025-07-10 13:00:03,355 - EnhancedDataProcessor - INFO - 🔄 生成 2048 tokens 限制的数据集...
2025-07-10 13:00:03,355 - EnhancedDataProcessor - INFO - #dataset before length filtering: 20
2025-07-10 13:00:03,355 - EnhancedDataProcessor - INFO - #dataset after length filtering: 2
2025-07-10 13:00:03,356 - EnhancedDataProcessor - INFO - 📏 字符限制: 7168 (约 2048 tokens)
2025-07-10 13:00:03,359 - EnhancedDataProcessor - INFO - ✅ 生成完成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing/custom_enhanced_2048.json (2 条记录)
2025-07-10 13:00:03,359 - EnhancedDataProcessor - INFO - 🔄 生成 4096 tokens 限制的数据集...
2025-07-10 13:00:03,359 - EnhancedDataProcessor - INFO - #dataset before length filtering: 20
2025-07-10 13:00:03,359 - EnhancedDataProcessor - INFO - #dataset after length filtering: 2
2025-07-10 13:00:03,359 - EnhancedDataProcessor - INFO - 📏 字符限制: 14336 (约 4096 tokens)
2025-07-10 13:00:03,361 - EnhancedDataProcessor - INFO - ✅ 生成完成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing/custom_enhanced_4096.json (2 条记录)
2025-07-10 13:00:03,361 - EnhancedDataProcessor - INFO - 🔄 生成 8192 tokens 限制的数据集...
2025-07-10 13:00:03,361 - EnhancedDataProcessor - INFO - #dataset before length filtering: 20
2025-07-10 13:00:03,362 - EnhancedDataProcessor - INFO - #dataset after length filtering: 4
2025-07-10 13:00:03,362 - EnhancedDataProcessor - INFO - 📏 字符限制: 28672 (约 8192 tokens)
2025-07-10 13:00:03,365 - EnhancedDataProcessor - INFO - ✅ 生成完成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing/custom_enhanced_8192.json (4 条记录)
2025-07-10 13:00:03,471 - EnhancedDataProcessor - INFO - ✅ 原工具预处理完成
2025-07-10 13:00:03,480 - WorkflowOrchestrator - INFO - ✅ 完成步骤: preprocessing
2025-07-10 13:00:03,482 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:00:03,485 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 13:00:03,485 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 13:00:03,485 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 13:00:03,485 - EnhancedDataProcessor - INFO - 🔄 开始reduction模块处理...
2025-07-10 13:00:03,486 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:00:03,488 - EnhancedDataProcessor - ERROR - ❌ Reduction模块处理失败: Incompatible Language version 15. Must be between 13 and 14
2025-07-10 13:00:03,489 - EnhancedDataProcessor - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 29, in __new__
    cls._instance.parser.set_language(cls._instance.C_LANGUAGE)
ValueError: Incompatible Language version 15. Must be between 13 and 14

2025-07-10 13:00:03,489 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:00:03,491 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: Incompatible Language version 15. Must be between 13 and 14
2025-07-10 13:00:03,491 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:00:03,519 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: Incompatible Language version 15. Must be between 13 and 14
2025-07-10 13:00:03,520 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 29, in __new__
    cls._instance.parser.set_language(cls._instance.C_LANGUAGE)
ValueError: Incompatible Language version 15. Must be between 13 and 14

2025-07-10 13:00:03,523 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:14:07,824 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 13:14:07,824 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 13:14:07,825 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 13:14:07,825 - EnhancedDataProcessor - INFO - 🔄 开始reduction模块处理...
2025-07-10 13:14:07,825 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:14:08,013 - EnhancedDataProcessor - ERROR - ❌ Reduction模块处理失败: type object 'tree_sitter.Language' has no attribute 'build_library'
2025-07-10 13:14:08,015 - EnhancedDataProcessor - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 21, in __new__
    Language.build_library(
    ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'tree_sitter.Language' has no attribute 'build_library'

2025-07-10 13:14:08,015 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:14:08,017 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: type object 'tree_sitter.Language' has no attribute 'build_library'
2025-07-10 13:14:08,017 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:14:08,019 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: type object 'tree_sitter.Language' has no attribute 'build_library'
2025-07-10 13:14:08,019 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 21, in __new__
    Language.build_library(
    ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'tree_sitter.Language' has no attribute 'build_library'

2025-07-10 13:14:08,021 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:15:16,933 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 13:15:16,933 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 13:15:16,933 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 13:15:16,933 - EnhancedDataProcessor - INFO - 🔄 开始reduction模块处理...
2025-07-10 13:15:16,934 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:15:17,071 - EnhancedDataProcessor - ERROR - ❌ Reduction模块处理失败: type object 'tree_sitter.Language' has no attribute 'find'
2025-07-10 13:15:17,072 - EnhancedDataProcessor - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 21, in __new__
    cls._instance.C_LANGUAGE = Language.find("c")
                               ^^^^^^^^^^^^^
AttributeError: type object 'tree_sitter.Language' has no attribute 'find'

2025-07-10 13:15:17,072 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:15:17,074 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: type object 'tree_sitter.Language' has no attribute 'find'
2025-07-10 13:15:17,074 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:15:17,075 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: type object 'tree_sitter.Language' has no attribute 'find'
2025-07-10 13:15:17,076 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 21, in __new__
    cls._instance.C_LANGUAGE = Language.find("c")
                               ^^^^^^^^^^^^^
AttributeError: type object 'tree_sitter.Language' has no attribute 'find'

2025-07-10 13:15:17,078 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:21:00,292 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 13:21:00,292 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 13:21:00,292 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 13:21:00,292 - EnhancedDataProcessor - INFO - 🔄 开始reduction模块处理...
2025-07-10 13:21:00,294 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:21:00,479 - EnhancedDataProcessor - ERROR - ❌ Reduction模块处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:21:00,481 - EnhancedDataProcessor - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:21:00,481 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:21:00,483 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:21:00,483 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:21:00,484 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:21:00,485 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:21:00,487 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:22:58,650 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 13:22:58,650 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 13:22:58,650 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 13:22:58,650 - EnhancedDataProcessor - INFO - 🔄 开始reduction模块处理...
2025-07-10 13:22:58,650 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:22:58,850 - EnhancedDataProcessor - ERROR - ❌ Reduction模块处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:22:58,851 - EnhancedDataProcessor - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.parser = get_parser('c')
                           ^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 19, in tree_sitter_languages.core.get_parser
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:22:58,852 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:22:58,854 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:22:58,855 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:22:58,856 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:22:58,857 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.parser = get_parser('c')
                           ^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 19, in tree_sitter_languages.core.get_parser
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:22:58,859 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:24:36,052 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 13:24:36,052 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 13:24:36,052 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 13:24:36,052 - EnhancedDataProcessor - INFO - 🔄 开始reduction模块处理...
2025-07-10 13:24:36,052 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:24:36,240 - EnhancedDataProcessor - ERROR - ❌ Reduction模块处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:24:36,241 - EnhancedDataProcessor - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:24:36,241 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:24:36,242 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:24:36,243 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:24:36,244 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:24:36,245 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:24:36,247 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:27:21,254 - EnhancedDataProcessor - INFO - 🚀 增强版数据处理器初始化完成
2025-07-10 13:27:21,254 - EnhancedDataProcessor - INFO - 📁 工作空间: /home/<USER>/nas-files/Re-paper/ppathf
2025-07-10 13:27:21,254 - EnhancedDataProcessor - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/data_processing
2025-07-10 13:27:21,254 - EnhancedDataProcessor - INFO - 🔄 开始reduction模块处理...
2025-07-10 13:27:21,254 - EnhancedDataProcessor - INFO - 📦 已添加原工具路径到Python path: /home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main
2025-07-10 13:27:21,446 - EnhancedDataProcessor - ERROR - ❌ Reduction模块处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:27:21,448 - EnhancedDataProcessor - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:27:21,448 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:27:21,450 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:27:21,451 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:27:21,672 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:27:21,674 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:27:21,676 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
