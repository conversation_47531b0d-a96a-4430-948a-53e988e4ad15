#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推理管道 - 集成原工具的predict功能和API调用

核心功能：
1. 调用原工具的predict模块进行模型推理
2. 集成StarCoder API服务调用
3. 支持多种数据集格式的批量推理
4. 智能的错误处理和重试机制
5. 详细的推理结果统计和分析

技术要点：
- 完全兼容原工具的predict接口和参数
- 支持本地模型和远程API两种推理方式
- 优化的批处理和并发控制
- 完整的进度跟踪和性能监控

作者：AI助手
创建时间：2025年1月
版本：v1.0 (推理管道)
"""

import os
import sys
import json
import logging
import asyncio
import aiohttp
import concurrent.futures
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import traceback
import time
import threading
from queue import Queue

class InferencePipeline:
    """
    推理管道
    
    集成原工具的predict功能，支持批量推理
    """
    
    def __init__(self, 
                 workspace_root: str,
                 output_dir: str,
                 config: Dict[str, Any]):
        """
        初始化推理管道
        
        Args:
            workspace_root: 工作空间根目录
            output_dir: 输出目录
            config: 推理配置 (现在支持多个API URLs)
        """
        self.workspace_root = Path(workspace_root)
        self.output_dir = Path(output_dir)
        self.config = config
        
        # 支持多个API URLs
        if isinstance(config.get('api_urls'), list):
            self.api_urls = config['api_urls']
        else:
            # 向后兼容单个API
            self.api_urls = [config.get('api_base_url', 'http://localhost:5001')]
        
        # 原工具路径
        self.original_tool_dir = self.workspace_root / "src" / "PPatHF-main" / "PPatHF-main"
        
        # 确保目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 推理统计
        self.stats = {
            'total_samples': 0,
            'successful_inferences': 0,
            'failed_inferences': 0,
            'start_time': None,
            'end_time': None,
            'processing_time_per_sample': [],
            'error_patterns': {}
        }
        
        self.logger.info("🔮 推理管道初始化完成")
        self.logger.info(f"📁 输出目录: {self.output_dir}")
        self.logger.info(f"🌐 API服务器: {len(self.api_urls)} 个")
        for i, url in enumerate(self.api_urls):
            self.logger.info(f"   API-{i+1}: {url}")
        self.logger.info(f"⚙️ 配置: {self.config}")
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / "inference.log"
        
        # 创建logger
        self.logger = logging.getLogger('InferencePipeline')
        self.logger.setLevel(logging.INFO)
        
        # 避免重复添加handlers
        if not self.logger.handlers:
            # 文件handler
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def add_original_tool_to_path(self):
        """将原工具目录添加到Python路径"""
        original_tool_str = str(self.original_tool_dir)
        if original_tool_str not in sys.path:
            sys.path.insert(0, original_tool_str)
            self.logger.info(f"📦 已添加原工具路径到Python path: {original_tool_str}")
    
    def call_api_service(self, prompt: str, api_url: str = None, max_retries: int = 2, timeout: int = 360) -> tuple[bool, str]:
        """
        调用API服务进行推理 - 支持指定API URL
        
        Args:
            prompt: 输入提示
            api_url: 指定的API URL (如果为None则使用第一个)
            max_retries: 最大重试次数
            timeout: 超时时间（秒）
            
        Returns:
            (成功状态, 生成的文本)
        """
        import requests
        import time
        
        # 选择API URL
        if api_url is None:
            api_url = self.api_urls[0]
        
        api_endpoint = f"{api_url}/v1/completions"
        
        # 如果prompt太长，尝试截断（与用户代码保持一致）
        if len(prompt) > 6000:
            self.logger.warning(f"⚠️ Prompt过长({len(prompt)}字符)，尝试截断...")
            prompt = self._truncate_prompt(prompt)
            self.logger.info(f"📏 截断后长度: {len(prompt)}字符")
        
        payload = {
            "model": "starcoder",  # 与用户配置保持一致
            "prompt": prompt,
            "max_tokens": 2048,
            "temperature": 0.1,
            "stop": ["### Function", "\n\n\n", "```"]
        }
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"🔄 API调用尝试 {attempt+1}/{max_retries}...")
                
                response = requests.post(
                    api_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    text = result['choices'][0]['text'] if result['choices'] else ""
                    self.logger.info(f"✅ API调用成功")
                    return True, text.strip()
                elif response.status_code == 500:
                    self.logger.warning(f"⚠️ 尝试{attempt+1}: HTTP 500 (服务器内部错误)")
                    time.sleep(2)  # 服务器错误时等待更长时间
                else:
                    self.logger.warning(f"⚠️ 尝试{attempt+1}: HTTP {response.status_code}")
                    time.sleep(1)
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"⚠️ 尝试{attempt+1}: 超时 (>{timeout}秒)")
                time.sleep(1)
            except requests.exceptions.ConnectionError as e:
                self.logger.warning(f"⚠️ 尝试{attempt+1}: 连接错误 - {e}")
                time.sleep(2)
            except Exception as e:
                self.logger.warning(f"⚠️ 尝试{attempt+1}: 未知错误 - {e}")
                time.sleep(1)
        
        self.logger.error(f"❌ 所有API重试失败")
        return False, ""
    
    def split_data_for_apis(self, data: List[Dict], num_apis: int) -> List[List[Dict]]:
        """
        将数据按API数量分割
        
        Args:
            data: 原始数据列表
            num_apis: API数量
            
        Returns:
            分割后的数据块列表
        """
        if num_apis <= 0:
            return [data]
        
        chunk_size = len(data) // num_apis
        remainder = len(data) % num_apis
        
        chunks = []
        start_idx = 0
        
        for i in range(num_apis):
            # 前面的块多分配一个样本（如果有余数）
            current_chunk_size = chunk_size + (1 if i < remainder else 0)
            end_idx = start_idx + current_chunk_size
            
            if start_idx < len(data):
                chunks.append(data[start_idx:end_idx])
            else:
                chunks.append([])  # 空块
            
            start_idx = end_idx
        
        return chunks
    
    def process_chunk_with_api(self, chunk: List[Dict], api_url: str, chunk_id: int) -> List[Dict]:
        """
        使用指定API处理数据块
        
        Args:
            chunk: 数据块
            api_url: API URL
            chunk_id: 块ID（用于日志）
            
        Returns:
            处理结果列表
        """
        self.logger.info(f"🔄 API块-{chunk_id} ({api_url}) 开始处理 {len(chunk)} 个样本")
        
        results = []
        successful_count = 0
        
        for i, item in enumerate(chunk):
            start_time = time.time()
            
            try:
                # 构建prompt
                if 'prompt' in item:
                    prompt = item['prompt']
                else:
                    # 如果没有现成的prompt，构建一个（根据数据格式调整）
                    prompt = self._build_prompt_from_item(item)
                
                # 调用API
                success, generated_text = self.call_api_service(prompt, api_url)
                
                processing_time = time.time() - start_time
                
                if success:
                    result = {
                        'id': item.get('id', f'chunk_{chunk_id}_item_{i}'),
                        'prompt': prompt,
                        'generated_text': generated_text,
                        'success': True,
                        'processing_time': processing_time,
                        'api_url': api_url,
                        'chunk_id': chunk_id
                    }
                    successful_count += 1
                    self.stats['successful_inferences'] += 1
                else:
                    result = {
                        'id': item.get('id', f'chunk_{chunk_id}_item_{i}'),
                        'prompt': prompt,
                        'generated_text': '',
                        'success': False,
                        'error': 'API调用失败',
                        'processing_time': processing_time,
                        'api_url': api_url,
                        'chunk_id': chunk_id
                    }
                    self.stats['failed_inferences'] += 1
                
                results.append(result)
                self.stats['processing_time_per_sample'].append(processing_time)
                
                # 进度日志
                if (i + 1) % 10 == 0:
                    self.logger.info(f"📊 API块-{chunk_id} 进度: {i+1}/{len(chunk)} ({(i+1)/len(chunk)*100:.1f}%)")
                    
            except Exception as e:
                processing_time = time.time() - start_time
                self.logger.error(f"❌ API块-{chunk_id} 样本-{i} 处理失败: {e}")
                
                result = {
                    'id': item.get('id', f'chunk_{chunk_id}_item_{i}'),
                    'prompt': item.get('prompt', ''),
                    'generated_text': '',
                    'success': False,
                    'error': str(e),
                    'processing_time': processing_time,
                    'api_url': api_url,
                    'chunk_id': chunk_id
                }
                results.append(result)
                self.stats['failed_inferences'] += 1
                self.stats['processing_time_per_sample'].append(processing_time)
        
        self.logger.info(f"✅ API块-{chunk_id} ({api_url}) 完成: {successful_count}/{len(chunk)} 成功")
        return results
    
    def _build_prompt_from_item(self, item: Dict) -> str:
        """
        从数据项构建prompt（根据实际数据格式调整）
        """
        # 这里需要根据你的具体数据格式来调整
        if 'input' in item:
            return item['input']
        elif 'question' in item:
            return item['question']
        elif 'text' in item:
            return item['text']
        else:
            # 尝试将整个item转换为字符串
            return str(item)
    
    def run_concurrent_inference_on_dataset(self, dataset_file: str, output_suffix: str = "") -> str:
        """
        使用多个API并发处理数据集
        
        Args:
            dataset_file: 数据集文件路径
            output_suffix: 输出文件后缀
            
        Returns:
            结果文件路径
        """
        try:
            self.logger.info(f"🚀 开始并发推理: {dataset_file}")
            self.logger.info(f"🌐 使用 {len(self.api_urls)} 个API并发处理")
            
            # 加载数据
            with open(dataset_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                raise ValueError("数据文件必须包含一个列表")
            
            self.stats['total_samples'] += len(data)
            self.logger.info(f"📊 总样本数: {len(data)}")
            
            # 分割数据
            chunks = self.split_data_for_apis(data, len(self.api_urls))
            self.logger.info(f"📦 数据分割为 {len(chunks)} 个块")
            
            for i, chunk in enumerate(chunks):
                self.logger.info(f"   块-{i+1}: {len(chunk)} 个样本 -> API: {self.api_urls[i] if i < len(self.api_urls) else '无'}")
            
            # 并发处理
            all_results = []
            
            if len(self.api_urls) == 1:
                # 单API处理
                self.logger.info("🔄 单API处理模式")
                chunk_results = self.process_chunk_with_api(chunks[0], self.api_urls[0], 1)
                all_results.extend(chunk_results)
            else:
                # 多API并发处理
                self.logger.info("🔄 多API并发处理模式")
                
                with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.api_urls)) as executor:
                    # 提交任务
                    future_to_chunk = {}
                    for i, (chunk, api_url) in enumerate(zip(chunks, self.api_urls)):
                        if chunk:  # 只处理非空块
                            future = executor.submit(self.process_chunk_with_api, chunk, api_url, i+1)
                            future_to_chunk[future] = i+1
                    
                    # 收集结果
                    for future in concurrent.futures.as_completed(future_to_chunk):
                        chunk_id = future_to_chunk[future]
                        try:
                            chunk_results = future.result()
                            all_results.extend(chunk_results)
                            self.logger.info(f"✅ 块-{chunk_id} 结果已收集")
                        except Exception as e:
                            self.logger.error(f"❌ 块-{chunk_id} 处理失败: {e}")
            
            # 按原始顺序排序结果（如果需要）
            # all_results.sort(key=lambda x: x.get('id', ''))
            
            # 保存结果 - 统一文件命名，避免混乱
            dataset_name = Path(dataset_file).stem
            
            # 详细结果文件
            detailed_output_file = self.output_dir / f"{dataset_name}_detailed_inference_results.json"
            with open(detailed_output_file, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
            
            # 简化结果（兼容原格式）- 使用标准命名
            simple_results = []
            for result in all_results:
                simple_results.append(result.get('generated_text', ''))
            
            simple_output_file = self.output_dir / f"{dataset_name}_inference_results.json"
            with open(simple_output_file, 'w', encoding='utf-8') as f:
                json.dump(simple_results, f, ensure_ascii=False, indent=2)
            
            # 统计信息
            successful_count = sum(1 for r in all_results if r.get('success', False))
            self.logger.info(f"\n✅ 并发推理完成")
            self.logger.info(f"📊 详细结果文件: {detailed_output_file}")
            self.logger.info(f"📊 兼容格式文件: {simple_output_file}")
            self.logger.info(f"📈 总样本数: {len(all_results)}")
            self.logger.info(f"🎯 成功生成: {successful_count}/{len(all_results)} ({successful_count/len(all_results)*100:.1f}%)")
            
            # 按API统计
            api_stats = {}
            for result in all_results:
                api_url = result.get('api_url', 'unknown')
                if api_url not in api_stats:
                    api_stats[api_url] = {'total': 0, 'success': 0}
                api_stats[api_url]['total'] += 1
                if result.get('success', False):
                    api_stats[api_url]['success'] += 1
            
            self.logger.info(f"📊 各API统计:")
            for api_url, stats in api_stats.items():
                success_rate = stats['success'] / stats['total'] * 100 if stats['total'] > 0 else 0
                self.logger.info(f"   {api_url}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
            
            return str(simple_output_file)
            
        except Exception as e:
            self.logger.error(f"❌ 并发推理失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise
    
    def _truncate_prompt(self, prompt: str) -> str:
        """截断过长的prompt（与用户代码保持一致）"""
        lines = prompt.split('\n')
        # 找到关键分隔符
        vim_before_start = -1
        vim_after_start = -1
        neovim_before_start = -1
        
        for i, line in enumerate(lines):
            if "### Function Before (vim):" in line:
                vim_before_start = i
            elif "### Function After (vim):" in line:
                vim_after_start = i
            elif "### Function Before (neovim):" in line:
                neovim_before_start = i
        
        # 截断过长的函数内容
        if vim_before_start != -1 and vim_after_start != -1:
            vim_before_lines = lines[vim_before_start+1:vim_after_start]
            if len('\n'.join(vim_before_lines)) > 1500:
                lines[vim_before_start+1:vim_after_start] = vim_before_lines[:30] + ["// ... 函数内容截断 ..."] + vim_before_lines[-10:]
        
        if vim_after_start != -1 and neovim_before_start != -1:
            vim_after_lines = lines[vim_after_start+1:neovim_before_start]
            if len('\n'.join(vim_after_lines)) > 1500:
                lines[vim_after_start+1:neovim_before_start] = vim_after_lines[:30] + ["// ... 函数内容截断 ..."] + vim_after_lines[-10:]
                
        if neovim_before_start != -1:
            neovim_before_lines = lines[neovim_before_start+1:]
            if len('\n'.join(neovim_before_lines)) > 1500:
                lines[neovim_before_start+1:] = neovim_before_lines[:30] + ["// ... 函数内容截断 ..."] + neovim_before_lines[-10:]
        
        return '\n'.join(lines)
    
    def create_inference_prompt(self, sample: Dict[str, Any]) -> str:
        """
        创建推理用的prompt - 与用户的prompt格式保持一致
        
        Args:
            sample: 数据样本
            
        Returns:
            格式化的prompt
        """
        # 使用与用户完全相同的prompt格式
        func_before_source = sample.get('func_before_source', '')
        func_after_source = sample.get('func_after_source', '')
        func_before_target = sample.get('func_before_target', '')
        
        prompt = f"""Below is a patch (including function before and function after) from vim, paired with a corresponding function before from neovim. Adapt the patch from vim to neovim by generating the function after based on the given function before.

### Function Before (vim):
{func_before_source}

### Function After (vim):
{func_after_source}

### Function Before (neovim):
{func_before_target}

### Function After (neovim):
"""
        return prompt
    
    def run_inference_on_dataset(self, dataset_file: str, output_suffix: str = "") -> str:
        """
        对单个数据集运行推理 - 与用户的推理逻辑保持一致
        
        Args:
            dataset_file: 数据集文件路径
            output_suffix: 输出文件后缀
            
        Returns:
            推理结果文件路径
        """
        self.logger.info(f"🔮 开始对数据集运行推理: {dataset_file}")
        
        try:
            # 读取数据集
            with open(dataset_file, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            
            self.logger.info(f"📊 数据集包含 {len(dataset)} 个样本")
            self.stats['total_samples'] += len(dataset)
            
            # 生成推理结果 - 使用与用户相同的结构
            detailed_results = []
            simple_results = []  # 兼容原格式
            successful_count = 0
            
            # 创建详细结果文件路径
            dataset_name = Path(dataset_file).stem
            detailed_result_path = self.output_dir / f"{dataset_name}_detailed_results{output_suffix}.json"
            simple_result_path = self.output_dir / f"{dataset_name}_inference_results{output_suffix}.json"
            
            self.logger.info(f"💾 详细结果将保存到: {detailed_result_path}")
            self.logger.info(f"💾 兼容格式将保存到: {simple_result_path}")
            
            for i, sample in enumerate(dataset):
                self.logger.info(f"\n📝 处理样本 {i+1}/{len(dataset)}")
                self.logger.info(f"🗂️ 文件: {sample.get('file_path_target', 'unknown')}")
                self.logger.info(f"🔧 Commit: {sample.get('commit_id_target', 'unknown')[:8]}...")
                
                start_time = time.time()
                
                try:
                    # 创建prompt
                    prompt = self.create_inference_prompt(sample)
                    prompt_length = len(prompt)
                    self.logger.info(f"📏 Prompt长度: {prompt_length} 字符")
                    
                    # 调用API推理
                    if self.config.get('use_api_service', True):
                        success, generated_text = self.call_api_service(prompt)
                        generation_time = time.time() - start_time
                        
                        if success:
                            self.logger.info(f"✅ 生成成功 ({generation_time:.2f}s)")
                            self.logger.info(f"📝 生成长度: {len(generated_text)} 字符")
                            successful_count += 1
                            
                            # 显示生成预览
                            preview = generated_text[:100].replace('\n', '\\n')
                            self.logger.info(f"🔍 生成预览: {preview}...")
                        else:
                            self.logger.error(f"❌ 生成失败 ({generation_time:.2f}s)")
                            generated_text = ""
                    else:
                        # 本地模型推理（如果需要）
                        generated_text = "本地模型推理尚未实现"
                        success = False
                        generation_time = 0.0
                    
                    # 创建详细结果记录（与用户格式完全一致）
                    detailed_result = {
                        "sample_index": i,
                        "commit_id_source": sample.get('commit_id_source', 'unknown'),
                        "commit_id_target": sample.get('commit_id_target', 'unknown'),
                        "file_path_source": sample.get('file_path_source', 'unknown'),
                        "file_path_target": sample.get('file_path_target', 'unknown'),
                        "func_before_source": sample.get('func_before_source', ''),
                        "func_after_source": sample.get('func_after_source', ''),
                        "func_before_target": sample.get('func_before_target', ''),
                        "func_after_target": sample.get('func_after_target', ''),  # 真实标签
                        "generated_func_after_target": generated_text,  # 生成结果
                        "generation_success": success,
                        "generation_time": generation_time,
                        "prompt_length": prompt_length,
                        "generated_length": len(generated_text),
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # 添加到结果列表
                    detailed_results.append(detailed_result)
                    simple_results.append(generated_text)  # 兼容原格式
                    
                    if success:
                        self.stats['successful_inferences'] += 1
                    else:
                        self.stats['failed_inferences'] += 1
                    
                    # 记录处理时间
                    processing_time = time.time() - start_time
                    self.stats['processing_time_per_sample'].append(processing_time)
                    
                    # 实时保存（与用户代码保持一致）
                    try:
                        # 保存详细结果
                        with open(detailed_result_path, 'w', encoding='utf-8') as f:
                            json.dump(detailed_results, f, indent=2, ensure_ascii=False)
                        
                        # 保存兼容格式
                        with open(simple_result_path, 'w', encoding='utf-8') as f:
                            json.dump(simple_results, f, indent=2, ensure_ascii=False)
                        
                        self.logger.info(f"💾 实时保存完成 ({i+1}/{len(dataset)})")
                    except Exception as save_error:
                        self.logger.warning(f"⚠️ 实时保存失败: {save_error}")
                    
                    # 进度报告
                    if (i + 1) % 10 == 0:
                        avg_time = sum(self.stats['processing_time_per_sample']) / len(self.stats['processing_time_per_sample'])
                        self.logger.info(f"\n📊 进度报告: {i+1}/{len(dataset)} ({(i+1)/len(dataset)*100:.1f}%)")
                        self.logger.info(f"✅ 成功: {successful_count}/{i+1} ({successful_count/(i+1)*100:.1f}%)")
                        self.logger.info(f"⏱️ 平均时间: {avg_time:.2f}秒/样本")
                    
                    # 添加延迟避免API压力
                    time.sleep(1)
                
                except Exception as e:
                    self.logger.error(f"❌ 样本 {i+1} 推理失败: {e}")
                    self.stats['failed_inferences'] += 1
                    
                    # 记录错误模式
                    error_type = type(e).__name__
                    if error_type not in self.stats['error_patterns']:
                        self.stats['error_patterns'][error_type] = 0
                    self.stats['error_patterns'][error_type] += 1
                    
                    # 添加失败记录
                    error_result = {
                        "sample_index": i,
                        "commit_id_source": sample.get('commit_id_source', 'unknown'),
                        "commit_id_target": sample.get('commit_id_target', 'unknown'),
                        "file_path_source": sample.get('file_path_source', 'unknown'),
                        "file_path_target": sample.get('file_path_target', 'unknown'),
                        "func_before_source": sample.get('func_before_source', ''),
                        "func_after_source": sample.get('func_after_source', ''),
                        "func_before_target": sample.get('func_before_target', ''),
                        "func_after_target": sample.get('func_after_target', ''),
                        "generated_func_after_target": "",
                        "generation_success": False,
                        "generation_time": time.time() - start_time,
                        "prompt_length": 0,
                        "generated_length": 0,
                        "error_message": str(e),
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    detailed_results.append(error_result)
                    simple_results.append("")  # 空结果占位
            
            # 最终总结
            self.logger.info(f"\n✅ 推理完成")
            self.logger.info(f"📊 详细结果文件: {detailed_result_path}")
            self.logger.info(f"📊 兼容格式文件: {simple_result_path}")
            self.logger.info(f"📈 总样本数: {len(detailed_results)}")
            self.logger.info(f"🎯 成功生成: {successful_count}/{len(detailed_results)} ({successful_count/len(detailed_results)*100:.1f}%)")
            
            return str(simple_result_path)  # 返回兼容格式路径
        
        except Exception as e:
            self.logger.error(f"❌ 数据集推理失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise
    
    def run_inference_on_datasets(self, dataset_files: Dict[str, str]) -> Dict[str, str]:
        """
        对多个数据集运行推理
        
        Args:
            dataset_files: 数据集文件路径字典 {size: file_path}
            
        Returns:
            推理结果文件路径字典 {size: result_file_path}
        """
        self.stats['start_time'] = datetime.now()
        self.logger.info("🚀 开始批量推理...")
        
        inference_results = {}
        
        try:
            for size_key, dataset_file in dataset_files.items():
                self.logger.info(f"🔄 处理 {size_key} 数据集...")
                
                try:
                    result_file = self.run_inference_on_dataset(
                        dataset_file, 
                        output_suffix=f"_{size_key}"
                    )
                    inference_results[size_key] = result_file
                    
                except Exception as e:
                    self.logger.error(f"❌ {size_key} 数据集推理失败: {e}")
                    # 继续处理其他数据集
                    continue
            
            # 生成推理统计报告
            self.generate_inference_report()
            
            self.stats['end_time'] = datetime.now()
            self.logger.info("✅ 批量推理完成")
            
            return inference_results
        
        except Exception as e:
            self.logger.error(f"❌ 批量推理失败: {e}")
            raise
    
    def generate_inference_report(self):
        """生成推理统计报告"""
        try:
            # 计算统计指标
            total_time = 0
            if self.stats['start_time']:
                total_time = (datetime.now() - self.stats['start_time']).total_seconds()
            
            avg_time_per_sample = 0
            if self.stats['processing_time_per_sample']:
                avg_time_per_sample = sum(self.stats['processing_time_per_sample']) / len(self.stats['processing_time_per_sample'])
            
            success_rate = 0
            if self.stats['total_samples'] > 0:
                success_rate = self.stats['successful_inferences'] / self.stats['total_samples'] * 100
            
            # 生成报告
            report = {
                'inference_summary': {
                    'start_time': self.stats['start_time'].isoformat() if self.stats['start_time'] else None,
                    'end_time': datetime.now().isoformat(),
                    'total_processing_time_seconds': total_time,
                    'total_samples': self.stats['total_samples'],
                    'successful_inferences': self.stats['successful_inferences'],
                    'failed_inferences': self.stats['failed_inferences'],
                    'success_rate_percentage': success_rate,
                    'average_time_per_sample_seconds': avg_time_per_sample
                },
                'performance_metrics': {
                    'samples_per_second': self.stats['total_samples'] / total_time if total_time > 0 else 0,
                    'min_processing_time': min(self.stats['processing_time_per_sample']) if self.stats['processing_time_per_sample'] else 0,
                    'max_processing_time': max(self.stats['processing_time_per_sample']) if self.stats['processing_time_per_sample'] else 0
                },
                'error_analysis': {
                    'error_patterns': self.stats['error_patterns'],
                    'most_common_error': max(self.stats['error_patterns'].items(), key=lambda x: x[1]) if self.stats['error_patterns'] else None
                },
                'configuration': self.config
            }
            
            # 保存报告
            report_file = self.output_dir / "inference_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📋 推理报告已生成: {report_file}")
            
            # 显示关键统计信息
            self.logger.info(f"📊 推理统计: {self.stats['successful_inferences']}/{self.stats['total_samples']} 成功 ({success_rate:.1f}%)")
            self.logger.info(f"⏱️ 平均处理时间: {avg_time_per_sample:.2f}秒/样本")
            
        except Exception as e:
            self.logger.error(f"❌ 生成推理报告失败: {e}")
    
    async def run_with_original_predict(self, dataset_file: str, model_config: Dict[str, Any]) -> str:
        """
        使用原工具的predict模块运行推理
        
        Args:
            dataset_file: 数据集文件路径
            model_config: 模型配置
            
        Returns:
            推理结果文件路径
        """
        self.logger.info("🔮 使用原工具predict模块运行推理...")
        
        try:
            # 切换到原工具目录
            original_cwd = os.getcwd()
            os.chdir(self.original_tool_dir)
            self.add_original_tool_to_path()
            
            # 导入predict模块
            from predict import generate
            
            # 准备参数
            output_file = self.output_dir / f"{Path(dataset_file).stem}_predict_results.json"
            
            # 调用原工具的generate函数
            if self.config.get('use_api_service', True):
                # 使用API服务
                await generate(
                    base_model_name_or_path=model_config.get('base_model_path', 'bigcode/starcoder'),
                    data_path=dataset_file,
                    output_path=str(output_file),
                    api_url=self.config['api_base_url'] + "/v1/completions",
                    model_max_length=model_config.get('max_length', 4096),
                    max_length=model_config.get('max_length', 4096)
                )
            else:
                # 使用本地模型
                generate(
                    base_model_name_or_path=model_config['base_model_path'],
                    data_path=dataset_file,
                    output_path=str(output_file),
                    peft_output=model_config.get('peft_path'),
                    model_max_length=model_config.get('max_length', 4096),
                    max_length=model_config.get('max_length', 4096),
                    device=model_config.get('device', 'cuda')
                )
            
            self.logger.info(f"✅ 原工具推理完成: {output_file}")
            return str(output_file)
        
        except Exception as e:
            self.logger.error(f"❌ 原工具推理失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise
        
        finally:
            # 恢复工作目录和路径
            os.chdir(original_cwd)
            if str(self.original_tool_dir) in sys.path:
                sys.path.remove(str(self.original_tool_dir))


def main():
    """主程序入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="推理管道 - 集成原工具predict功能")
    parser.add_argument("--workspace", "-w", 
                       default="/home/<USER>/nas-files/Re-paper/ppathf",
                       help="工作空间根目录")
    parser.add_argument("--data-files", "-d", nargs='+', 
                       help="数据文件路径列表")
    parser.add_argument("--output-dir", "-o", 
                       help="输出目录路径")
    parser.add_argument("--api-url", 
                       default="http://************:5002",
                       help="单个API服务地址（向后兼容）")
    parser.add_argument("--api-urls", nargs='+',
                       help="多个API服务地址列表（支持并发）")
    parser.add_argument("--use-api", action="store_true", 
                       help="使用API服务而非本地模型")
    parser.add_argument("--concurrent", action="store_true",
                       help="启用多API并发处理同一文件")
    
    args = parser.parse_args()
    
    # 创建配置
    if args.api_urls:
        # 使用多个API URLs
        api_urls = args.api_urls
    else:
        # 向后兼容单个API URL
        api_urls = [args.api_url]
    
    config = {
        'api_base_url': args.api_url,  # 向后兼容
        'api_urls': api_urls,  # 新的多API支持
        'api_model_name': 'starcoder-lora',
        'use_api_service': args.use_api,
        'concurrent_mode': args.concurrent
    }
    
    # 创建推理管道
    pipeline = InferencePipeline(
        workspace_root=args.workspace,
        output_dir=args.output_dir or "./inference_output",
        config=config
    )
    
    # 处理数据文件
    if args.data_files:
        print("🔮 启动推理管道...")
        print(f"📁 输出目录: {pipeline.output_dir}")
        print(f"🌐 API服务器数量: {len(api_urls)}")
        
        if args.concurrent and len(api_urls) > 1:
            # 并发模式：多个API处理同一文件
            print(f"🚀 并发模式：{len(api_urls)} 个API并发处理同一文件")
            
            results = {}
            for i, file_path in enumerate(args.data_files):
                print(f"\n🔄 处理文件 {i+1}/{len(args.data_files)}: {file_path}")
                result_file = pipeline.run_concurrent_inference_on_dataset(
                    file_path, 
                    output_suffix=""  # 不添加额外后缀，保持文件命名简洁
                )
                results[f"file_{i}"] = result_file
        else:
            # 传统模式：每个API处理不同文件
            print(f"🔄 传统模式：按文件分配给不同API")
            dataset_files = {f"file_{i}": file_path for i, file_path in enumerate(args.data_files)}
            results = pipeline.run_inference_on_datasets(dataset_files)
        
        print(f"\n✅ 推理完成！")
        for size_key, result_file in results.items():
            print(f"📋 {size_key}: {result_file}")
    else:
        print("❌ 请提供数据文件路径")


if __name__ == "__main__":
    main() 