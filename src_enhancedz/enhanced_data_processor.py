#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版数据处理器 - 基于原工具核心功能的自建数据处理管道

核心功能：
1. 调用已有的数据适配器处理自建数据
2. 使用原工具的reduction模块进行函数级分析
3. 应用原工具的数据预处理流程（长度过滤、日期过滤等）
4. 生成符合原工具标准的各种格式数据集
5. 保持与原工具完全兼容，不修改核心功能

技术要点：
- 继承和封装原工具功能，不直接修改
- 智能路径管理，处理不同工作目录切换
- 完整的错误处理和日志记录
- 支持断点续传和增量处理

作者：AI助手
创建时间：2025年1月
版本：v1.0 (增强版数据处理器)
"""

import os
import sys
import json
import logging
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import asyncio
import traceback

class EnhancedDataProcessor:
    """
    增强版数据处理器
    
    基于原工具核心功能，处理自建数据集的完整流程
    """
    
    def __init__(self, 
                 workspace_root: str = "/home/<USER>/nas-files/Re-paper/ppathf",
                 custom_data_path: str = None,
                 output_base_dir: str = None):
        """
        初始化增强版数据处理器
        
        Args:
            workspace_root: 工作空间根目录
            custom_data_path: 自建数据路径（可选）
            output_base_dir: 输出基础目录（可选）
        """
        # 路径配置
        self.workspace_root = Path(workspace_root)
        self.src_enhanced_dir = self.workspace_root / "src_enhancedz"
        self.reproduction_work_dir = self.workspace_root / "reproduction_work"
        self.original_tool_dir = self.workspace_root / "src" / "PPatHF-main" / "PPatHF-main"
        
        # 数据路径
        self.custom_data_path = custom_data_path or str(self.workspace_root / "data" / "data" / "enhanced_data" / "enhanced_and_nvd_dataset.json")
        self.output_base_dir = Path(output_base_dir) if output_base_dir else self.src_enhanced_dir / "processed_data"
        
        # 确保目录存在
        self.src_enhanced_dir.mkdir(exist_ok=True)
        self.output_base_dir.mkdir(parents=True, exist_ok=True)
        
        # 日志配置
        self.setup_logging()
        
        # 处理统计
        self.stats = {
            'start_time': None,
            'end_time': None,
            'total_records': 0,
            'adapted_records': 0,
            'processed_records': 0,
            'filtered_records': {},
            'generated_files': []
        }
        
        self.logger.info("🚀 增强版数据处理器初始化完成")
        self.logger.info(f"📁 工作空间: {self.workspace_root}")
        self.logger.info(f"📁 输出目录: {self.output_base_dir}")
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.src_enhanced_dir / "enhanced_processing.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('EnhancedDataProcessor')
    
    def _simple_length_filter(self, dataset: List[Dict], max_tokens: int) -> List[Dict]:
        """
        简化的长度过滤（避免tokenizer依赖）
        
        基于字符长度估算，大致对应token数量：
        - 英文: 1 token ≈ 4 字符  
        - 代码: 1 token ≈ 3-4 字符
        
        Args:
            dataset: 数据集
            max_tokens: 最大token数
            
        Returns:
            过滤后的数据集
        """
        self.logger.info(f"#dataset before length filtering: {len(dataset)}")
        
        # 简化的字符到token的估算比例（保守估计）
        char_to_token_ratio = 3.5  # 1 token ≈ 3.5 字符
        max_chars = int(max_tokens * char_to_token_ratio)
        
        filtered_dataset = []
        for sample in dataset:
            # 计算样本的总字符长度
            total_chars = 0
            
            # 主要字段的字符数
            for field in ['func_before_source', 'func_after_source', 'func_before_target']:
                if field in sample and sample[field]:
                    total_chars += len(str(sample[field]))
            
            # 与用户的prompt模板保持一致的长度计算
            prompt_template_overhead = 200  # prompt模板的固定字符数
            total_chars += prompt_template_overhead
            
            # 如果字符数在限制内，保留样本
            if total_chars <= max_chars:
                filtered_dataset.append(sample)
        
        self.logger.info(f"#dataset after length filtering: {len(filtered_dataset)}")
        self.logger.info(f"📏 字符限制: {max_chars} (约 {max_tokens} tokens)")
        
        return filtered_dataset
    
    def add_original_tool_to_path(self):
        """将原工具目录添加到Python路径"""
        original_tool_str = str(self.original_tool_dir)
        if original_tool_str not in sys.path:
            sys.path.insert(0, original_tool_str)
            self.logger.info(f"📦 已添加原工具路径到Python path: {original_tool_str}")
    
    async def run_data_adaptation(self, max_records: Optional[int] = None) -> str:
        """
        步骤1: 运行数据适配
        调用已有的数据适配器将自建数据转换为PPatHF格式
        
        Returns:
            适配后的数据文件路径
        """
        self.logger.info("🔄 开始数据适配步骤...")
        
        try:
            # 切换到reproduction_work目录
            original_cwd = os.getcwd()
            os.chdir(self.reproduction_work_dir)
            
            # 导入数据适配器
            sys.path.insert(0, str(self.reproduction_work_dir))
            from data_adapter import CustomDataAdapter
            
            # 创建适配器实例
            adapter = CustomDataAdapter(
                input_file=self.custom_data_path,
                output_file="data/custom/enhanced_ppathf_adapted.json"
            )
            
            # 执行适配
            await adapter.adapt_dataset(max_records=max_records)
            
            # 获取适配后的文件路径
            adapted_file = self.reproduction_work_dir / "data" / "custom" / "enhanced_ppathf_adapted.json"
            
            if adapted_file.exists():
                self.logger.info(f"✅ 数据适配完成: {adapted_file}")
                self.stats['adapted_records'] = len(json.load(open(adapted_file, 'r')))
                return str(adapted_file)
            else:
                raise FileNotFoundError("适配后的数据文件不存在")
        
        except Exception as e:
            self.logger.error(f"❌ 数据适配失败: {e}")
            raise
        finally:
            # 恢复工作目录
            os.chdir(original_cwd)
            if str(self.reproduction_work_dir) in sys.path:
                sys.path.remove(str(self.reproduction_work_dir))
    
    def apply_original_tool_preprocessing(self, adapted_data_file: str, max_records: Optional[int] = None) -> Dict[str, str]:
        """
        步骤2: 应用原工具的预处理流程
        
        包括：
        - 长度过滤 (2048, 4096, 8192 tokens)
        - 日期过滤
        - 数据格式验证
        
        Args:
            adapted_data_file: 适配后的数据文件路径
            
        Returns:
            生成的各种长度限制数据文件路径字典
        """
        self.logger.info("🔄 开始原工具预处理步骤...")
        
        # 设置max_records用于后续处理
        if max_records:
            self.max_records = max_records
            self.logger.info(f"🔄 设置最大记录数限制: {max_records}")
        
        try:
            # 切换到原工具目录
            original_cwd = os.getcwd()
            os.chdir(self.original_tool_dir)
            self.add_original_tool_to_path()
            
            # 导入原工具模块
            from utils import filter_by_max_length, filter_by_date, DATE_FORMAT
            from config import LLM_PATH, LLM_NAME_PATH_MAPPING
            
            # 读取适配的数据
            with open(adapted_data_file, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            
            self.logger.info(f"📊 加载数据集: {len(dataset)} 条记录")
            self.stats['total_records'] = len(dataset)
            
            # 应用日期过滤（如果数据中有日期字段）
            if 'neovim_committer_date' in dataset[0]:
                # 先标准化日期格式
                self.logger.info("🔄 标准化日期格式...")
                from dateutil import parser as date_parser
                for sample in dataset:
                    if 'neovim_committer_date' in sample:
                        try:
                            # 解析ISO格式的日期
                            dt = date_parser.parse(sample['neovim_committer_date'])
                            # 转换为原工具期望的格式
                            sample['neovim_committer_date'] = dt.strftime('%Y-%m-%d %H:%M:%S%z')
                        except Exception as e:
                            self.logger.warning(f"⚠️ 日期格式转换失败: {sample['neovim_committer_date']}, 错误: {e}")
                
                split_date = "2022-07-01 00:00:00+00:00"  # StarCoder预训练数据截止时间
                self.logger.info(f"📅 应用日期过滤: {split_date}")
                _, dataset = filter_by_date(dataset, split_date, "neovim_committer_date")
                self.logger.info(f"📊 日期过滤后: {len(dataset)} 条记录")
            
            # 生成不同长度限制的数据集 - 使用简化的长度过滤（避免tokenizer依赖）
            length_limits = [2048, 4096, 8192]
            generated_files = {}
            
            # 限制记录数（如果设置了max_records）
            if hasattr(self, 'max_records') and self.max_records:
                limited_dataset = dataset[:self.max_records]
                self.logger.info(f"🔄 限制处理记录数: {len(limited_dataset)}/{len(dataset)}")
                dataset = limited_dataset
            
            for max_length in length_limits:
                self.logger.info(f"🔄 生成 {max_length} tokens 限制的数据集...")
                
                # 使用简化的长度过滤（基于字符长度估算，避免tokenizer依赖）
                filtered_dataset = self._simple_length_filter(dataset.copy(), max_length)
                
                # 保存到增强处理目录
                output_file = self.output_base_dir / f"custom_enhanced_{max_length}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(filtered_dataset, f, indent=2, ensure_ascii=False)
                
                generated_files[str(max_length)] = str(output_file)
                self.stats['filtered_records'][max_length] = len(filtered_dataset)
                self.stats['generated_files'].append(str(output_file))
                
                self.logger.info(f"✅ 生成完成: {output_file} ({len(filtered_dataset)} 条记录)")
            
            # 同时保存未过滤的完整数据集
            full_output_file = self.output_base_dir / "custom_enhanced_all.json"
            with open(full_output_file, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, indent=2, ensure_ascii=False)
            generated_files['all'] = str(full_output_file)
            self.stats['generated_files'].append(str(full_output_file))
            
            self.logger.info("✅ 原工具预处理完成")
            return generated_files
        
        except Exception as e:
            self.logger.error(f"❌ 原工具预处理失败: {e}")
            raise
        finally:
            # 恢复工作目录和路径
            os.chdir(original_cwd)
            if str(self.original_tool_dir) in sys.path:
                sys.path.remove(str(self.original_tool_dir))
    
    def apply_reduction_module(self, data_files: Dict[str, str]) -> Dict[str, str]:
        """
        步骤3: 应用原工具的reduction模块
        
        使用tree-sitter进行函数级代码分析和提取
        
        Args:
            data_files: 预处理后的数据文件路径字典
            
        Returns:
            reduction处理后的数据文件路径字典
        """
        self.logger.info("🔄 开始reduction模块处理...")
        
        try:
            # 切换到原工具目录
            original_cwd = os.getcwd()
            os.chdir(self.original_tool_dir)
            self.add_original_tool_to_path()
            
            # 导入reduction模块
            from reduction.reducer import Reducer
            from reduction.fcu import FunctionCompareUtilities
            
            # 初始化reduction组件
            reducer = Reducer(tolerant=True)
            fcu = FunctionCompareUtilities()
            
            self.logger.info("🔧 Reduction模块初始化成功")
            
            sliced_files = {}
            
            for size_key, data_file in data_files.items():
                self.logger.info(f"🔄 处理 {size_key} 数据集...")
                
                # 读取数据
                with open(data_file, 'r', encoding='utf-8') as f:
                    dataset = json.load(f)
                
                # 应用reduction处理
                processed_dataset = []
                for i, sample in enumerate(dataset):
                    try:
                        # 这里可以添加具体的reduction逻辑
                        # 例如函数标准化、代码切片等
                        processed_sample = sample.copy()
                        
                        # 应用函数标准化（如果有相关字段）
                        for field in ['func_before_source', 'func_after_source', 
                                      'func_before_target', 'func_after_target']:
                            if field in processed_sample and processed_sample[field]:
                                # 应用reducer的标准化功能
                                standardized = reducer.func_standardization(processed_sample[field])
                                processed_sample[f"{field}_sliced"] = standardized
                        
                        processed_dataset.append(processed_sample)
                        
                        if (i + 1) % 10 == 0:
                            self.logger.info(f"  已处理 {i + 1}/{len(dataset)} 个样本")
                    
                    except Exception as e:
                        self.logger.warning(f"⚠️ 样本 {i+1} 处理失败: {e}")
                        if not reducer.tolerant:
                            raise
                        # 容错模式下保留原样本
                        processed_dataset.append(sample)
                
                # 保存处理后的数据
                sliced_output_file = self.output_base_dir / f"custom_enhanced_sliced_{size_key}.json"
                with open(sliced_output_file, 'w', encoding='utf-8') as f:
                    json.dump(processed_dataset, f, indent=2, ensure_ascii=False)
                
                sliced_files[size_key] = str(sliced_output_file)
                self.stats['generated_files'].append(str(sliced_output_file))
                
                self.logger.info(f"✅ 完成 {size_key} 数据集处理: {sliced_output_file}")
            
            self.logger.info("✅ Reduction模块处理完成")
            return sliced_files
        
        except Exception as e:
            self.logger.error(f"❌ Reduction模块处理失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise
        finally:
            # 恢复工作目录和路径
            os.chdir(original_cwd)
            if str(self.original_tool_dir) in sys.path:
                sys.path.remove(str(self.original_tool_dir))
    
    def integrate_with_original_tool(self, processed_files: Dict[str, str]):
        """
        步骤4: 与原工具集成
        
        将处理后的数据复制到原工具数据目录，确保原工具可以直接使用
        
        Args:
            processed_files: 处理后的数据文件路径字典
        """
        self.logger.info("🔄 开始与原工具集成...")
        
        try:
            # 原工具数据目录
            original_data_dir = self.original_tool_dir / "data"
            original_data_dir.mkdir(exist_ok=True)
            
            # 复制文件到原工具目录
            for size_key, file_path in processed_files.items():
                source_file = Path(file_path)
                target_file = original_data_dir / f"custom_enhanced_{size_key}.json"
                
                shutil.copy2(source_file, target_file)
                self.logger.info(f"📋 已复制: {source_file.name} -> {target_file}")
                self.stats['generated_files'].append(str(target_file))
            
            # 生成集成报告
            integration_report = {
                'integration_time': datetime.now().isoformat(),
                'source_directory': str(self.output_base_dir),
                'target_directory': str(original_data_dir),
                'integrated_files': list(processed_files.keys()),
                'total_files_copied': len(processed_files),
                'processing_stats': self.stats
            }
            
            report_file = self.output_base_dir / "integration_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(integration_report, f, indent=2, ensure_ascii=False)
            
            self.logger.info("✅ 与原工具集成完成")
            
        except Exception as e:
            self.logger.error(f"❌ 与原工具集成失败: {e}")
            raise
    
    def generate_processing_report(self) -> str:
        """
        生成处理报告
        
        Returns:
            报告文件路径
        """
        self.stats['end_time'] = datetime.now()
        
        # 计算处理时间
        if self.stats['start_time']:
            processing_time = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            self.stats['processing_time_seconds'] = processing_time
        
        # 生成详细报告
        report = {
            'processing_summary': {
                'start_time': self.stats['start_time'].isoformat() if self.stats['start_time'] else None,
                'end_time': self.stats['end_time'].isoformat(),
                'processing_time_seconds': self.stats.get('processing_time_seconds', 0),
                'total_input_records': self.stats['total_records'],
                'adapted_records': self.stats['adapted_records'],
                'filtered_records_by_length': self.stats['filtered_records']
            },
            'generated_files': self.stats['generated_files'],
            'workflow_steps': [
                '1. 数据适配 - 将自建数据转换为PPatHF格式',
                '2. 原工具预处理 - 长度过滤和日期过滤',
                '3. Reduction模块 - 函数级代码分析',
                '4. 原工具集成 - 数据复制和配置更新'
            ],
            'next_steps': [
                '使用原工具的predict模块进行推理',
                '使用原工具的test模块进行评估',
                '查看生成的metrics和结果分析'
            ]
        }
        
        # 保存报告
        report_file = self.output_base_dir / "processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📋 处理报告已生成: {report_file}")
        return str(report_file)
    
    async def run_full_pipeline(self, max_records: Optional[int] = None) -> str:
        """
        运行完整的增强数据处理管道
        
        Args:
            max_records: 最大处理记录数（用于测试）
            
        Returns:
            处理报告文件路径
        """
        self.stats['start_time'] = datetime.now()
        self.logger.info("🚀 开始完整的增强数据处理管道...")
        
        try:
            # 步骤1: 数据适配
            adapted_file = await self.run_data_adaptation(max_records)
            
            # 步骤2: 原工具预处理
            preprocessed_files = self.apply_original_tool_preprocessing(adapted_file)
            
            # 步骤3: Reduction模块处理
            sliced_files = self.apply_reduction_module(preprocessed_files)
            
            # 步骤4: 与原工具集成
            self.integrate_with_original_tool(sliced_files)
            
            # 生成处理报告
            report_file = self.generate_processing_report()
            
            self.logger.info("🎉 完整的增强数据处理管道执行完成！")
            self.logger.info(f"📋 查看详细报告: {report_file}")
            
            return report_file
        
        except Exception as e:
            self.logger.error(f"❌ 增强数据处理管道执行失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise


async def main():
    """主程序入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="增强版数据处理器 - 基于原工具核心功能")
    parser.add_argument("--workspace", "-w", 
                       default="/home/<USER>/nas-files/Re-paper/ppathf",
                       help="工作空间根目录")
    parser.add_argument("--custom-data", "-d",
                       help="自建数据文件路径")
    parser.add_argument("--output-dir", "-o",
                       help="输出目录路径")
    parser.add_argument("--max-records", "-m", type=int,
                       help="最大处理记录数（用于测试）")
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = EnhancedDataProcessor(
        workspace_root=args.workspace,
        custom_data_path=args.custom_data,
        output_base_dir=args.output_dir
    )
    
    # 执行完整管道
    report_file = await processor.run_full_pipeline(max_records=args.max_records)
    
    print(f"\n🎉 处理完成！")
    print(f"📋 详细报告: {report_file}")
    print(f"📁 输出目录: {processor.output_base_dir}")
    
    return report_file


if __name__ == "__main__":
    asyncio.run(main()) 