 #!/bin/bash

# 多API并发推理脚本 - 支持多个API URL并发处理
# 用法: bash multi_api_concurrent_inference.sh
# 特性: 支持多个API服务器，自动负载均衡，并发处理

echo "======================================"
echo "🚀 多API并发推理系统启动"
echo "开始时间: $(date)"
echo "======================================"

# 工作目录
WORKSPACE="/home/<USER>/nas-files/Re-paper/ppathf"
cd $WORKSPACE

# 多API配置 - 使用可用的API服务器
declare -a API_URLS=(
    "http://10.150.10.76:5001"  # API服务器1 - 健康状态良好
    "http://10.150.10.76:5002"  # API服务器2 - 健康状态良好
    # "http://10.150.10.76:5005"  # 备用API (如需要可启用)
)

# 获取API数量作为并发数
CONCURRENT_WORKERS=${#API_URLS[@]}
echo "🔧 检测到 ${CONCURRENT_WORKERS} 个API服务器"
for i in "${!API_URLS[@]}"; do
    echo "   API-$((i+1)): ${API_URLS[$i]}"
done

# 定义数据文件列表 - 配置为处理all数据集
declare -a DATASETS=(
    # 原始版本和切片版本的all数据集
    #"reproduction_work/data/organized/original/custom_vim_neovim_quality_all.json:original:all"
    "reproduction_work/data/organized/sliced/custom_vim_neovim_quality_all_sliced.json:sliced:all"

    # 如果需要处理其他数据集，取消注释下面的行
    #"reproduction_work/data/organized/original/custom_vim_neovim_quality_2048.json:original:2048"
    #"reproduction_work/data/organized/original/custom_vim_neovim_quality_4096.json:original:4096"
    #"reproduction_work/data/organized/original/custom_vim_neovim_quality_8192.json:original:8192"
    #"reproduction_work/data/organized/sliced/custom_vim_neovim_quality_2048_sliced.json:sliced:2048"
    #"reproduction_work/data/organized/sliced/custom_vim_neovim_quality_4096_sliced.json:sliced:4096"
    #"reproduction_work/data/organized/sliced/custom_vim_neovim_quality_8192_sliced.json:sliced:8192"
)

# 创建主输出目录 - 保持与原来相同的路径结构
MAIN_OUTPUT_DIR="reproduction_work/inference_results"
MAIN_LOG_DIR="reproduction_work/logs"
mkdir -p "$MAIN_OUTPUT_DIR"
mkdir -p "$MAIN_LOG_DIR"

# 进程ID数组，用于跟踪后台任务
declare -a WORKER_PIDS=()

# 任务分配函数
assign_tasks() {
    local dataset_count=${#DATASETS[@]}
    local api_count=${#API_URLS[@]}
    
    echo "📊 任务分配策略:"
    echo "   总数据集: $dataset_count 个"
    echo "   API服务器: $api_count 个"
    echo "   每个API平均处理: $((dataset_count / api_count)) 个数据集"
    echo ""
    
    # 为每个API分配任务
    for i in "${!API_URLS[@]}"; do
        local api_url="${API_URLS[$i]}"
        local worker_id=$((i + 1))
        
        # 计算这个worker应该处理的数据集索引范围
        local start_idx=$((i * dataset_count / api_count))
        local end_idx=$(((i + 1) * dataset_count / api_count))
        
        # 使用统一的输出目录，不创建worker专用子目录
        local worker_output_dir="$MAIN_OUTPUT_DIR"
        local worker_log_dir="$MAIN_LOG_DIR/worker_${worker_id}_api_$((i+1))"
        mkdir -p "$worker_log_dir"
        
        echo "🔄 启动Worker-${worker_id} (API: ${api_url})"
        echo "   处理数据集索引: ${start_idx} 到 $((end_idx-1))"
        echo "   输出目录: $worker_output_dir"
        echo "   日志目录: $worker_log_dir"
        
        # 启动worker（后台运行）
        start_worker "$worker_id" "$api_url" "$start_idx" "$end_idx" "$worker_output_dir" "$worker_log_dir" &
        WORKER_PIDS+=($!)
        
        echo "   Worker-${worker_id} PID: ${WORKER_PIDS[$i]}"
        echo ""
        
        # 短暂延迟，避免同时启动造成资源竞争
        sleep 2
    done
}

# Worker函数 - 处理分配给它的数据集
start_worker() {
    local worker_id="$1"
    local api_url="$2" 
    local start_idx="$3"
    local end_idx="$4"
    local worker_output_dir="$5"
    local worker_log_dir="$6"
    
    local worker_log="$worker_log_dir/worker_${worker_id}_main.log"
    
    {
        echo "======================================"
        echo "🚀 Worker-${worker_id} 开始工作"
        echo "API URL: $api_url"
        echo "开始时间: $(date)"
        echo "======================================"
        
        # 处理分配的数据集
        for ((idx=start_idx; idx<end_idx; idx++)); do
            if [ $idx -lt ${#DATASETS[@]} ]; then
                local dataset_info="${DATASETS[$idx]}"
                process_dataset_with_api "$worker_id" "$api_url" "$dataset_info" "$worker_output_dir" "$worker_log_dir"
            fi
        done
        
        echo "======================================"
        echo "✅ Worker-${worker_id} 完成所有任务"
        echo "结束时间: $(date)"
        echo "======================================"
        
    } >> "$worker_log" 2>&1
}

# 处理单个数据集的函数
process_dataset_with_api() {
    local worker_id="$1"
    local api_url="$2"
    local dataset_info="$3"
    local worker_output_dir="$4"
    local worker_log_dir="$5"
    
    # 解析数据集信息
    IFS=':' read -r data_file data_type size_version <<< "$dataset_info"
    
    echo "🔄 Worker-${worker_id} 处理: $data_type/$size_version"
    echo "数据文件: $data_file"
    echo "API: $api_url"
    echo "开始时间: $(date)"
    
    # 设置输出目录和日志文件 - 统一路径管理
    local specific_output_dir="$worker_output_dir/$data_type"
    local dataset_log="$worker_log_dir/${data_type}_${size_version}.log"
    
    # 确保目录存在
    mkdir -p "$specific_output_dir"
    
    # 运行推理
    echo "🚀 开始推理..."
    python src_enhancedz/inference_pipeline.py \
        --data-files "$data_file" \
        --output-dir "$specific_output_dir" \
        --use-api \
        --api-url "$api_url" \
        > "$dataset_log" 2>&1
    
    local exit_code=$?
    
    # 检查执行结果
    if [ $exit_code -eq 0 ]; then
        echo "✅ Worker-${worker_id} $data_type/$size_version 处理完成"
        echo "📋 结果保存在: $specific_output_dir"
        echo "📝 日志保存在: $dataset_log"
    else
        echo "❌ Worker-${worker_id} $data_type/$size_version 处理失败"
        echo "📝 错误日志: $dataset_log"
    fi
    
    echo "完成时间: $(date)"
    echo ""
}

# 监控函数 - 实时显示所有worker状态
monitor_workers() {
    echo "📊 开始监控所有Worker状态..."
    echo "按 Ctrl+C 停止监控（不会停止worker）"
    echo ""
    
    while true; do
        clear
        echo "======================================"
        echo "🖥️  多API并发推理监控面板"
        echo "更新时间: $(date)"
        echo "======================================"
        
        # 检查每个worker状态
        for i in "${!WORKER_PIDS[@]}"; do
            local pid="${WORKER_PIDS[$i]}"
            local worker_id=$((i + 1))
            local api_url="${API_URLS[$i]}"
            
            if kill -0 "$pid" 2>/dev/null; then
                echo "🟢 Worker-${worker_id} (PID: $pid) - 运行中"
                echo "   API: $api_url"
                
                # 显示最新日志行（如果存在）
                local worker_log="$MAIN_LOG_DIR/worker_${worker_id}_api_$((i+1))/worker_${worker_id}_main.log"
                if [ -f "$worker_log" ]; then
                    local last_line=$(tail -n 1 "$worker_log" 2>/dev/null)
                    if [ -n "$last_line" ]; then
                        echo "   最新: ${last_line:0:60}..."
                    fi
                fi
            else
                echo "🔴 Worker-${worker_id} (PID: $pid) - 已完成或停止"
                echo "   API: $api_url"
            fi
            echo ""
        done
        
        # 检查是否所有worker都完成了
        local running_count=0
        for pid in "${WORKER_PIDS[@]}"; do
            if kill -0 "$pid" 2>/dev/null; then
                ((running_count++))
            fi
        done
        
        echo "📈 运行状态: $running_count/${#WORKER_PIDS[@]} 个Worker运行中"
        
        if [ $running_count -eq 0 ]; then
            echo "🎉 所有Worker已完成！"
            break
        fi
        
        echo "======================================"
        sleep 10
    done
}

# 等待所有worker完成
wait_for_completion() {
    echo "⏳ 等待所有Worker完成..."
    
    for i in "${!WORKER_PIDS[@]}"; do
        local pid="${WORKER_PIDS[$i]}"
        local worker_id=$((i + 1))
        
        echo "等待Worker-${worker_id} (PID: $pid)..."
        wait "$pid"
        local exit_code=$?
        
        if [ $exit_code -eq 0 ]; then
            echo "✅ Worker-${worker_id} 成功完成"
        else
            echo "❌ Worker-${worker_id} 异常退出 (代码: $exit_code)"
        fi
    done
}

# 生成汇总报告
generate_summary_report() {
    echo ""
    echo "======================================"
    echo "📊 生成汇总报告"
    echo "======================================"
    
    local summary_file="$MAIN_OUTPUT_DIR/concurrent_inference_summary.json"
    local summary_md="$MAIN_OUTPUT_DIR/concurrent_inference_summary.md"
    
    {
        echo "# 多API并发推理汇总报告"
        echo ""
        echo "- **开始时间**: $(date)"
        echo "- **API服务器数量**: ${#API_URLS[@]}"
        echo "- **数据集总数**: ${#DATASETS[@]}"
        echo "- **并发Worker数**: ${#WORKER_PIDS[@]}"
        echo ""
        echo "## API服务器列表"
        for i in "${!API_URLS[@]}"; do
            echo "- API-$((i+1)): ${API_URLS[$i]}"
        done
        echo ""
        echo "## Worker输出目录"
        for i in "${!API_URLS[@]}"; do
            local worker_id=$((i + 1))
            echo "- Worker-${worker_id}: $MAIN_OUTPUT_DIR/worker_${worker_id}_api_$((i+1))/"
        done
        echo ""
        echo "## 结果文件"
        echo '```'
        find "$MAIN_OUTPUT_DIR" -name "*.json" -type f | sort
        echo '```'
        echo ""
        echo "## 日志文件"
        echo '```'
        find "$MAIN_LOG_DIR" -name "*.log" -type f | sort
        echo '```'
    } > "$summary_md"
    
    echo "📋 汇总报告已生成:"
    echo "   Markdown: $summary_md"
    echo "   查看结果: cat $summary_md"
}

# 生成并发处理汇总报告
generate_concurrent_summary_report() {
    echo ""
    echo "======================================"
    echo "📊 生成并发处理汇总报告"
    echo "======================================"
    
    local summary_file="$MAIN_OUTPUT_DIR/concurrent_processing_summary.md"
    
    {
        echo "# 多API并发推理汇总报告"
        echo ""
        echo "- **处理时间**: $(date)"
        echo "- **处理模式**: 并发模式 (多API处理同一文件)"
        echo "- **API服务器数量**: ${#API_URLS[@]}"
        echo "- **数据集总数**: ${#DATASETS[@]}"
        echo ""
        
        echo "## API服务器配置"
        for i in "${!API_URLS[@]}"; do
            echo "- API-$((i+1)): ${API_URLS[$i]}"
        done
        echo ""
        
        echo "## 处理的数据集"
        for dataset_info in "${DATASETS[@]}"; do
            IFS=':' read -r data_file data_type size_version <<< "$dataset_info"
            echo "- **$data_type/$size_version**: $data_file"
        done
        echo ""
        
        echo "## 输出文件结构"
        echo '```'
        echo "reproduction_work/inference_results/"
        for dataset_info in "${DATASETS[@]}"; do
            IFS=':' read -r data_file data_type size_version <<< "$dataset_info"
            echo "├── $data_type/"
            if [ -d "$MAIN_OUTPUT_DIR/$data_type" ]; then
                find "$MAIN_OUTPUT_DIR/$data_type" -name "*.json" -type f | sed 's|.*\/||' | sed 's/^/│   ├── /'
            fi
        done
        echo '```'
        echo ""
        
        echo "## 日志文件"
        echo '```'
        echo "reproduction_work/logs/"
        for dataset_info in "${DATASETS[@]}"; do
            IFS=':' read -r data_file data_type size_version <<< "$dataset_info"
            echo "├── $data_type/"
            if [ -d "$MAIN_LOG_DIR/$data_type" ]; then
                find "$MAIN_LOG_DIR/$data_type" -name "*.log" -type f | sed 's|.*\/||' | sed 's/^/│   ├── /'
            fi
        done
        echo '```'
        echo ""
        
        echo "## 快速查看命令"
        echo '```bash'
        echo "# 查看所有生成的结果文件"
        echo "find $MAIN_OUTPUT_DIR -name '*_inference_results.json' -type f"
        echo ""
        echo "# 查看详细结果文件"
        echo "find $MAIN_OUTPUT_DIR -name '*_detailed_inference_results.json' -type f"
        echo ""
        echo "# 查看处理日志"
        echo "find $MAIN_LOG_DIR -name '*.log' -type f"
        echo '```'
        
    } > "$summary_file"
    
    echo "📋 汇总报告已生成: $summary_file"
    echo ""
    echo "🔍 快速查看结果:"
    echo "   所有结果文件:"
    find "$MAIN_OUTPUT_DIR" -name "*_inference_results.json" -type f 2>/dev/null | head -5
    echo ""
    echo "   详细结果文件:"
    find "$MAIN_OUTPUT_DIR" -name "*_detailed_inference_results.json" -type f 2>/dev/null | head -5
    echo ""
    echo "📝 查看完整报告: cat $summary_file"
}

# 新功能：多API并发处理同一数据文件
process_single_file_concurrent() {
    local dataset_info="$1"
    
    # 解析数据集信息
    IFS=':' read -r data_file data_type size_version <<< "$dataset_info"
    
    echo "======================================"
    echo "🚀 多API并发处理单一文件模式"
    echo "数据文件: $data_file"
    echo "数据类型: $data_type"
    echo "大小版本: $size_version"
    echo "使用API: ${#API_URLS[@]} 个"
    echo "======================================"
    
    # 创建输出目录 - 保持与原来相同的路径结构
    local concurrent_output_dir="$MAIN_OUTPUT_DIR/$data_type"
    local concurrent_log_dir="$MAIN_LOG_DIR/$data_type"
    mkdir -p "$concurrent_output_dir"
    mkdir -p "$concurrent_log_dir"
    
    # 构建API URLs参数
    local api_urls_args=""
    for api_url in "${API_URLS[@]}"; do
        api_urls_args="$api_urls_args $api_url"
    done
    
    # 运行并发推理 - 统一日志命名
    local concurrent_log="$concurrent_log_dir/concurrent_inference_${size_version}.log"
    
    echo "🚀 开始多API并发推理..."
    echo "📝 日志文件: $concurrent_log"
    echo "📁 所有结果将保存在: $concurrent_output_dir"
    
    python src_enhancedz/inference_pipeline.py \
        --data-files "$data_file" \
        --output-dir "$concurrent_output_dir" \
        --use-api \
        --api-urls $api_urls_args \
        --concurrent \
        > "$concurrent_log" 2>&1
    
    local exit_code=$?
    
    # 检查执行结果
    if [ $exit_code -eq 0 ]; then
        echo "✅ 多API并发处理完成"
        echo "📋 结果保存在: $concurrent_output_dir"
        echo "📝 日志保存在: $concurrent_log"
        
        # 显示结果统计
        if [ -f "$concurrent_log" ]; then
            echo "📊 处理统计:"
            grep -E "(总样本数|成功生成|各API统计)" "$concurrent_log" | tail -5
        fi
    else
        echo "❌ 多API并发处理失败"
        echo "📝 错误日志: $concurrent_log"
        if [ -f "$concurrent_log" ]; then
            echo "❌ 最后几行错误信息:"
            tail -10 "$concurrent_log"
        fi
    fi
    
    echo "完成时间: $(date)"
    echo ""
}

# 主执行流程
main() {
    # 检查API可用性
    echo "🔍 检查API服务器可用性..."
    for i in "${!API_URLS[@]}"; do
        local api_url="${API_URLS[$i]}"
        echo -n "测试 API-$((i+1)) ($api_url)... "
        
        if curl -s --connect-timeout 5 "$api_url/health" >/dev/null 2>&1 || \
           curl -s --connect-timeout 5 "$api_url" >/dev/null 2>&1; then
            echo "✅ 可用"
        else
            echo "⚠️ 无响应 (将继续尝试)"
        fi
    done
    echo ""
    
    # 选择处理模式
    echo "请选择处理模式:"
    echo "1) 传统模式: 不同API处理不同数据文件"
    echo "2) 并发模式: 多个API并发处理同一数据文件"
    echo "3) 重试模式: 重新处理已有结果中的失败记录"
    read -p "请输入选择 (1-3): " processing_mode
    
    case $processing_mode in
        2)
            echo "🚀 启动并发模式..."

            # 并发处理每个数据文件
            for dataset_info in "${DATASETS[@]}"; do
                process_single_file_concurrent "$dataset_info"
            done

            echo "🎉 所有文件并发处理完成！"
            echo "📁 主输出目录: $MAIN_OUTPUT_DIR"
            echo "📝 主日志目录: $MAIN_LOG_DIR"

            # 生成并发处理汇总报告
            generate_concurrent_summary_report
            return 0
            ;;
        3)
            echo "🔄 启动重试模式..."
            process_retry_mode
            return 0
            ;;
        1|*)
            echo "🔄 启动传统模式..."
            ;;
    esac
    
    # 分配并启动任务 (传统模式)
    assign_tasks
    
    # 选择监控方式
    echo "请选择监控方式:"
    echo "1) 实时监控面板 (推荐)"
    echo "2) 后台运行，仅等待完成"
    echo "3) 后台运行，立即退出"
    read -p "请输入选择 (1-3): " monitor_choice
    
    case $monitor_choice in
        1)
            monitor_workers
            ;;
        2)
            wait_for_completion
            ;;
        3)
            echo "🏃 后台运行中，PID列表: ${WORKER_PIDS[*]}"
            echo "💡 使用以下命令监控: ps aux | grep inference_pipeline"
            exit 0
            ;;
        *)
            echo "默认选择: 等待完成"
            wait_for_completion
            ;;
    esac
    
    # 生成汇总报告
    generate_summary_report
    
    echo ""
    echo "🎉 多API并发推理完成！"
    echo "📁 主输出目录: $MAIN_OUTPUT_DIR"
    echo "📝 主日志目录: $MAIN_LOG_DIR"
}

# 信号处理 - 优雅停止所有worker
cleanup() {
    echo ""
    echo "🛑 接收到停止信号，正在停止所有Worker..."
    
    for pid in "${WORKER_PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            echo "停止 PID: $pid"
            kill -TERM "$pid"
        fi
    done
    
    # 等待进程优雅退出
    sleep 5
    
    # 强制杀死仍在运行的进程
    for pid in "${WORKER_PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            echo "强制停止 PID: $pid"
            kill -KILL "$pid"
        fi
    done
    
    echo "🏁 所有Worker已停止"
    exit 1
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 重试模式处理函数
process_retry_mode() {
    echo "🔄 重试模式：重新处理失败的记录"

    # 查找所有结果文件
    local result_files=()
    echo "🔍 搜索结果文件..."

    # 在输出目录中查找结果文件
    if [ -d "$MAIN_OUTPUT_DIR" ]; then
        while IFS= read -r -d '' file; do
            result_files+=("$file")
        done < <(find "$MAIN_OUTPUT_DIR" -name "*_detailed_results_*.json" -print0)
    fi

    if [ ${#result_files[@]} -eq 0 ]; then
        echo "❌ 未找到结果文件！"
        echo "   请确保输出目录存在结果文件: $MAIN_OUTPUT_DIR"
        return 1
    fi

    echo "📋 找到 ${#result_files[@]} 个结果文件:"
    for file in "${result_files[@]}"; do
        echo "   - $(basename "$file")"
    done

    # 确认是否继续
    echo ""
    read -p "是否继续重试这些文件中的失败记录？(y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "❌ 用户取消操作"
        return 0
    fi

    # 创建重试日志目录
    local retry_log_dir="$MAIN_LOG_DIR/retry_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$retry_log_dir"

    echo "🚀 开始重试处理..."
    echo "📝 重试日志目录: $retry_log_dir"

    # 构建结果文件参数
    local result_files_args=""
    for file in "${result_files[@]}"; do
        result_files_args="$result_files_args \"$file\""
    done

    # 执行重试
    local retry_log="$retry_log_dir/retry_main.log"
    local retry_cmd="python src_enhancedz/inference_pipeline.py \
        --workspace \"$WORKSPACE_ROOT\" \
        --api-urls ${API_URLS[*]} \
        --output-dir \"$MAIN_OUTPUT_DIR\" \
        --retry-failed \
        --result-files $result_files_args"

    echo "🔧 执行命令: $retry_cmd"
    echo "📝 日志文件: $retry_log"

    # 执行重试命令
    eval "$retry_cmd" 2>&1 | tee "$retry_log"
    local exit_code=${PIPESTATUS[0]}

    if [ $exit_code -eq 0 ]; then
        echo "✅ 重试处理完成！"
        echo "📝 详细日志: $retry_log"

        # 统计重试结果
        echo ""
        echo "📊 重试统计:"
        grep -E "(总失败记录|重试成功|仍然失败|更新文件)" "$retry_log" || echo "   (统计信息请查看日志文件)"

    else
        echo "❌ 重试处理失败！退出码: $exit_code"
        echo "📝 错误日志: $retry_log"
        return $exit_code
    fi
}

# 启动主程序
main