#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评估管道 - 使用原工具的test和metrics模块进行结果评估

核心功能：
1. 调用原工具的test模块计算各种指标
2. 使用原工具的metrics模块进行详细分析
3. 支持多种评估配置和参数
4. 生成详细的评估报告和可视化
5. 提供与原论文结果的对比分析

技术要点：
- 完全兼容原工具的评估框架
- 支持多种指标：exact_match、AED、RED等
- 智能的错误分析和问题诊断
- 详细的统计报告和可视化图表

作者：AI助手
创建时间：2025年1月
版本：v1.0 (评估管道)
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import traceback
import pandas as pd

class EvaluationPipeline:
    """
    评估管道
    
    使用原工具的评估框架对推理结果进行全面评估
    """
    
    def __init__(self, 
                 workspace_root: str,
                 output_dir: str,
                 config: Dict[str, Any]):
        """
        初始化评估管道
        
        Args:
            workspace_root: 工作空间根目录
            output_dir: 输出目录
            config: 评估配置
        """
        self.workspace_root = Path(workspace_root)
        self.output_dir = Path(output_dir)
        self.config = config
        
        # 原工具路径
        self.original_tool_dir = self.workspace_root / "src" / "PPatHF-main" / "PPatHF-main"
        
        # 确保目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 评估统计
        self.stats = {
            'total_evaluations': 0,
            'successful_evaluations': 0,
            'failed_evaluations': 0,
            'start_time': None,
            'end_time': None,
            'evaluation_results': {},
            'error_patterns': {}
        }
        
        self.logger.info("📊 评估管道初始化完成")
        self.logger.info(f"📁 输出目录: {self.output_dir}")
        self.logger.info(f"⚙️ 配置: {self.config}")
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / "evaluation.log"
        
        # 创建logger
        self.logger = logging.getLogger('EvaluationPipeline')
        self.logger.setLevel(logging.INFO)
        
        # 避免重复添加handlers
        if not self.logger.handlers:
            # 文件handler
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def add_original_tool_to_path(self):
        """将原工具目录添加到Python路径"""
        original_tool_str = str(self.original_tool_dir)
        if original_tool_str not in sys.path:
            sys.path.insert(0, original_tool_str)
            self.logger.info(f"📦 已添加原工具路径到Python path: {original_tool_str}")
    
    def prepare_evaluation_data(self, 
                              inference_results_file: str, 
                              original_data_file: str) -> Tuple[List[str], List[Dict[str, Any]]]:
        """
        准备评估数据
        
        Args:
            inference_results_file: 推理结果文件
            original_data_file: 原始数据文件
            
        Returns:
            (生成结果列表, 测试数据列表)
        """
        try:
            # 读取推理结果
            with open(inference_results_file, 'r', encoding='utf-8') as f:
                inference_results = json.load(f)
            
            # 读取原始数据
            with open(original_data_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            # 提取生成的文本
            generations = []
            test_data = []
            
            for i, result in enumerate(inference_results):
                # 获取生成文本
                generated_text = result.get('generated_text', '')
                generations.append(generated_text)
                
                # 获取对应的原始数据
                if i < len(original_data):
                    test_data.append(original_data[i])
                else:
                    # 如果推理结果比原始数据多，使用推理结果中的数据
                    test_sample = {k: v for k, v in result.items() if k != 'generated_text'}
                    test_data.append(test_sample)
            
            self.logger.info(f"📊 准备评估数据: {len(generations)} 个生成结果")
            return generations, test_data
        
        except Exception as e:
            self.logger.error(f"❌ 准备评估数据失败: {e}")
            raise
    
    def run_original_tool_evaluation(self, 
                                   data_path: str, 
                                   output_path: str, 
                                   inference_results_file: str) -> Dict[str, Any]:
        """
        使用原工具的评估模块进行评估
        
        Args:
            data_path: 原始数据文件路径
            output_path: 推理结果文件路径
            inference_results_file: 推理结果文件路径
            
        Returns:
            评估结果字典
        """
        try:
            # 切换到原工具目录
            original_cwd = os.getcwd()
            os.chdir(self.original_tool_dir)
            self.add_original_tool_to_path()
            
            # 导入原工具的评估模块
            from test import cal_metrics
            from metrics import MetricsSampleWise
            
            self.logger.info("🔧 使用原工具评估模块...")
            
            # 准备评估数据
            generations, test_data = self.prepare_evaluation_data(inference_results_file, data_path)
            
            # 设置评估参数
            do_recover = self.config.get('enable_recovery', True)
            reference_key = 'func_after_target'
            reference_before_key = 'func_before_target'
            
            # 如果是sliced数据，使用origin字段
            if 'sliced' in Path(data_path).name:
                reference_key += '_origin'
                reference_before_key += '_origin'
            
            # 创建MetricsSampleWise实例
            metrics_calculator = MetricsSampleWise(
                generations=generations,
                test_set=test_data,
                do_postprocess=True,
                key_str_before_target="### Function After (neovim):\n",
                key_str_after_target=["### Function", "\n\n\n"],
                do_recover=do_recover,
                reference_key=reference_key,
                reference_before_key=reference_before_key
            )
            
            # 计算样本级指标
            sample_wise_results = metrics_calculator.get_sample_wise_test_results()
            
            # 计算总体指标
            overall_metrics = metrics_calculator.get_overall_test_results()
            
            # 保存样本级结果
            sample_results_file = self.output_dir / f"{Path(data_path).stem}_sample_results.json"
            with open(sample_results_file, 'w', encoding='utf-8') as f:
                json.dump(sample_wise_results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 原工具评估完成")
            self.logger.info(f"📊 总体指标: {overall_metrics}")
            
            return {
                'overall_metrics': overall_metrics,
                'sample_wise_results': sample_wise_results,
                'sample_results_file': str(sample_results_file),
                'evaluation_config': {
                    'do_recover': do_recover,
                    'reference_key': reference_key,
                    'reference_before_key': reference_before_key
                }
            }
        
        except Exception as e:
            self.logger.error(f"❌ 原工具评估失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise
        
        finally:
            # 恢复工作目录和路径
            os.chdir(original_cwd)
            if str(self.original_tool_dir) in sys.path:
                sys.path.remove(str(self.original_tool_dir))
    
    def analyze_evaluation_results(self, evaluation_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析评估结果
        
        Args:
            evaluation_results: 评估结果字典
            
        Returns:
            分析结果字典
        """
        try:
            overall_metrics = evaluation_results['overall_metrics']
            sample_results = evaluation_results['sample_wise_results']
            
            # 基础统计
            total_samples = len(sample_results)
            exact_matches = sum(1 for r in sample_results if r.get('exact_match', False))
            exact_match_rate = exact_matches / total_samples if total_samples > 0 else 0
            
            # 距离统计
            gen_distances = [r.get('gen_distance', 0) for r in sample_results]
            rel_distances = [r.get('rel_distance', 0) for r in sample_results]
            
            gen_distance_avg = sum(gen_distances) / len(gen_distances) if gen_distances else 0
            rel_distance_avg = sum(rel_distances) / len(rel_distances) if rel_distances else 0
            
            # 成功生成分析
            successful_generations = [r for r in sample_results if not r.get('gen', '').startswith('ERROR')]
            success_rate = len(successful_generations) / total_samples if total_samples > 0 else 0
            
            # 代码质量分析
            code_quality_analysis = self._analyze_code_quality(sample_results)
            
            # 错误模式分析
            error_analysis = self._analyze_error_patterns(sample_results)
            
            analysis = {
                'basic_statistics': {
                    'total_samples': total_samples,
                    'exact_matches': exact_matches,
                    'exact_match_rate': exact_match_rate,
                    'success_rate': success_rate,
                    'average_generation_distance': gen_distance_avg,
                    'average_relative_distance': rel_distance_avg
                },
                'code_quality_analysis': code_quality_analysis,
                'error_analysis': error_analysis,
                'performance_distribution': {
                    'generation_distances': {
                        'min': min(gen_distances) if gen_distances else 0,
                        'max': max(gen_distances) if gen_distances else 0,
                        'median': sorted(gen_distances)[len(gen_distances)//2] if gen_distances else 0
                    },
                    'relative_distances': {
                        'min': min(rel_distances) if rel_distances else 0,
                        'max': max(rel_distances) if rel_distances else 0,
                        'median': sorted(rel_distances)[len(rel_distances)//2] if rel_distances else 0
                    }
                }
            }
            
            return analysis
        
        except Exception as e:
            self.logger.error(f"❌ 分析评估结果失败: {e}")
            return {}
    
    def _analyze_code_quality(self, sample_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析代码质量"""
        try:
            # 分析生成的代码特征
            code_features = {
                'contains_function_definition': 0,
                'contains_braces': 0,
                'contains_semicolon': 0,
                'contains_comments': 0,
                'average_length': 0,
                'empty_generations': 0
            }
            
            total_length = 0
            
            for result in sample_results:
                generated = result.get('gen_processed', result.get('gen', ''))
                
                if not generated or generated.strip() == '':
                    code_features['empty_generations'] += 1
                    continue
                
                total_length += len(generated)
                
                # 检查各种代码特征
                if any(keyword in generated for keyword in ['void ', 'int ', 'char ', 'static ']):
                    code_features['contains_function_definition'] += 1
                
                if '{' in generated and '}' in generated:
                    code_features['contains_braces'] += 1
                
                if ';' in generated:
                    code_features['contains_semicolon'] += 1
                
                if '//' in generated or '/*' in generated:
                    code_features['contains_comments'] += 1
            
            # 计算平均长度
            valid_samples = len(sample_results) - code_features['empty_generations']
            if valid_samples > 0:
                code_features['average_length'] = total_length / valid_samples
            
            # 转换为百分比
            total_samples = len(sample_results)
            for key in ['contains_function_definition', 'contains_braces', 'contains_semicolon', 'contains_comments']:
                if total_samples > 0:
                    code_features[f'{key}_percentage'] = code_features[key] / total_samples * 100
            
            return code_features
        
        except Exception as e:
            self.logger.error(f"❌ 分析代码质量失败: {e}")
            return {}
    
    def _analyze_error_patterns(self, sample_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析错误模式"""
        try:
            error_patterns = {
                'api_errors': 0,
                'empty_generations': 0,
                'malformed_code': 0,
                'django_code_generated': 0,
                'partial_generations': 0
            }
            
            error_samples = []
            
            for i, result in enumerate(sample_results):
                generated = result.get('gen', '')
                
                # 检查各种错误模式
                if generated.startswith('ERROR'):
                    error_patterns['api_errors'] += 1
                    error_samples.append({'index': i, 'type': 'api_error', 'content': generated[:200]})
                
                elif not generated or generated.strip() == '':
                    error_patterns['empty_generations'] += 1
                    error_samples.append({'index': i, 'type': 'empty_generation', 'content': ''})
                
                elif 'django' in generated.lower() or 'models.Model' in generated:
                    error_patterns['django_code_generated'] += 1
                    error_samples.append({'index': i, 'type': 'django_code', 'content': generated[:200]})
                
                elif not ('{' in generated and '}' in generated):
                    error_patterns['partial_generations'] += 1
                    error_samples.append({'index': i, 'type': 'partial_generation', 'content': generated[:200]})
            
            return {
                'error_counts': error_patterns,
                'error_samples': error_samples[:10],  # 只保留前10个错误样本
                'total_errors': sum(error_patterns.values())
            }
        
        except Exception as e:
            self.logger.error(f"❌ 分析错误模式失败: {e}")
            return {}
    
    async def run_evaluation_on_results(self, 
                                       inference_results: Dict[str, str], 
                                       data_files: Dict[str, str]) -> Dict[str, str]:
        """
        对推理结果进行评估
        
        Args:
            inference_results: 推理结果文件路径字典
            data_files: 原始数据文件路径字典
            
        Returns:
            评估结果文件路径字典
        """
        self.stats['start_time'] = datetime.now()
        self.logger.info("📊 开始批量评估...")
        
        evaluation_results = {}
        
        try:
            for size_key in inference_results:
                if size_key not in data_files:
                    self.logger.warning(f"⚠️ 未找到 {size_key} 对应的原始数据文件，跳过")
                    continue
                
                self.logger.info(f"🔄 评估 {size_key} 数据集...")
                
                try:
                    # 运行评估
                    eval_result = self.run_original_tool_evaluation(
                        data_path=data_files[size_key],
                        output_path=inference_results[size_key],
                        inference_results_file=inference_results[size_key]
                    )
                    
                    # 分析结果
                    analysis = self.analyze_evaluation_results(eval_result)
                    eval_result['analysis'] = analysis
                    
                    # 保存评估结果
                    eval_output_file = self.output_dir / f"{size_key}_evaluation_results.json"
                    with open(eval_output_file, 'w', encoding='utf-8') as f:
                        json.dump(eval_result, f, indent=2, ensure_ascii=False, default=str)
                    
                    evaluation_results[size_key] = str(eval_output_file)
                    self.stats['successful_evaluations'] += 1
                    self.stats['evaluation_results'][size_key] = eval_result['overall_metrics']
                    
                    self.logger.info(f"✅ {size_key} 评估完成: {eval_output_file}")
                
                except Exception as e:
                    self.logger.error(f"❌ {size_key} 评估失败: {e}")
                    self.stats['failed_evaluations'] += 1
                    # 记录错误模式
                    error_type = type(e).__name__
                    if error_type not in self.stats['error_patterns']:
                        self.stats['error_patterns'][error_type] = 0
                    self.stats['error_patterns'][error_type] += 1
                    continue
            
            # 生成综合评估报告
            comprehensive_report = self.generate_comprehensive_report()
            
            self.stats['end_time'] = datetime.now()
            self.logger.info("✅ 批量评估完成")
            
            return evaluation_results
        
        except Exception as e:
            self.logger.error(f"❌ 批量评估失败: {e}")
            raise
    
    def generate_comprehensive_report(self) -> str:
        """生成综合评估报告"""
        try:
            # 计算总体统计
            end_time = datetime.now()
            total_time = 0
            if self.stats['start_time']:
                total_time = (end_time - self.stats['start_time']).total_seconds()
            
            # 汇总各数据集的结果
            summary_metrics = {}
            for size_key, metrics in self.stats['evaluation_results'].items():
                summary_metrics[size_key] = {
                    'exact_match_rate': metrics.get('exact_match_rate', 0),
                    'average_generation_distance': metrics.get('average_generation_distance', 0),
                    'average_relative_distance': metrics.get('average_relative_distance', 0)
                }
            
            # 生成综合报告
            comprehensive_report = {
                'evaluation_summary': {
                    'start_time': self.stats['start_time'].isoformat() if self.stats['start_time'] else None,
                    'end_time': end_time.isoformat(),
                    'total_evaluation_time_seconds': total_time,
                    'successful_evaluations': self.stats['successful_evaluations'],
                    'failed_evaluations': self.stats['failed_evaluations'],
                    'total_datasets_evaluated': len(self.stats['evaluation_results'])
                },
                'cross_dataset_comparison': summary_metrics,
                'performance_insights': self._generate_performance_insights(summary_metrics),
                'recommendations': self._generate_recommendations(summary_metrics),
                'error_analysis': {
                    'error_patterns': self.stats['error_patterns'],
                    'error_rate': self.stats['failed_evaluations'] / (self.stats['successful_evaluations'] + self.stats['failed_evaluations']) if (self.stats['successful_evaluations'] + self.stats['failed_evaluations']) > 0 else 0
                },
                'configuration': self.config
            }
            
            # 保存综合报告
            report_file = self.output_dir / "comprehensive_evaluation_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)
            
            # 生成简化的Markdown报告
            self._generate_markdown_report(comprehensive_report, report_file.with_suffix('.md'))
            
            self.logger.info(f"📋 综合评估报告已生成: {report_file}")
            return str(report_file)
        
        except Exception as e:
            self.logger.error(f"❌ 生成综合评估报告失败: {e}")
            return ""
    
    def _generate_performance_insights(self, summary_metrics: Dict[str, Dict[str, float]]) -> List[str]:
        """生成性能洞察"""
        insights = []
        
        try:
            if not summary_metrics:
                return ["无可用数据进行分析"]
            
            # 找出表现最好的数据集
            best_dataset = max(summary_metrics.keys(), 
                             key=lambda k: summary_metrics[k].get('exact_match_rate', 0))
            best_rate = summary_metrics[best_dataset].get('exact_match_rate', 0)
            
            insights.append(f"表现最佳的数据集: {best_dataset} (精确匹配率: {best_rate:.2%})")
            
            # 分析长度限制的影响
            if len(summary_metrics) > 1:
                rates = [(k, v.get('exact_match_rate', 0)) for k, v in summary_metrics.items()]
                rates.sort(key=lambda x: x[1], reverse=True)
                
                insights.append(f"数据集性能排序: {' > '.join([f'{k}({v:.2%})' for k, v in rates])}")
            
            # 分析平均性能
            avg_exact_match = sum(v.get('exact_match_rate', 0) for v in summary_metrics.values()) / len(summary_metrics)
            insights.append(f"平均精确匹配率: {avg_exact_match:.2%}")
            
            # 性能评价
            if avg_exact_match > 0.5:
                insights.append("整体性能表现良好")
            elif avg_exact_match > 0.2:
                insights.append("整体性能中等，有改进空间")
            else:
                insights.append("整体性能较低，需要重点优化")
        
        except Exception as e:
            insights.append(f"性能分析出错: {e}")
        
        return insights
    
    def _generate_recommendations(self, summary_metrics: Dict[str, Dict[str, float]]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        try:
            if not summary_metrics:
                return ["建议检查数据处理流程"]
            
            avg_exact_match = sum(v.get('exact_match_rate', 0) for v in summary_metrics.values()) / len(summary_metrics)
            avg_gen_distance = sum(v.get('average_generation_distance', 0) for v in summary_metrics.values()) / len(summary_metrics)
            
            # 基于性能给出建议
            if avg_exact_match < 0.1:
                recommendations.extend([
                    "精确匹配率过低，建议检查API服务配置",
                    "考虑重新训练或调整模型参数",
                    "检查数据预处理是否正确"
                ])
            
            if avg_gen_distance > 100:
                recommendations.extend([
                    "生成距离过大，建议优化prompt设计",
                    "考虑使用更强的基础模型",
                    "增加训练数据的质量和数量"
                ])
            
            # 数据集特定建议
            if len(summary_metrics) > 1:
                worst_dataset = min(summary_metrics.keys(), 
                                 key=lambda k: summary_metrics[k].get('exact_match_rate', 0))
                recommendations.append(f"重点优化 {worst_dataset} 数据集的处理")
            
            # 通用建议
            recommendations.extend([
                "定期监控API服务状态和性能",
                "收集更多样化的训练数据",
                "考虑使用ensemble方法提高性能"
            ])
        
        except Exception as e:
            recommendations.append(f"建议生成出错: {e}")
        
        return recommendations
    
    def _generate_markdown_report(self, report_data: Dict[str, Any], output_file: Path):
        """生成Markdown格式的报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# 增强版PPatHF评估报告\n\n")
                
                # 评估摘要
                f.write("## 评估摘要\n\n")
                summary = report_data['evaluation_summary']
                f.write(f"- **评估时间**: {summary.get('start_time', 'N/A')} - {summary.get('end_time', 'N/A')}\n")
                f.write(f"- **总耗时**: {summary.get('total_evaluation_time_seconds', 0):.2f} 秒\n")
                f.write(f"- **成功评估**: {summary.get('successful_evaluations', 0)}\n")
                f.write(f"- **失败评估**: {summary.get('failed_evaluations', 0)}\n\n")
                
                # 跨数据集对比
                f.write("## 跨数据集性能对比\n\n")
                comparison = report_data.get('cross_dataset_comparison', {})
                if comparison:
                    f.write("| 数据集 | 精确匹配率 | 平均生成距离 | 平均相对距离 |\n")
                    f.write("|--------|------------|--------------|-------------|\n")
                    for dataset, metrics in comparison.items():
                        f.write(f"| {dataset} | {metrics.get('exact_match_rate', 0):.2%} | {metrics.get('average_generation_distance', 0):.2f} | {metrics.get('average_relative_distance', 0):.2f} |\n")
                    f.write("\n")
                
                # 性能洞察
                f.write("## 性能洞察\n\n")
                insights = report_data.get('performance_insights', [])
                for insight in insights:
                    f.write(f"- {insight}\n")
                f.write("\n")
                
                # 改进建议
                f.write("## 改进建议\n\n")
                recommendations = report_data.get('recommendations', [])
                for rec in recommendations:
                    f.write(f"- {rec}\n")
                f.write("\n")
                
                # 错误分析
                f.write("## 错误分析\n\n")
                error_analysis = report_data.get('error_analysis', {})
                if error_analysis.get('error_patterns'):
                    f.write("### 错误模式\n\n")
                    for error_type, count in error_analysis['error_patterns'].items():
                        f.write(f"- **{error_type}**: {count} 次\n")
                    f.write(f"\n- **总体错误率**: {error_analysis.get('error_rate', 0):.2%}\n\n")
            
            self.logger.info(f"📝 Markdown报告已生成: {output_file}")
        
        except Exception as e:
            self.logger.error(f"❌ 生成Markdown报告失败: {e}")


async def main():
    """主程序入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="评估管道 - 使用原工具test和metrics模块")
    parser.add_argument("--workspace", "-w", 
                       default="/home/<USER>/nas-files/Re-paper/ppathf",
                       help="工作空间根目录")
    parser.add_argument("--inference-results", "-r", nargs='+', 
                       help="推理结果文件路径列表")
    parser.add_argument("--data-files", "-d", nargs='+', 
                       help="原始数据文件路径列表")
    parser.add_argument("--output-dir", "-o", 
                       help="输出目录路径")
    
    args = parser.parse_args()
    
    if not args.inference_results or not args.data_files:
        print("❌ 请提供推理结果文件和原始数据文件路径")
        return
    
    # 创建配置
    config = {
        'enable_recovery': True,
        'tolerant_mode': True
    }
    
    # 创建评估管道
    pipeline = EvaluationPipeline(
        workspace_root=args.workspace,
        output_dir=args.output_dir or "./evaluation_output",
        config=config
    )
    
    # 组织文件
    inference_files = {f"file_{i}": file_path for i, file_path in enumerate(args.inference_results)}
    data_files = {f"file_{i}": file_path for i, file_path in enumerate(args.data_files)}
    
    print("📊 启动评估管道...")
    print(f"📁 输出目录: {pipeline.output_dir}")
    
    results = await pipeline.run_evaluation_on_results(inference_files, data_files)
    
    print(f"\n✅ 评估完成！")
    for size_key, result_file in results.items():
        print(f"📋 {size_key}: {result_file}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 