# 增强版PPatHF处理系统

## 🎯 项目概述

增强版PPatHF处理系统是一个完整的自建数据处理管道，它基于原始PPatHF工具的核心功能，提供了从数据适配到最终评估的完整工作流。

### 核心特性

- 🔧 **原工具兼容**: 完全兼容原PPatHF工具，不破坏原有功能
- 🚀 **完整管道**: 数据适配 → 预处理 → Reduction → 推理 → 评估
- 📊 **智能分析**: 详细的处理统计和性能监控
- 🔄 **断点续传**: 支持中断恢复和增量处理
- 📋 **详细报告**: 生成完整的处理和评估报告

## 📁 文件结构

```
src_enhancedz/
├── enhanced_data_processor.py        # 核心数据处理器
├── workflow_orchestrator.py         # 工作流编排器
├── inference_pipeline.py            # 推理管道
├── evaluation_pipeline.py           # 评估管道
├── batch_create_sliced_datasets.py  # 批量Slicing处理工具
├── test_reduction_compatibility.py  # Reduction兼容性测试工具
├── batch_inference_all.sh           # 批量推理执行脚本
├── README.md                        # 本文档
└── examples/                        # 使用示例
    ├── quick_start.py               # 快速开始示例
    ├── custom_config.json           # 自定义配置示例
    └── batch_processing.py          # 批量处理示例
```

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 确保在正确的工作目录
cd /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz

# 安装依赖
pip install aiohttp pandas transformers

# 确保原工具依赖可用
pip install tree-sitter editdistance unidiff
```

### 2. 基础使用

#### 方式一：使用增强数据处理器（基础功能）

```bash
# 处理小规模数据（测试）
python enhanced_data_processor.py --max-records 10

# 处理完整数据集
python enhanced_data_processor.py
```

#### 方式二：使用工作流编排器（推荐）

```bash
# 运行完整工作流
python workflow_orchestrator.py --max-records 50

# 使用自定义配置
python workflow_orchestrator.py --config custom_config.json

# 跳过某些步骤
python workflow_orchestrator.py --skip-steps data_adaptation preprocessing
```

### 3. 高级使用

#### 单独运行各个模块

```bash
# 只运行推理
python inference_pipeline.py --data-files data1.json data2.json --output-dir ./inference_results

# 只运行评估
python evaluation_pipeline.py --inference-results result1.json --data-files data1.json --output-dir ./evaluation_results
```

## ⚙️ 配置说明

### 工作流配置文件示例 (config.json)

```json
{
    "workspace_root": "/home/<USER>/nas-files/Re-paper/ppathf",
    "custom_data_path": null,
    "output_dir": null,
    "max_records": null,
    "token_limits": [2048, 4096, 8192],
    "use_api_service": true,
    "api_base_url": "http://************:5002",
    "api_model_name": "starcoder-lora",
    "skip_data_adaptation": false,
    "skip_preprocessing": false,
    "skip_reduction": false,
    "skip_inference": false,
    "skip_evaluation": false,
    "enable_slicing": true,
    "enable_recovery": true,
    "tolerant_mode": true
}
```

### 关键参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `workspace_root` | 工作空间根目录 | `/home/<USER>/nas-files/Re-paper/ppathf` |
| `max_records` | 最大处理记录数（测试用） | `None`（处理全部） |
| `token_limits` | 长度过滤限制 | `[2048, 4096, 8192]` |
| `use_api_service` | 使用API服务还是本地模型 | `true` |
| `enable_slicing` | 启用代码切片 | `true` |
| `tolerant_mode` | 容错模式 | `true` |

## 🛠️ 专用工具说明

### `batch_create_sliced_datasets.py` - 批量Slicing处理工具

**功能**: 批量创建Sliced数据集，支持多版本同时处理
- 对2048/4096/8192/all版本数据集进行reduction处理
- 生成符合PPatHF格式的sliced数据集
- 提供详细的处理统计和验证报告

**使用方法**:
```bash
cd /home/<USER>/nas-files/Re-paper/ppathf
python src_enhancedz/batch_create_sliced_datasets.py
```

**特点**:
- ✅ 自动批量处理多个数据集版本
- ✅ 实时进度显示和统计报告
- ✅ 完整的格式验证和兼容性检查
- ✅ 智能错误处理和恢复机制

### `test_reduction_compatibility.py` - Reduction兼容性测试工具

**功能**: 测试自建数据集与PPatHF reduction模块的兼容性
- 验证数据格式是否符合PPatHF要求
- 测试reduction处理是否正常工作
- 生成详细的兼容性测试报告

**使用方法**:
```bash
cd /home/<USER>/nas-files/Re-paper/ppathf
python src_enhancedz/test_reduction_compatibility.py
```

**特点**:
- ✅ 全面的兼容性验证
- ✅ 样本数据适合性评估
- ✅ 详细的测试报告和建议
- ✅ 支持批量样本测试

### `batch_inference_all.sh` - 批量推理执行脚本

**功能**: 一键批量处理所有数据集的推理任务
- 自动处理6个数据集文件(original/sliced × 2048/4096/8192)
- 支持后台执行和进度监控
- 自动创建合理的目录结构和日志文件

**使用方法**:
```bash
# 方式1: 前台执行
bash src_enhancedz/batch_inference_all.sh

# 方式2: 后台执行 (推荐)
cd /home/<USER>/nas-files/Re-paper/ppathf
nohup bash src_enhancedz/batch_inference_all.sh > batch_inference_master.log 2>&1 &

# 监控进度
tail -f batch_inference_master.log
```

**输出结构**:
```
reproduction_work/
├── inference_results/
│   ├── original/          # 原始数据集推理结果
│   └── sliced/            # Sliced数据集推理结果
└── logs/
    ├── original/          # 各版本详细日志
    └── sliced/
```

**特点**:
- ✅ 全自动化批量处理
- ✅ 智能错误处理和恢复
- ✅ 详细的进度监控和日志
- ✅ API负载均衡 (任务间隔5秒)

## 🔄 处理流程详解

### 1. 数据适配 (Data Adaptation)

- **功能**: 将自建数据转换为PPatHF标准格式
- **输入**: `enhanced_and_nvd_dataset.json`
- **输出**: `enhanced_ppathf_adapted.json`
- **关键技术**: GitHub API、异步处理、断点续传

### 2. 数据预处理 (Preprocessing)

- **功能**: 使用原工具的长度过滤和日期过滤
- **输入**: 适配后的数据
- **输出**: 多种长度限制的数据集（2048/4096/8192 tokens）
- **关键技术**: 原工具的`utils.py`模块

### 3. Reduction处理

- **功能**: 使用tree-sitter进行函数级代码分析
- **输入**: 预处理后的数据
- **输出**: sliced版本的数据集
- **关键技术**: 原工具的`reduction`模块

### 4. 模型推理 (Inference)

- **功能**: 调用StarCoder API或本地模型进行推理
- **输入**: 处理后的数据集
- **输出**: 推理结果文件
- **关键技术**: 原工具的`predict`模块、API调用

### 5. 结果评估 (Evaluation)

- **功能**: 使用原工具的评估框架计算各种指标
- **输入**: 推理结果和原始数据
- **输出**: 详细的评估报告
- **关键技术**: 原工具的`test`和`metrics`模块

## 📊 输出文件说明

### 数据处理输出

```
src_enhancedz/processed_data/
├── custom_enhanced_2048.json          # 2048 tokens限制的数据集
├── custom_enhanced_4096.json          # 4096 tokens限制的数据集
├── custom_enhanced_8192.json          # 8192 tokens限制的数据集
├── custom_enhanced_all.json           # 完整数据集
├── custom_enhanced_sliced_2048.json   # 切片后的数据集
├── custom_enhanced_sliced_4096.json
├── custom_enhanced_sliced_8192.json
└── processing_report.json             # 处理报告
```

### 推理输出

```
src_enhancedz/workflow_output/inference/
├── custom_enhanced_2048_inference_results.json
├── custom_enhanced_4096_inference_results.json
├── custom_enhanced_8192_inference_results.json
└── inference_report.json              # 推理统计报告
```

### 评估输出

```
src_enhancedz/workflow_output/evaluation/
├── 2048_evaluation_results.json       # 详细评估结果
├── 4096_evaluation_results.json
├── 8192_evaluation_results.json
├── comprehensive_evaluation_report.json    # 综合评估报告
├── comprehensive_evaluation_report.md      # Markdown格式报告
└── evaluation.log                     # 评估日志
```

### 工作流输出

```
src_enhancedz/workflow_output/
├── workflow_state.json                # 工作流状态
├── final_workflow_report.json         # 最终工作流报告
└── workflow.log                       # 工作流日志
```

## 🚨 故障排除

### 常见问题

#### 1. 导入模块失败
```
❌ 错误: ModuleNotFoundError: No module named 'xxx'
✅ 解决: pip install xxx 或检查Python路径配置
```

#### 2. API连接失败
```
❌ 错误: API请求失败: Connection refused
✅ 解决: 检查API服务状态，确认地址和端口正确
```

#### 3. 数据文件不存在
```
❌ 错误: FileNotFoundError: 数据文件未找到
✅ 解决: 检查输入文件路径，确认文件存在
```

#### 4. 内存不足
```
❌ 错误: MemoryError 或 OOM
✅ 解决: 使用--max-records限制处理数量，或增加系统内存
```

### 调试模式

启用详细日志输出：

```bash
# 设置日志级别
export PYTHONPATH=/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
" workflow_orchestrator.py --max-records 5
```

### 性能优化

1. **减少数据量**: 使用`--max-records`参数
2. **跳过步骤**: 使用`--skip-steps`跳过已完成的步骤
3. **并行处理**: 系统内部已优化并行处理
4. **内存管理**: 大文件会自动分批处理

## 🔍 监控和调试

### 实时监控

```bash
# 监控工作流状态
watch -n 5 "cat src_enhancedz/workflow_output/workflow_state.json | jq '.current_step'"

# 监控日志
tail -f src_enhancedz/workflow_output/workflow.log

# 检查进度
ls -la src_enhancedz/workflow_output/*/
```

### 性能分析

```bash
# 查看处理统计
python -c "
import json
with open('src_enhancedz/workflow_output/final_workflow_report.json') as f:
    report = json.load(f)
    print('处理时间:', report['workflow_summary']['total_processing_time_seconds'])
    print('完成步骤:', report['workflow_summary']['completed_steps'])
"
```

## 🔧 自定义扩展

### 添加新的处理步骤

1. 在相应的管道类中添加新方法
2. 更新工作流编排器的步骤定义
3. 添加相应的配置参数

### 集成其他模型

1. 修改`inference_pipeline.py`中的推理逻辑
2. 添加新的模型配置参数
3. 更新评估模块以适应新模型的输出格式

## 📈 最佳实践

### 1. 开发和测试

- 始终先用小数据集测试（`--max-records 10`）
- 检查每个步骤的输出质量
- 定期备份重要的中间结果

### 2. 生产环境

- 使用配置文件管理参数
- 启用断点续传机制
- 监控系统资源使用情况

### 3. 性能优化

- 根据硬件配置调整并发参数
- 定期清理临时文件
- 使用SSD存储提高I/O性能

## 🤝 贡献指南

### 代码规范

1. 遵循PEP 8编码规范
2. 添加详细的文档字符串
3. 包含适当的错误处理
4. 添加日志记录

### 测试要求

1. 单元测试覆盖核心功能
2. 集成测试验证完整流程
3. 性能测试确保效率

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 检查本文档的故障排除部分
2. 查看相关日志文件
3. 描述问题的详细情况和复现步骤

## 📄 许可证

本项目基于原PPatHF工具开发，遵循相同的开源许可证。

---

*最后更新时间: 2025年1月* 