#!/usr/bin/env python3
"""
测试自建数据是否可以进行reduction处理
验证quality_fixed数据集与PPatHF reduction模块的兼容性
"""

import json
import sys
import os

# 添加PPatHF工具路径
sys.path.append('src/PPatHF-main/PPatHF-main')

try:
    from reduction.reducer import Reducer
    from reduction.fcu import FunctionCompareUtilities
    print("✅ 成功导入PPatHF reduction模块!")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_single_sample_reduction(sample, sample_id):
    """测试单个样本的reduction效果"""
    print(f"\n=== 测试样本 {sample_id} ===")
    
    func_before = sample['func_before_source']
    print(f"原始长度: {len(func_before)} 字符")
    print(f"原始行数: {func_before.count(chr(10))}")
    
    # 初始化reducer
    reducer = Reducer(tolerant=True)
    fcu = FunctionCompareUtilities()
    
    try:
        # 测试基础功能
        print("🔧 测试预处理函数...")
        
        # 1. 移除预处理指令
        no_preproc = reducer.remove_preprocs(func_before)
        print(f"移除预处理后长度: {len(no_preproc)} 字符")
        
        # 2. 函数标准化
        standardized = reducer.func_standardization(func_before)
        print(f"标准化后长度: {len(standardized)} 字符")
        
        # 3. tree-sitter解析
        try:
            code_bytes = func_before.encode('utf-8')
            tree = fcu.parse(code_bytes)
            print("✅ tree-sitter解析成功")
            
            # 获取token
            tokens = fcu.get_cleaned_tokens(func_before)
            print(f"提取token数量: {len(tokens)}")
            
        except Exception as e:
            print(f"⚠️ tree-sitter解析失败: {e}")
        
        # 预览内容变化
        print("\n原始内容预览:")
        print(func_before[:200].replace('\n', '\\n'))
        print("\n标准化后预览:")
        print(standardized[:200].replace('\n', '\\n'))
        
        return True
        
    except Exception as e:
        print(f"❌ reduction测试失败: {e}")
        return False

def analyze_data_suitability():
    """分析数据是否适合reduction处理"""
    print("🔍 分析数据适合度...")
    
    # 读取质量修复后的数据
    quality_path = 'reproduction_work/data/quality_fixed/custom_vim_neovim_quality_4096.json'
    
    try:
        with open(quality_path, 'r') as f:
            quality_data = json.load(f)
        
        print(f"📊 加载数据: {len(quality_data)} 个样本")
        
        # 统计数据特征
        stats = {
            'total': len(quality_data),
            'single_functions': 0,
            'header_files': 0,
            'large_files': 0,
            'medium_files': 0,
            'suitable_for_reduction': 0
        }
        
        suitable_samples = []
        
        for i, sample in enumerate(quality_data[:20]):  # 测试前20个样本
            func_before = sample['func_before_source']
            char_count = len(func_before)
            
            # 分类数据类型
            has_includes = '#include' in func_before
            has_header_guard = func_before.startswith('#ifndef') or func_before.startswith('#if')
            has_copyright = 'Copyright' in func_before
            
            if has_header_guard and char_count < 2000:
                stats['header_files'] += 1
                data_type = 'header'
            elif has_includes and char_count > 5000:
                stats['large_files'] += 1
                data_type = 'large'
            elif not has_includes and char_count < 1000:
                stats['single_functions'] += 1
                data_type = 'function'
            else:
                stats['medium_files'] += 1
                data_type = 'medium'
            
            # 判断是否适合reduction
            suitable = (
                char_count > 200 and          # 不能太短
                char_count < 10000 and        # 不能太长
                not (has_copyright and char_count > 5000)  # 避免版权信息过多的大文件
            )
            
            if suitable:
                stats['suitable_for_reduction'] += 1
                suitable_samples.append((i, sample, data_type))
            
            print(f"样本 {i+1:>2}: {char_count:>5}字符, 类型={data_type:>8}, 适合={suitable}")
        
        print(f"\n📈 统计结果:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print(f"\n适合率: {stats['suitable_for_reduction']/min(20, len(quality_data))*100:.1f}%")
        
        return suitable_samples[:5]  # 返回前5个适合的样本
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
        return []

def main():
    """主函数"""
    print("🎯 自建数据Reduction兼容性测试")
    print("=" * 50)
    
    # 1. 分析数据适合度
    suitable_samples = analyze_data_suitability()
    
    if not suitable_samples:
        print("❌ 没有找到适合的样本")
        return
    
    # 2. 测试reduction功能
    print(f"\n🧪 开始测试 {len(suitable_samples)} 个适合的样本...")
    
    success_count = 0
    for i, (sample_idx, sample, data_type) in enumerate(suitable_samples):
        try:
            success = test_single_sample_reduction(sample, f"{sample_idx+1} ({data_type})")
            if success:
                success_count += 1
        except Exception as e:
            print(f"❌ 样本 {sample_idx+1} 测试异常: {e}")
    
    # 3. 总结结果
    print(f"\n📋 测试总结:")
    print(f"  测试样本数: {len(suitable_samples)}")
    print(f"  成功样本数: {success_count}")
    print(f"  成功率: {success_count/len(suitable_samples)*100:.1f}%")
    
    if success_count > 0:
        print("\n✅ 结论: 你的数据可以用作reduction输入!")
        print("💡 建议:")
        print("  1. 优先使用中等大小的文件/函数 (200-5000字符)")
        print("  2. 避免过大的文件和过多的版权信息")
        print("  3. header文件也可以处理，但效果可能有限")
    else:
        print("\n❌ 结论: 数据可能不太适合当前的reduction算法")

if __name__ == "__main__":
    main() 