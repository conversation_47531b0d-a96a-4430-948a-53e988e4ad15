#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作流编排器 - 管理增强版PPatHF处理管道的完整工作流

核心功能：
1. 协调整个处理流程：数据适配 → 预处理 → Reduction → 推理 → 评估
2. 提供统一的配置管理和状态跟踪
3. 支持断点续传和流程恢复
4. 调用原工具的所有核心模块
5. 生成完整的处理报告和结果分析

技术要点：
- 模块化设计，每个步骤独立可控
- 智能的依赖管理和错误恢复
- 兼容原工具的所有配置和参数
- 详细的进度跟踪和性能监控

作者：AI助手
创建时间：2025年1月
版本：v1.0 (工作流编排器)
"""

import os
import sys
import json
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import traceback
from dataclasses import dataclass, asdict

@dataclass
class WorkflowConfig:
    """工作流配置类"""
    # 基础路径配置
    workspace_root: str = "/home/<USER>/nas-files/Re-paper/ppathf"
    custom_data_path: Optional[str] = None
    output_dir: Optional[str] = None
    
    # 处理参数
    max_records: Optional[int] = None  # 测试时限制记录数
    token_limits: List[int] = None  # 长度过滤限制
    
    # 原工具配置
    use_api_service: bool = True
    api_base_url: str = "http://************:5002"
    api_model_name: str = "starcoder-lora"
    
    # 流程控制
    skip_data_adaptation: bool = False  # 跳过数据适配（如果已完成）
    skip_preprocessing: bool = False
    skip_reduction: bool = False
    skip_inference: bool = False
    skip_evaluation: bool = False
    
    # 高级选项
    enable_slicing: bool = True  # 启用代码切片
    enable_recovery: bool = True  # 启用代码恢复
    tolerant_mode: bool = True   # 容错模式
    
    def __post_init__(self):
        if self.token_limits is None:
            self.token_limits = [2048, 4096, 8192]

@dataclass
class WorkflowState:
    """工作流状态类"""
    current_step: str = "initialized"
    completed_steps: List[str] = None
    failed_steps: List[str] = None
    generated_files: Dict[str, str] = None
    processing_stats: Dict[str, Any] = None
    start_time: Optional[datetime] = None
    last_update_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.completed_steps is None:
            self.completed_steps = []
        if self.failed_steps is None:
            self.failed_steps = []
        if self.generated_files is None:
            self.generated_files = {}
        if self.processing_stats is None:
            self.processing_stats = {}

class WorkflowOrchestrator:
    """
    工作流编排器
    
    管理增强版PPatHF处理管道的完整工作流
    """
    
    def __init__(self, config: WorkflowConfig):
        """
        初始化工作流编排器
        
        Args:
            config: 工作流配置
        """
        self.config = config
        self.state = WorkflowState()
        
        # 路径设置
        self.workspace_root = Path(config.workspace_root)
        self.src_enhanced_dir = self.workspace_root / "src_enhancedz"
        self.output_dir = Path(config.output_dir) if config.output_dir else self.src_enhanced_dir / "workflow_output"
        
        # 确保目录存在
        self.src_enhanced_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 状态文件
        self.state_file = self.output_dir / "workflow_state.json"
        
        # 设置日志
        self.setup_logging()
        
        # 加载之前的状态（如果存在）
        self.load_state()
        
        self.logger.info("🎭 工作流编排器初始化完成")
        self.logger.info(f"📁 输出目录: {self.output_dir}")
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / "workflow.log"
        
        # 创建logger
        self.logger = logging.getLogger('WorkflowOrchestrator')
        self.logger.setLevel(logging.INFO)
        
        # 避免重复添加handlers
        if not self.logger.handlers:
            # 文件handler
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def save_state(self):
        """保存工作流状态"""
        try:
            # 更新时间戳
            self.state.last_update_time = datetime.now()
            
            # 转换为可序列化的格式
            state_dict = asdict(self.state)
            
            # 处理datetime对象
            for key, value in state_dict.items():
                if isinstance(value, datetime):
                    state_dict[key] = value.isoformat()
            
            # 保存到文件
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_dict, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.debug(f"💾 工作流状态已保存: {self.state_file}")
        
        except Exception as e:
            self.logger.error(f"❌ 保存工作流状态失败: {e}")
    
    def load_state(self):
        """加载工作流状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_dict = json.load(f)
                
                # 恢复datetime对象
                if state_dict.get('start_time'):
                    state_dict['start_time'] = datetime.fromisoformat(state_dict['start_time'])
                if state_dict.get('last_update_time'):
                    state_dict['last_update_time'] = datetime.fromisoformat(state_dict['last_update_time'])
                
                # 重建状态对象
                self.state = WorkflowState(**state_dict)
                self.logger.info(f"📋 已加载工作流状态: {self.state.current_step}")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 加载工作流状态失败，使用默认状态: {e}")
            self.state = WorkflowState()
    
    def update_step(self, step_name: str, status: str = "running"):
        """更新当前步骤状态"""
        if status == "running":
            self.state.current_step = step_name
            self.logger.info(f"🔄 开始步骤: {step_name}")
        elif status == "completed":
            if step_name not in self.state.completed_steps:
                self.state.completed_steps.append(step_name)
            self.logger.info(f"✅ 完成步骤: {step_name}")
        elif status == "failed":
            if step_name not in self.state.failed_steps:
                self.state.failed_steps.append(step_name)
            self.logger.error(f"❌ 步骤失败: {step_name}")
        
        self.save_state()
    
    async def step_data_adaptation(self) -> str:
        """步骤1: 数据适配"""
        step_name = "data_adaptation"
        
        if self.config.skip_data_adaptation:
            self.logger.info("⏭️ 跳过数据适配步骤")
            # 如果跳过数据适配，使用用户提供的数据文件路径
            if self.config.custom_data_path:
                adapted_file_path = Path(self.config.custom_data_path)
                # 转换为绝对路径
                if not adapted_file_path.is_absolute():
                    adapted_file_path = self.workspace_root / adapted_file_path
                adapted_file = str(adapted_file_path.resolve())
                self.logger.info(f"📄 使用现有数据文件: {adapted_file}")
                return adapted_file
            else:
                self.logger.error("❌ 跳过数据适配但未提供数据文件路径")
                raise ValueError("跳过数据适配时必须提供custom_data_path参数")
        
        if step_name in self.state.completed_steps:
            self.logger.info("✅ 数据适配步骤已完成，跳过")
            return self.state.generated_files.get('adapted_data', '')
        
        try:
            self.update_step(step_name, "running")
            
            # 导入并使用增强数据处理器
            from enhanced_data_processor import EnhancedDataProcessor
            
            processor = EnhancedDataProcessor(
                workspace_root=self.config.workspace_root,
                custom_data_path=self.config.custom_data_path,
                output_base_dir=str(self.output_dir / "data_processing")
            )
            
            # 运行数据适配
            adapted_file = await processor.run_data_adaptation(self.config.max_records)
            
            # 更新状态
            self.state.generated_files['adapted_data'] = adapted_file
            self.update_step(step_name, "completed")
            
            return adapted_file
        
        except Exception as e:
            self.update_step(step_name, "failed")
            self.logger.error(f"❌ 数据适配失败: {e}")
            raise
    
    async def step_preprocessing(self, adapted_file: str) -> Dict[str, str]:
        """步骤2: 数据预处理"""
        step_name = "preprocessing"
        
        if self.config.skip_preprocessing:
            self.logger.info("⏭️ 跳过数据预处理步骤")
            return {}
        
        if step_name in self.state.completed_steps:
            self.logger.info("✅ 数据预处理步骤已完成，跳过")
            return self.state.generated_files.get('preprocessed_data', {})
        
        try:
            self.update_step(step_name, "running")
            
            # 导入并使用增强数据处理器
            from enhanced_data_processor import EnhancedDataProcessor
            
            processor = EnhancedDataProcessor(
                workspace_root=self.config.workspace_root,
                output_base_dir=str(self.output_dir / "data_processing")
            )
            
            # 运行预处理
            preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file, self.config.max_records)
            
            # 更新状态
            self.state.generated_files['preprocessed_data'] = preprocessed_files
            self.update_step(step_name, "completed")
            
            return preprocessed_files
        
        except Exception as e:
            self.update_step(step_name, "failed")
            self.logger.error(f"❌ 数据预处理失败: {e}")
            raise
    
    async def step_reduction(self, preprocessed_files: Dict[str, str]) -> Dict[str, str]:
        """步骤3: Reduction处理"""
        step_name = "reduction"
        
        if self.config.skip_reduction:
            self.logger.info("⏭️ 跳过Reduction处理步骤")
            return preprocessed_files
        
        if step_name in self.state.completed_steps:
            self.logger.info("✅ Reduction处理步骤已完成，跳过")
            return self.state.generated_files.get('reduced_data', {})
        
        try:
            self.update_step(step_name, "running")
            
            if not self.config.enable_slicing:
                self.logger.info("🔀 代码切片已禁用，跳过Reduction处理")
                self.state.generated_files['reduced_data'] = preprocessed_files
                self.update_step(step_name, "completed")
                return preprocessed_files
            
            # 导入并使用增强数据处理器
            from enhanced_data_processor import EnhancedDataProcessor
            
            processor = EnhancedDataProcessor(
                workspace_root=self.config.workspace_root,
                output_base_dir=str(self.output_dir / "data_processing")
            )
            
            # 运行reduction处理
            reduced_files = processor.apply_reduction_module(preprocessed_files)
            
            # 更新状态
            self.state.generated_files['reduced_data'] = reduced_files
            self.update_step(step_name, "completed")
            
            return reduced_files
        
        except Exception as e:
            self.update_step(step_name, "failed")
            self.logger.error(f"❌ Reduction处理失败: {e}")
            raise
    
    async def step_inference(self, data_files: Dict[str, str]) -> Dict[str, str]:
        """步骤4: 模型推理"""
        step_name = "inference"
        
        if self.config.skip_inference:
            self.logger.info("⏭️ 跳过模型推理步骤")
            return {}
        
        if step_name in self.state.completed_steps:
            self.logger.info("✅ 模型推理步骤已完成，跳过")
            return self.state.generated_files.get('inference_results', {})
        
        try:
            self.update_step(step_name, "running")
            
            # 导入推理管道
            from inference_pipeline import InferencePipeline
            
            pipeline = InferencePipeline(
                workspace_root=self.config.workspace_root,
                output_dir=str(self.output_dir / "inference"),
                config={
                    'api_base_url': self.config.api_base_url,
                    'api_model_name': self.config.api_model_name,
                    'use_api_service': self.config.use_api_service
                }
            )
            
            # 运行推理
            inference_results = pipeline.run_inference_on_datasets(data_files)
            
            # 更新状态
            self.state.generated_files['inference_results'] = inference_results
            self.update_step(step_name, "completed")
            
            return inference_results
        
        except Exception as e:
            self.update_step(step_name, "failed")
            self.logger.error(f"❌ 模型推理失败: {e}")
            raise
    
    async def step_evaluation(self, inference_results: Dict[str, str], data_files: Dict[str, str]) -> Dict[str, str]:
        """步骤5: 结果评估"""
        step_name = "evaluation"
        
        if self.config.skip_evaluation:
            self.logger.info("⏭️ 跳过结果评估步骤")
            return {}
        
        if step_name in self.state.completed_steps:
            self.logger.info("✅ 结果评估步骤已完成，跳过")
            return self.state.generated_files.get('evaluation_results', {})
        
        try:
            self.update_step(step_name, "running")
            
            # 导入评估管道
            from evaluation_pipeline import EvaluationPipeline
            
            pipeline = EvaluationPipeline(
                workspace_root=self.config.workspace_root,
                output_dir=str(self.output_dir / "evaluation"),
                config={
                    'enable_recovery': self.config.enable_recovery,
                    'tolerant_mode': self.config.tolerant_mode
                }
            )
            
            # 运行评估
            evaluation_results = pipeline.run_evaluation_on_results(
                inference_results, data_files
            )
            
            # 更新状态
            self.state.generated_files['evaluation_results'] = evaluation_results
            self.update_step(step_name, "completed")
            
            return evaluation_results
        
        except Exception as e:
            self.update_step(step_name, "failed")
            self.logger.error(f"❌ 结果评估失败: {e}")
            raise
    
    def generate_final_report(self) -> str:
        """生成最终的工作流报告"""
        try:
            # 计算总处理时间
            end_time = datetime.now()
            if self.state.start_time:
                total_time = (end_time - self.state.start_time).total_seconds()
            else:
                total_time = 0
            
            # 生成综合报告
            report = {
                'workflow_summary': {
                    'start_time': self.state.start_time.isoformat() if self.state.start_time else None,
                    'end_time': end_time.isoformat(),
                    'total_processing_time_seconds': total_time,
                    'completed_steps': self.state.completed_steps,
                    'failed_steps': self.state.failed_steps,
                    'final_status': 'completed' if not self.state.failed_steps else 'partial_failure'
                },
                'configuration': asdict(self.config),
                'generated_files': self.state.generated_files,
                'processing_statistics': self.state.processing_stats,
                'next_steps_recommendations': self._generate_next_steps_recommendations()
            }
            
            # 保存报告
            report_file = self.output_dir / "final_workflow_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"📋 最终工作流报告已生成: {report_file}")
            return str(report_file)
        
        except Exception as e:
            self.logger.error(f"❌ 生成最终报告失败: {e}")
            return ""
    
    def _generate_next_steps_recommendations(self) -> List[str]:
        """生成下一步建议"""
        recommendations = []
        
        if 'evaluation_results' in self.state.generated_files:
            recommendations.extend([
                "查看评估结果中的详细指标分析",
                "对比不同长度限制数据集的性能表现",
                "分析生成代码的质量和准确性"
            ])
        
        if 'inference_results' in self.state.generated_files:
            recommendations.extend([
                "手动检查部分生成的代码样本",
                "分析推理过程中的错误模式",
                "考虑调整API参数或模型配置"
            ])
        
        if self.state.failed_steps:
            recommendations.extend([
                f"检查并修复失败的步骤: {', '.join(self.state.failed_steps)}",
                "查看详细错误日志了解失败原因",
                "考虑调整配置参数重新运行失败步骤"
            ])
        
        recommendations.extend([
            "使用生成的数据训练或微调模型",
            "将结果与原论文或基准数据进行对比",
            "基于评估结果优化数据处理流程"
        ])
        
        return recommendations
    
    async def run_full_workflow(self) -> str:
        """运行完整的工作流"""
        try:
            self.state.start_time = datetime.now()
            self.update_step("workflow_started", "running")
            
            self.logger.info("🚀 开始执行完整工作流...")
            self.logger.info(f"📋 配置: {self.config}")
            
            # 步骤1: 数据适配
            adapted_file = await self.step_data_adaptation()
            
            # 步骤2: 数据预处理
            preprocessed_files = await self.step_preprocessing(adapted_file)
            
            # 步骤3: Reduction处理
            reduced_files = await self.step_reduction(preprocessed_files)
            
            # 选择用于推理的数据文件（优先使用reduced版本）
            inference_data = reduced_files if reduced_files else preprocessed_files
            
            # 步骤4: 模型推理
            inference_results = await self.step_inference(inference_data)
            
            # 步骤5: 结果评估
            evaluation_results = await self.step_evaluation(inference_results, inference_data)
            
            # 生成最终报告
            final_report = self.generate_final_report()
            
            self.update_step("workflow_completed", "completed")
            self.logger.info("🎉 完整工作流执行完成！")
            
            return final_report
        
        except Exception as e:
            self.update_step("workflow_failed", "failed")
            self.logger.error(f"❌ 工作流执行失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 即使失败也生成报告
            try:
                final_report = self.generate_final_report()
                return final_report
            except:
                return ""
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取工作流状态摘要"""
        return {
            'current_step': self.state.current_step,
            'completed_steps': self.state.completed_steps,
            'failed_steps': self.state.failed_steps,
            'progress_percentage': len(self.state.completed_steps) / 5 * 100,  # 5个主要步骤
            'generated_files_count': len(self.state.generated_files),
            'last_update': self.state.last_update_time.isoformat() if self.state.last_update_time else None
        }


async def main():
    """主程序入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="工作流编排器 - 管理增强版PPatHF处理管道")
    parser.add_argument("--config", "-c", help="配置文件路径（JSON格式）")
    parser.add_argument("--workspace", "-w", 
                       default="/home/<USER>/nas-files/Re-paper/ppathf",
                       help="工作空间根目录")
    parser.add_argument("--custom-data", "-d", help="自建数据文件路径")
    parser.add_argument("--output-dir", "-o", help="输出目录路径")
    parser.add_argument("--max-records", "-m", type=int, help="最大处理记录数（用于测试）")
    parser.add_argument("--skip-steps", nargs='+', 
                       choices=['data_adaptation', 'preprocessing', 'reduction', 'inference', 'evaluation'],
                       help="要跳过的步骤")
    
    args = parser.parse_args()
    
    # 创建配置
    if args.config and Path(args.config).exists():
        # 从配置文件加载
        with open(args.config, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        config = WorkflowConfig(**config_dict)
    else:
        # 使用命令行参数创建配置
        config = WorkflowConfig(
            workspace_root=args.workspace,
            custom_data_path=args.custom_data,
            output_dir=args.output_dir,
            max_records=args.max_records
        )
    
    # 应用跳过步骤设置
    if args.skip_steps:
        for step in args.skip_steps:
            setattr(config, f'skip_{step}', True)
    
    # 创建编排器
    orchestrator = WorkflowOrchestrator(config)
    
    # 执行工作流
    print("🎭 启动工作流编排器...")
    print(f"📁 输出目录: {orchestrator.output_dir}")
    
    final_report = await orchestrator.run_full_workflow()
    
    print(f"\n🎉 工作流完成！")
    print(f"📋 最终报告: {final_report}")
    print(f"📊 状态摘要: {orchestrator.get_status_summary()}")
    
    return final_report


if __name__ == "__main__":
    asyncio.run(main()) 