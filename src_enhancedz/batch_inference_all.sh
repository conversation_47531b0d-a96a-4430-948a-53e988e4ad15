#!/bin/bash

# 批量推理脚本 - 处理所有6个数据集版本
# 用法: nohup bash batch_inference_all.sh > batch_inference.log 2>&1 &

echo "======================================"
echo "🚀 开始批量推理处理"
echo "开始时间: $(date)"
echo "======================================"

# 工作目录
WORKSPACE="/home/<USER>/nas-files/Re-paper/ppathf"
cd $WORKSPACE

# API配置
API_URL="http://10.150.10.76:5005"

# 定义数据文件列表
declare -a DATASETS=(
    #"reproduction_work/data/organized/original/custom_vim_neovim_quality_2048.json:original:2048"
    #"reproduction_work/data/organized/original/custom_vim_neovim_quality_4096.json:original:4096"
    #"reproduction_work/data/organized/original/custom_vim_neovim_quality_8192.json:original:8192"
    #"reproduction_work/data/organized/sliced/custom_vim_neovim_quality_2048_sliced.json:sliced:2048"
    #"reproduction_work/data/organized/sliced/custom_vim_neovim_quality_4096_sliced.json:sliced:4096"
    "reproduction_work/data/organized/sliced/custom_vim_neovim_quality_8192_sliced.json:sliced:8192"
)

# 处理每个数据集
for dataset_info in "${DATASETS[@]}"; do
    # 解析数据集信息
    IFS=':' read -r data_file data_type size_version <<< "$dataset_info"
    
    echo ""
    echo "======================================"
    echo "🔄 处理: $data_type/$size_version"
    echo "数据文件: $data_file"
    echo "开始时间: $(date)"
    echo "======================================"
    
    # 设置输出目录和日志文件
    output_dir="reproduction_work/inference_results/$data_type"
    log_file="reproduction_work/logs/$data_type/inference_${size_version}.log"
    
    # 确保目录存在
    mkdir -p "$output_dir"
    mkdir -p "$(dirname "$log_file")"
    
    # 运行推理
    echo "🚀 开始推理..."
    python src_enhancedz/inference_pipeline.py \
        --data-files "$data_file" \
        --output-dir "$output_dir" \
        --use-api \
        --api-url "$API_URL" \
        2>&1 | tee "$log_file"
    
    # 检查执行结果
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        echo "✅ $data_type/$size_version 处理完成"
        echo "📋 结果保存在: $output_dir"
        echo "📝 日志保存在: $log_file"
    else
        echo "❌ $data_type/$size_version 处理失败"
        echo "📝 错误日志: $log_file"
    fi
    
    echo "完成时间: $(date)"
    echo ""
    
    # 短暂休息，避免API过载
    echo "⏳ 等待5秒..."
    sleep 5
done

echo "======================================"
echo "🎉 所有数据集处理完成!"
echo "结束时间: $(date)"
echo "======================================"

# 生成总结报告
echo ""
echo "📊 处理总结:"
echo "- Original 数据集结果目录: reproduction_work/inference_results/original/"
echo "- Sliced 数据集结果目录: reproduction_work/inference_results/sliced/"
echo "- 日志文件目录: reproduction_work/logs/"
echo ""

# 显示结果文件
echo "🔍 生成的推理结果文件:"
find reproduction_work/inference_results/ -name "*.json" -type f | sort

echo ""
echo "📝 日志文件:"
find reproduction_work/logs/ -name "*.log" -type f | sort 