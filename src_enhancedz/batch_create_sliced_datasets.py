#!/usr/bin/env python3
"""
为自建数据集创建Sliced版本
将quality_fixed数据集转换为带有reduction处理的sliced格式
"""

import json
import sys
import os
from pathlib import Path

# 添加PPatHF工具路径
sys.path.append('src/PPatHF-main/PPatHF-main')

try:
    from reduction.reducer import Reducer
    print("✅ 成功导入PPatHF reduction模块!")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def create_sliced_sample(sample, reducer):
    """为单个样本创建sliced版本"""
    
    # 复制原始数据
    sliced_sample = sample.copy()
    
    # 备份原始字段
    sliced_sample['func_before_source_origin'] = sample['func_before_source']
    sliced_sample['func_after_source_origin'] = sample['func_after_source']
    sliced_sample['func_before_target_origin'] = sample['func_before_target']
    sliced_sample['func_after_target_origin'] = sample['func_after_target']
    
    # 为每个函数字段进行reduction处理
    functions_to_slice = [
        ('func_before_source', 'func_before_sliced_source', 'removed_pieces_sliced_source'),
        ('func_after_source', 'func_after_sliced_source', 'removed_pieces_sliced_source'),
        ('func_before_target', 'func_before_sliced_target', 'removed_pieces_sliced_target'),
    ]
    
    for func_key, sliced_key, removed_key in functions_to_slice:
        if func_key in sample:
            func_code = sample[func_key]
            
            try:
                # 执行reduction处理
                sliced_code, removed_pieces = apply_reduction(func_code, reducer)
                
                sliced_sample[sliced_key] = sliced_code
                
                # 只在有移除内容时添加removed_pieces
                if removed_pieces and removed_key not in sliced_sample:
                    sliced_sample[removed_key] = removed_pieces
                
            except Exception as e:
                print(f"⚠️ {func_key} reduction失败: {e}")
                # 失败时使用原始代码
                sliced_sample[sliced_key] = func_code
                if removed_key not in sliced_sample:
                    sliced_sample[removed_key] = {}
    
    return sliced_sample

def apply_reduction(func_code, reducer):
    """应用reduction算法"""
    
    # 简化的reduction策略：
    # 1. 移除预处理指令
    # 2. 移除注释块
    # 3. 简化长的代码块
    
    reduced_code = func_code
    removed_pieces = {}
    placeholder_counter = 0
    
    try:
        # 1. 基础标准化
        reduced_code = reducer.func_standardization(func_code)
        
        # 2. 简单的代码块压缩策略
        lines = reduced_code.split('\n')
        processed_lines = []
        
        in_large_block = False
        current_block = []
        
        for line in lines:
            stripped = line.strip()
            
            # 检测大的代码块开始（例如长函数、大结构体等）
            if (len(stripped) > 100 or 
                (stripped.startswith('/*') and len(stripped) > 50) or
                (stripped.count(';') > 3)):
                
                if not in_large_block:
                    in_large_block = True
                    current_block = [line]
                else:
                    current_block.append(line)
            
            elif in_large_block and (stripped == '' or stripped == '}' or stripped.endswith('};')):
                # 结束大代码块
                current_block.append(line)
                
                if len(current_block) > 3:  # 只压缩较大的块
                    block_content = '\n'.join(current_block)
                    placeholder = f"/* placeholder_{placeholder_counter} */"
                    removed_pieces[str(placeholder_counter)] = block_content.strip()
                    processed_lines.append(f"    {placeholder}")
                    placeholder_counter += 1
                else:
                    processed_lines.extend(current_block)
                
                in_large_block = False
                current_block = []
                
            elif not in_large_block:
                processed_lines.append(line)
            else:
                current_block.append(line)
        
        # 处理未结束的块
        if in_large_block and current_block:
            if len(current_block) > 3:
                block_content = '\n'.join(current_block)
                placeholder = f"/* placeholder_{placeholder_counter} */"
                removed_pieces[str(placeholder_counter)] = block_content.strip()
                processed_lines.append(f"    {placeholder}")
            else:
                processed_lines.extend(current_block)
        
        reduced_code = '\n'.join(processed_lines)
        
    except Exception as e:
        print(f"⚠️ reduction处理异常: {e}")
        # 失败时返回原始代码
        reduced_code = func_code
        removed_pieces = {}
    
    return reduced_code, removed_pieces

def process_single_dataset(dataset_name, input_dir, output_dir):
    """处理单个数据集"""
    input_path = input_dir / f'custom_vim_neovim_quality_{dataset_name}.json'
    output_path = output_dir / f'custom_vim_neovim_quality_{dataset_name}_sliced.json'
    
    print(f"\n🎯 处理数据集: {dataset_name}")
    print("=" * 50)
    
    # 确保输出目录存在
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # 读取数据
        with open(input_path, 'r') as f:
            data = json.load(f)
        
        print(f"📊 加载数据: {len(data)} 个样本")
        
        # 初始化reducer
        reducer = Reducer(tolerant=True)
        
        # 处理数据
        sliced_data = []
        success_count = 0
        
        for i, sample in enumerate(data):
            try:
                print(f"🔄 处理样本 {i+1}/{len(data)}...", end='')
                
                sliced_sample = create_sliced_sample(sample, reducer)
                sliced_data.append(sliced_sample)
                success_count += 1
                
                print(" ✅")
                
                # 每100个样本显示一次进度
                if (i + 1) % 100 == 0:
                    print(f"📈 已处理: {i+1}/{len(data)} ({(i+1)/len(data)*100:.1f}%)")
                
            except Exception as e:
                print(f" ❌ 失败: {e}")
                # 失败时添加原始样本
                sliced_data.append(sample)
        
        # 保存结果
        print(f"\n💾 保存sliced数据集...")
        with open(output_path, 'w') as f:
            json.dump(sliced_data, f, indent=2, ensure_ascii=False)
        
        # 统计结果
        print(f"\n📋 {dataset_name} 处理完成!")
        print(f"  输入文件: {input_path}")
        print(f"  输出文件: {output_path}")
        print(f"  总样本数: {len(data)}")
        print(f"  成功处理: {success_count}")
        print(f"  成功率: {success_count/len(data)*100:.1f}%")
        print(f"  输出大小: {output_path.stat().st_size / 1024 / 1024:.1f} MB")
        
        # 验证输出格式
        print(f"\n🔍 验证输出格式...")
        first_sample = sliced_data[0]
        
        expected_fields = [
            'func_before_sliced_source', 'func_after_sliced_source', 
            'func_before_sliced_target', 'removed_pieces_sliced_source',
            'removed_pieces_sliced_target'
        ]
        
        for field in expected_fields:
            if field in first_sample:
                print(f"  ✅ {field}: OK")
            else:
                print(f"  ⚠️ {field}: 缺失")
        
        return {
            'dataset': dataset_name,
            'total_samples': len(data),
            'success_count': success_count,
            'success_rate': success_count/len(data)*100,
            'output_size_mb': output_path.stat().st_size / 1024 / 1024,
            'input_path': str(input_path),
            'output_path': str(output_path)
        }
        
    except Exception as e:
        print(f"❌ 处理 {dataset_name} 失败: {e}")
        return None

def main():
    """主函数 - 批量处理所有数据集"""
    print("🎯 批量创建自建数据集的Sliced版本")
    print("=" * 60)
    
    # 定义要处理的数据集
    datasets = ['2048', '4096', '8192', 'all']
    
    # 定义路径
    input_dir = Path('reproduction_work/data/organized/original')
    output_dir = Path('reproduction_work/data/organized/sliced')
    
    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 批量处理结果
    results = []
    
    print(f"📂 输入目录: {input_dir}")
    print(f"📂 输出目录: {output_dir}")
    print(f"📋 待处理数据集: {', '.join(datasets)}")
    
    for i, dataset in enumerate(datasets):
        print(f"\n{'='*60}")
        print(f"🔄 [{i+1}/{len(datasets)}] 开始处理: {dataset}")
        print(f"{'='*60}")
        
        result = process_single_dataset(dataset, input_dir, output_dir)
        
        if result:
            results.append(result)
            print(f"✅ {dataset} 处理完成!")
        else:
            print(f"❌ {dataset} 处理失败!")
    
    # 输出总结报告
    print(f"\n{'='*60}")
    print("📊 批量处理总结报告")
    print(f"{'='*60}")
    
    if results:
        print(f"✅ 成功处理: {len(results)}/{len(datasets)} 个数据集")
        print(f"\n📋 详细结果:")
        
        total_samples = 0
        total_success = 0
        total_size_mb = 0
        
        for result in results:
            print(f"  📄 {result['dataset']:>4}:")
            print(f"    样本数: {result['total_samples']:>6}")
            print(f"    成功率: {result['success_rate']:>6.1f}%")
            print(f"    大小: {result['output_size_mb']:>8.1f} MB")
            print(f"    输出: {result['output_path']}")
            
            total_samples += result['total_samples']
            total_success += result['success_count']
            total_size_mb += result['output_size_mb']
        
        print(f"\n📈 汇总统计:")
        print(f"  总样本数: {total_samples:,}")
        print(f"  总成功数: {total_success:,}")
        print(f"  平均成功率: {total_success/total_samples*100:.1f}%")
        print(f"  总输出大小: {total_size_mb:.1f} MB")
        
    else:
        print("❌ 没有成功处理任何数据集!")
    
    print(f"\n✅ 批量Sliced数据集创建完成!")
    print(f"💡 所有数据集现在都有对应的sliced版本，可以用于reduction-aware的训练和评估")

if __name__ == "__main__":
    main() 