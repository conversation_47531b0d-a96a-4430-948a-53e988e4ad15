[{"commit_id_source": "3ab68a9528780870b84200bbd91efaa47a586a3c", "file_path_source": "net/rose/rose_timer.c", "func_before_source": "/*\n * This program is free software; you can redistribute it and/or modify\n * it under the terms of the GNU General Public License as published by\n * the Free Software Foundation; either version 2 of the License, or\n * (at your option) any later version.\n *\n * Copyright (C) <PERSON> (<EMAIL>)\n * Copyright (C) 2002 <PERSON><PERSON> DO1GRB (<EMAIL>)\n */\n#include <linux/errno.h>\n#include <linux/types.h>\n#include <linux/socket.h>\n#include <linux/in.h>\n#include <linux/kernel.h>\n#include <linux/jiffies.h>\n#include <linux/timer.h>\n#include <linux/string.h>\n#include <linux/sockios.h>\n#include <linux/net.h>\n#include <net/ax25.h>\n#include <linux/inet.h>\n#include <linux/netdevice.h>\n#include <linux/skbuff.h>\n#include <net/sock.h>\n#include <net/tcp_states.h>\n#include <linux/fcntl.h>\n#include <linux/mm.h>\n#include <linux/interrupt.h>\n#include <net/rose.h>\n\nstatic void rose_heartbeat_expiry(unsigned long);\nstatic void rose_timer_expiry(unsigned long);\nstatic void rose_idletimer_expiry(unsigned long);\n\nvoid rose_start_heartbeat(struct sock *sk)\n{\n\tdel_timer(&sk->sk_timer);\n\n\tsk->sk_timer.data     = (unsigned long)sk;\n\tsk->sk_timer.function = &rose_heartbeat_expiry;\n\tsk->sk_timer.expires  = jiffies + 5 * HZ;\n\n\tadd_timer(&sk->sk_timer);\n}\n\nvoid rose_start_t1timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t1;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_t2timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t2;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_t3timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t3;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_hbtimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->hb;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_idletimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->idletimer);\n\n\tif (rose->idle > 0) {\n\t\trose->idletimer.data     = (unsigned long)sk;\n\t\trose->idletimer.function = &rose_idletimer_expiry;\n\t\trose->idletimer.expires  = jiffies + rose->idle;\n\n\t\tadd_timer(&rose->idletimer);\n\t}\n}\n\nvoid rose_stop_heartbeat(struct sock *sk)\n{\n\tdel_timer(&sk->sk_timer);\n}\n\nvoid rose_stop_timer(struct sock *sk)\n{\n\tdel_timer(&rose_sk(sk)->timer);\n}\n\nvoid rose_stop_idletimer(struct sock *sk)\n{\n\tdel_timer(&rose_sk(sk)->idletimer);\n}\n\nstatic void rose_heartbeat_expiry(unsigned long param)\n{\n\tstruct sock *sk = (struct sock *)param;\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_0:\n\t\t/* Magic here: If we listen() and a new link dies before it\n\t\t   is accepted() it isn't 'dead' so doesn't get removed. */\n\t\tif (sock_flag(sk, SOCK_DESTROY) ||\n\t\t    (sk->sk_state == TCP_LISTEN && sock_flag(sk, SOCK_DEAD))) {\n\t\t\tbh_unlock_sock(sk);\n\t\t\trose_destroy_socket(sk);\n\t\t\treturn;\n\t\t}\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\n\t\t/*\n\t\t * Check for the state of the receive buffer.\n\t\t */\n\t\tif (atomic_read(&sk->sk_rmem_alloc) < (sk->sk_rcvbuf / 2) &&\n\t\t    (rose->condition & ROSE_COND_OWN_RX_BUSY)) {\n\t\t\trose->condition &= ~ROSE_COND_OWN_RX_BUSY;\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose->vl         = rose->vr;\n\t\t\trose_write_internal(sk, ROSE_RR);\n\t\t\trose_stop_timer(sk);\t/* HB */\n\t\t\tbreak;\n\t\t}\n\t\tbreak;\n\t}\n\n\trose_start_heartbeat(sk);\n\tbh_unlock_sock(sk);\n}\n\nstatic void rose_timer_expiry(unsigned long param)\n{\n\tstruct sock *sk = (struct sock *)param;\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_1:\t/* T1 */\n\tcase ROSE_STATE_4:\t/* T2 */\n\t\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\t\trose->state = ROSE_STATE_2;\n\t\trose_start_t3timer(sk);\n\t\tbreak;\n\n\tcase ROSE_STATE_2:\t/* T3 */\n\t\trose->neighbour->use--;\n\t\trose_disconnect(sk, ETIMEDOUT, -1, -1);\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\t/* HB */\n\t\tif (rose->condition & ROSE_COND_ACK_PENDING) {\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose_enquiry_response(sk);\n\t\t}\n\t\tbreak;\n\t}\n\tbh_unlock_sock(sk);\n}\n\nstatic void rose_idletimer_expiry(unsigned long param)\n{\n\tstruct sock *sk = (struct sock *)param;\n\n\tbh_lock_sock(sk);\n\trose_clear_queues(sk);\n\n\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\trose_sk(sk)->state = ROSE_STATE_2;\n\n\trose_start_t3timer(sk);\n\n\tsk->sk_state     = TCP_CLOSE;\n\tsk->sk_err       = 0;\n\tsk->sk_shutdown |= SEND_SHUTDOWN;\n\n\tif (!sock_flag(sk, SOCK_DEAD)) {\n\t\tsk->sk_state_change(sk);\n\t\tsock_set_flag(sk, SOCK_DEAD);\n\t}\n\tbh_unlock_sock(sk);\n}\n", "func_after_source": "/*\n * This program is free software; you can redistribute it and/or modify\n * it under the terms of the GNU General Public License as published by\n * the Free Software Foundation; either version 2 of the License, or\n * (at your option) any later version.\n *\n * Copyright (C) <PERSON> (<EMAIL>)\n * Copyright (C) 2002 Ra<PERSON> DO1GRB (<EMAIL>)\n */\n#include <linux/errno.h>\n#include <linux/types.h>\n#include <linux/socket.h>\n#include <linux/in.h>\n#include <linux/kernel.h>\n#include <linux/jiffies.h>\n#include <linux/timer.h>\n#include <linux/string.h>\n#include <linux/sockios.h>\n#include <linux/net.h>\n#include <net/ax25.h>\n#include <linux/inet.h>\n#include <linux/netdevice.h>\n#include <linux/skbuff.h>\n#include <net/sock.h>\n#include <net/tcp_states.h>\n#include <linux/fcntl.h>\n#include <linux/mm.h>\n#include <linux/interrupt.h>\n#include <net/rose.h>\n\nstatic void rose_heartbeat_expiry(unsigned long);\nstatic void rose_timer_expiry(unsigned long);\nstatic void rose_idletimer_expiry(unsigned long);\n\nvoid rose_start_heartbeat(struct sock *sk)\n{\n\tsk_stop_timer(sk, &sk->sk_timer);\n\n\tsk->sk_timer.data     = (unsigned long)sk;\n\tsk->sk_timer.function = &rose_heartbeat_expiry;\n\tsk->sk_timer.expires  = jiffies + 5 * HZ;\n\n\tsk_reset_timer(sk, &sk->sk_timer, sk->sk_timer.expires);\n}\n\nvoid rose_start_t1timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t1;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_t2timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t2;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_t3timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t3;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_hbtimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.data     = (unsigned long)sk;\n\trose->timer.function = &rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->hb;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_idletimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\tif (rose->idle > 0) {\n\t\trose->idletimer.data     = (unsigned long)sk;\n\t\trose->idletimer.function = &rose_idletimer_expiry;\n\t\trose->idletimer.expires  = jiffies + rose->idle;\n\n\t\tsk_reset_timer(sk, &rose->idletimer, rose->idletimer.expires);\n\t}\n}\n\nvoid rose_stop_heartbeat(struct sock *sk)\n{\n\tsk_stop_timer(sk, &sk->sk_timer);\n}\n\nvoid rose_stop_timer(struct sock *sk)\n{\n\tsk_stop_timer(sk, &rose_sk(sk)->timer);\n}\n\nvoid rose_stop_idletimer(struct sock *sk)\n{\n\tsk_stop_timer(sk, &rose_sk(sk)->idletimer);\n}\n\nstatic void rose_heartbeat_expiry(unsigned long param)\n{\n\tstruct sock *sk = (struct sock *)param;\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_0:\n\t\t/* Magic here: If we listen() and a new link dies before it\n\t\t   is accepted() it isn't 'dead' so doesn't get removed. */\n\t\tif (sock_flag(sk, SOCK_DESTROY) ||\n\t\t    (sk->sk_state == TCP_LISTEN && sock_flag(sk, SOCK_DEAD))) {\n\t\t\tbh_unlock_sock(sk);\n\t\t\trose_destroy_socket(sk);\n\t\t\tsock_put(sk);\n\t\t\treturn;\n\t\t}\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\n\t\t/*\n\t\t * Check for the state of the receive buffer.\n\t\t */\n\t\tif (atomic_read(&sk->sk_rmem_alloc) < (sk->sk_rcvbuf / 2) &&\n\t\t    (rose->condition & ROSE_COND_OWN_RX_BUSY)) {\n\t\t\trose->condition &= ~ROSE_COND_OWN_RX_BUSY;\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose->vl         = rose->vr;\n\t\t\trose_write_internal(sk, ROSE_RR);\n\t\t\trose_stop_timer(sk);\t/* HB */\n\t\t\tbreak;\n\t\t}\n\t\tbreak;\n\t}\n\n\trose_start_heartbeat(sk);\n\tbh_unlock_sock(sk);\n\tsock_put(sk);\n}\n\nstatic void rose_timer_expiry(unsigned long param)\n{\n\tstruct sock *sk = (struct sock *)param;\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_1:\t/* T1 */\n\tcase ROSE_STATE_4:\t/* T2 */\n\t\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\t\trose->state = ROSE_STATE_2;\n\t\trose_start_t3timer(sk);\n\t\tbreak;\n\n\tcase ROSE_STATE_2:\t/* T3 */\n\t\trose->neighbour->use--;\n\t\trose_disconnect(sk, ETIMEDOUT, -1, -1);\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\t/* HB */\n\t\tif (rose->condition & ROSE_COND_ACK_PENDING) {\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose_enquiry_response(sk);\n\t\t}\n\t\tbreak;\n\t}\n\tbh_unlock_sock(sk);\n\tsock_put(sk);\n}\n\nstatic void rose_idletimer_expiry(unsigned long param)\n{\n\tstruct sock *sk = (struct sock *)param;\n\n\tbh_lock_sock(sk);\n\trose_clear_queues(sk);\n\n\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\trose_sk(sk)->state = ROSE_STATE_2;\n\n\trose_start_t3timer(sk);\n\n\tsk->sk_state     = TCP_CLOSE;\n\tsk->sk_err       = 0;\n\tsk->sk_shutdown |= SEND_SHUTDOWN;\n\n\tif (!sock_flag(sk, SOCK_DEAD)) {\n\t\tsk->sk_state_change(sk);\n\t\tsock_set_flag(sk, SOCK_DEAD);\n\t}\n\tbh_unlock_sock(sk);\n\tsock_put(sk);\n}\n", "diff_source": "@@ -34,95 +34,95 @@ static void rose_idletimer_expiry(unsigned long);\n \n void rose_start_heartbeat(struct sock *sk)\n {\n-\tdel_timer(&sk->sk_timer);\n+\tsk_stop_timer(sk, &sk->sk_timer);\n \n \tsk->sk_timer.data     = (unsigned long)sk;\n \tsk->sk_timer.function = &rose_heartbeat_expiry;\n \tsk->sk_timer.expires  = jiffies + 5 * HZ;\n \n-\tadd_timer(&sk->sk_timer);\n+\tsk_reset_timer(sk, &sk->sk_timer, sk->sk_timer.expires);\n }\n \n void rose_start_t1timer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.data     = (unsigned long)sk;\n \trose->timer.function = &rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->t1;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_t2timer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.data     = (unsigned long)sk;\n \trose->timer.function = &rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->t2;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_t3timer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.data     = (unsigned long)sk;\n \trose->timer.function = &rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->t3;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_hbtimer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.data     = (unsigned long)sk;\n \trose->timer.function = &rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->hb;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_idletimer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->idletimer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \tif (rose->idle > 0) {\n \t\trose->idletimer.data     = (unsigned long)sk;\n \t\trose->idletimer.function = &rose_idletimer_expiry;\n \t\trose->idletimer.expires  = jiffies + rose->idle;\n \n-\t\tadd_timer(&rose->idletimer);\n+\t\tsk_reset_timer(sk, &rose->idletimer, rose->idletimer.expires);\n \t}\n }\n \n void rose_stop_heartbeat(struct sock *sk)\n {\n-\tdel_timer(&sk->sk_timer);\n+\tsk_stop_timer(sk, &sk->sk_timer);\n }\n \n void rose_stop_timer(struct sock *sk)\n {\n-\tdel_timer(&rose_sk(sk)->timer);\n+\tsk_stop_timer(sk, &rose_sk(sk)->timer);\n }\n \n void rose_stop_idletimer(struct sock *sk)\n {\n-\tdel_timer(&rose_sk(sk)->idletimer);\n+\tsk_stop_timer(sk, &rose_sk(sk)->idletimer);\n }\n \n static void rose_heartbeat_expiry(unsigned long param)\n@@ -139,6 +139,7 @@ static void rose_heartbeat_expiry(unsigned long param)\n \t\t    (sk->sk_state == TCP_LISTEN && sock_flag(sk, SOCK_DEAD))) {\n \t\t\tbh_unlock_sock(sk);\n \t\t\trose_destroy_socket(sk);\n+\t\t\tsock_put(sk);\n \t\t\treturn;\n \t\t}\n \t\tbreak;\n@@ -161,6 +162,7 @@ static void rose_heartbeat_expiry(unsigned long param)\n \n \trose_start_heartbeat(sk);\n \tbh_unlock_sock(sk);\n+\tsock_put(sk);\n }\n \n static void rose_timer_expiry(unsigned long param)\n@@ -190,6 +192,7 @@ static void rose_timer_expiry(unsigned long param)\n \t\tbreak;\n \t}\n \tbh_unlock_sock(sk);\n+\tsock_put(sk);\n }\n \n static void rose_idletimer_expiry(unsigned long param)\n@@ -213,4 +216,5 @@ static void rose_idletimer_expiry(unsigned long param)\n \t\tsock_set_flag(sk, SOCK_DEAD);\n \t}\n \tbh_unlock_sock(sk);\n+\tsock_put(sk);\n }", "commit_id_target": "570b99c2e1508708c4a32a58f98071fbc3c2c351", "file_path_target": "net/rose/rose_timer.c", "func_before_target": "// SPDX-License-Identifier: GPL-2.0-or-later\n/*\n *\n * Copyright (C) <PERSON> (<EMAIL>)\n * Copyright (C) 2002 Ralf <PERSON> DO1GRB (<EMAIL>)\n */\n#include <linux/errno.h>\n#include <linux/types.h>\n#include <linux/socket.h>\n#include <linux/in.h>\n#include <linux/kernel.h>\n#include <linux/jiffies.h>\n#include <linux/timer.h>\n#include <linux/string.h>\n#include <linux/sockios.h>\n#include <linux/net.h>\n#include <net/ax25.h>\n#include <linux/inet.h>\n#include <linux/netdevice.h>\n#include <linux/skbuff.h>\n#include <net/sock.h>\n#include <net/tcp_states.h>\n#include <linux/fcntl.h>\n#include <linux/mm.h>\n#include <linux/interrupt.h>\n#include <net/rose.h>\n\nstatic void rose_heartbeat_expiry(struct timer_list *t);\nstatic void rose_timer_expiry(struct timer_list *);\nstatic void rose_idletimer_expiry(struct timer_list *);\n\nvoid rose_start_heartbeat(struct sock *sk)\n{\n\tdel_timer(&sk->sk_timer);\n\n\tsk->sk_timer.function = rose_heartbeat_expiry;\n\tsk->sk_timer.expires  = jiffies + 5 * HZ;\n\n\tadd_timer(&sk->sk_timer);\n}\n\nvoid rose_start_t1timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t1;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_t2timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t2;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_t3timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t3;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_hbtimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->hb;\n\n\tadd_timer(&rose->timer);\n}\n\nvoid rose_start_idletimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tdel_timer(&rose->idletimer);\n\n\tif (rose->idle > 0) {\n\t\trose->idletimer.function = rose_idletimer_expiry;\n\t\trose->idletimer.expires  = jiffies + rose->idle;\n\n\t\tadd_timer(&rose->idletimer);\n\t}\n}\n\nvoid rose_stop_heartbeat(struct sock *sk)\n{\n\tdel_timer(&sk->sk_timer);\n}\n\nvoid rose_stop_timer(struct sock *sk)\n{\n\tdel_timer(&rose_sk(sk)->timer);\n}\n\nvoid rose_stop_idletimer(struct sock *sk)\n{\n\tdel_timer(&rose_sk(sk)->idletimer);\n}\n\nstatic void rose_heartbeat_expiry(struct timer_list *t)\n{\n\tstruct sock *sk = from_timer(sk, t, sk_timer);\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_0:\n\t\t/* Magic here: If we listen() and a new link dies before it\n\t\t   is accepted() it isn't 'dead' so doesn't get removed. */\n\t\tif (sock_flag(sk, SOCK_DESTROY) ||\n\t\t    (sk->sk_state == TCP_LISTEN && sock_flag(sk, SOCK_DEAD))) {\n\t\t\tbh_unlock_sock(sk);\n\t\t\trose_destroy_socket(sk);\n\t\t\treturn;\n\t\t}\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\n\t\t/*\n\t\t * Check for the state of the receive buffer.\n\t\t */\n\t\tif (atomic_read(&sk->sk_rmem_alloc) < (sk->sk_rcvbuf / 2) &&\n\t\t    (rose->condition & ROSE_COND_OWN_RX_BUSY)) {\n\t\t\trose->condition &= ~ROSE_COND_OWN_RX_BUSY;\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose->vl         = rose->vr;\n\t\t\trose_write_internal(sk, ROSE_RR);\n\t\t\trose_stop_timer(sk);\t/* HB */\n\t\t\tbreak;\n\t\t}\n\t\tbreak;\n\t}\n\n\trose_start_heartbeat(sk);\n\tbh_unlock_sock(sk);\n}\n\nstatic void rose_timer_expiry(struct timer_list *t)\n{\n\tstruct rose_sock *rose = from_timer(rose, t, timer);\n\tstruct sock *sk = &rose->sock;\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_1:\t/* T1 */\n\tcase ROSE_STATE_4:\t/* T2 */\n\t\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\t\trose->state = ROSE_STATE_2;\n\t\trose_start_t3timer(sk);\n\t\tbreak;\n\n\tcase ROSE_STATE_2:\t/* T3 */\n\t\trose->neighbour->use--;\n\t\trose_disconnect(sk, ETIMEDOUT, -1, -1);\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\t/* HB */\n\t\tif (rose->condition & ROSE_COND_ACK_PENDING) {\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose_enquiry_response(sk);\n\t\t}\n\t\tbreak;\n\t}\n\tbh_unlock_sock(sk);\n}\n\nstatic void rose_idletimer_expiry(struct timer_list *t)\n{\n\tstruct rose_sock *rose = from_timer(rose, t, idletimer);\n\tstruct sock *sk = &rose->sock;\n\n\tbh_lock_sock(sk);\n\trose_clear_queues(sk);\n\n\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\trose_sk(sk)->state = ROSE_STATE_2;\n\n\trose_start_t3timer(sk);\n\n\tsk->sk_state     = TCP_CLOSE;\n\tsk->sk_err       = 0;\n\tsk->sk_shutdown |= SEND_SHUTDOWN;\n\n\tif (!sock_flag(sk, SOCK_DEAD)) {\n\t\tsk->sk_state_change(sk);\n\t\tsock_set_flag(sk, SOCK_DEAD);\n\t}\n\tbh_unlock_sock(sk);\n}\n", "func_after_target": "// SPDX-License-Identifier: GPL-2.0-or-later\n/*\n *\n * Copyright (C) <PERSON> (<EMAIL>)\n * Copyright (C) 2002 Ralf <PERSON> DO1GRB (<EMAIL>)\n */\n#include <linux/errno.h>\n#include <linux/types.h>\n#include <linux/socket.h>\n#include <linux/in.h>\n#include <linux/kernel.h>\n#include <linux/jiffies.h>\n#include <linux/timer.h>\n#include <linux/string.h>\n#include <linux/sockios.h>\n#include <linux/net.h>\n#include <net/ax25.h>\n#include <linux/inet.h>\n#include <linux/netdevice.h>\n#include <linux/skbuff.h>\n#include <net/sock.h>\n#include <net/tcp_states.h>\n#include <linux/fcntl.h>\n#include <linux/mm.h>\n#include <linux/interrupt.h>\n#include <net/rose.h>\n\nstatic void rose_heartbeat_expiry(struct timer_list *t);\nstatic void rose_timer_expiry(struct timer_list *);\nstatic void rose_idletimer_expiry(struct timer_list *);\n\nvoid rose_start_heartbeat(struct sock *sk)\n{\n\tsk_stop_timer(sk, &sk->sk_timer);\n\n\tsk->sk_timer.function = rose_heartbeat_expiry;\n\tsk->sk_timer.expires  = jiffies + 5 * HZ;\n\n\tsk_reset_timer(sk, &sk->sk_timer, sk->sk_timer.expires);\n}\n\nvoid rose_start_t1timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t1;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_t2timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t2;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_t3timer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->t3;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_hbtimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->timer);\n\n\trose->timer.function = rose_timer_expiry;\n\trose->timer.expires  = jiffies + rose->hb;\n\n\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n}\n\nvoid rose_start_idletimer(struct sock *sk)\n{\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tsk_stop_timer(sk, &rose->idletimer);\n\n\tif (rose->idle > 0) {\n\t\trose->idletimer.function = rose_idletimer_expiry;\n\t\trose->idletimer.expires  = jiffies + rose->idle;\n\n\t\tsk_reset_timer(sk, &rose->idletimer, rose->idletimer.expires);\n\t}\n}\n\nvoid rose_stop_heartbeat(struct sock *sk)\n{\n\tsk_stop_timer(sk, &sk->sk_timer);\n}\n\nvoid rose_stop_timer(struct sock *sk)\n{\n\tsk_stop_timer(sk, &rose_sk(sk)->timer);\n}\n\nvoid rose_stop_idletimer(struct sock *sk)\n{\n\tsk_stop_timer(sk, &rose_sk(sk)->idletimer);\n}\n\nstatic void rose_heartbeat_expiry(struct timer_list *t)\n{\n\tstruct sock *sk = from_timer(sk, t, sk_timer);\n\tstruct rose_sock *rose = rose_sk(sk);\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_0:\n\t\t/* Magic here: If we listen() and a new link dies before it\n\t\t   is accepted() it isn't 'dead' so doesn't get removed. */\n\t\tif (sock_flag(sk, SOCK_DESTROY) ||\n\t\t    (sk->sk_state == TCP_LISTEN && sock_flag(sk, SOCK_DEAD))) {\n\t\t\tbh_unlock_sock(sk);\n\t\t\trose_destroy_socket(sk);\n\t\t\tsock_put(sk);\n\t\t\treturn;\n\t\t}\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\n\t\t/*\n\t\t * Check for the state of the receive buffer.\n\t\t */\n\t\tif (atomic_read(&sk->sk_rmem_alloc) < (sk->sk_rcvbuf / 2) &&\n\t\t    (rose->condition & ROSE_COND_OWN_RX_BUSY)) {\n\t\t\trose->condition &= ~ROSE_COND_OWN_RX_BUSY;\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose->vl         = rose->vr;\n\t\t\trose_write_internal(sk, ROSE_RR);\n\t\t\trose_stop_timer(sk);\t/* HB */\n\t\t\tbreak;\n\t\t}\n\t\tbreak;\n\t}\n\n\trose_start_heartbeat(sk);\n\tbh_unlock_sock(sk);\n\tsock_put(sk);\n}\n\nstatic void rose_timer_expiry(struct timer_list *t)\n{\n\tstruct rose_sock *rose = from_timer(rose, t, timer);\n\tstruct sock *sk = &rose->sock;\n\n\tbh_lock_sock(sk);\n\tswitch (rose->state) {\n\tcase ROSE_STATE_1:\t/* T1 */\n\tcase ROSE_STATE_4:\t/* T2 */\n\t\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\t\trose->state = ROSE_STATE_2;\n\t\trose_start_t3timer(sk);\n\t\tbreak;\n\n\tcase ROSE_STATE_2:\t/* T3 */\n\t\trose->neighbour->use--;\n\t\trose_disconnect(sk, ETIMEDOUT, -1, -1);\n\t\tbreak;\n\n\tcase ROSE_STATE_3:\t/* HB */\n\t\tif (rose->condition & ROSE_COND_ACK_PENDING) {\n\t\t\trose->condition &= ~ROSE_COND_ACK_PENDING;\n\t\t\trose_enquiry_response(sk);\n\t\t}\n\t\tbreak;\n\t}\n\tbh_unlock_sock(sk);\n\tsock_put(sk);\n}\n\nstatic void rose_idletimer_expiry(struct timer_list *t)\n{\n\tstruct rose_sock *rose = from_timer(rose, t, idletimer);\n\tstruct sock *sk = &rose->sock;\n\n\tbh_lock_sock(sk);\n\trose_clear_queues(sk);\n\n\trose_write_internal(sk, ROSE_CLEAR_REQUEST);\n\trose_sk(sk)->state = ROSE_STATE_2;\n\n\trose_start_t3timer(sk);\n\n\tsk->sk_state     = TCP_CLOSE;\n\tsk->sk_err       = 0;\n\tsk->sk_shutdown |= SEND_SHUTDOWN;\n\n\tif (!sock_flag(sk, SOCK_DEAD)) {\n\t\tsk->sk_state_change(sk);\n\t\tsock_set_flag(sk, SOCK_DEAD);\n\t}\n\tbh_unlock_sock(sk);\n\tsock_put(sk);\n}\n", "diff_target": "@@ -31,89 +31,89 @@ static void rose_idletimer_expiry(struct timer_list *);\n \n void rose_start_heartbeat(struct sock *sk)\n {\n-\tdel_timer(&sk->sk_timer);\n+\tsk_stop_timer(sk, &sk->sk_timer);\n \n \tsk->sk_timer.function = rose_heartbeat_expiry;\n \tsk->sk_timer.expires  = jiffies + 5 * HZ;\n \n-\tadd_timer(&sk->sk_timer);\n+\tsk_reset_timer(sk, &sk->sk_timer, sk->sk_timer.expires);\n }\n \n void rose_start_t1timer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.function = rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->t1;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_t2timer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.function = rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->t2;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_t3timer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.function = rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->t3;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_hbtimer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->timer);\n+\tsk_stop_timer(sk, &rose->timer);\n \n \trose->timer.function = rose_timer_expiry;\n \trose->timer.expires  = jiffies + rose->hb;\n \n-\tadd_timer(&rose->timer);\n+\tsk_reset_timer(sk, &rose->timer, rose->timer.expires);\n }\n \n void rose_start_idletimer(struct sock *sk)\n {\n \tstruct rose_sock *rose = rose_sk(sk);\n \n-\tdel_timer(&rose->idletimer);\n+\tsk_stop_timer(sk, &rose->idletimer);\n \n \tif (rose->idle > 0) {\n \t\trose->idletimer.function = rose_idletimer_expiry;\n \t\trose->idletimer.expires  = jiffies + rose->idle;\n \n-\t\tadd_timer(&rose->idletimer);\n+\t\tsk_reset_timer(sk, &rose->idletimer, rose->idletimer.expires);\n \t}\n }\n \n void rose_stop_heartbeat(struct sock *sk)\n {\n-\tdel_timer(&sk->sk_timer);\n+\tsk_stop_timer(sk, &sk->sk_timer);\n }\n \n void rose_stop_timer(struct sock *sk)\n {\n-\tdel_timer(&rose_sk(sk)->timer);\n+\tsk_stop_timer(sk, &rose_sk(sk)->timer);\n }\n \n void rose_stop_idletimer(struct sock *sk)\n {\n-\tdel_timer(&rose_sk(sk)->idletimer);\n+\tsk_stop_timer(sk, &rose_sk(sk)->idletimer);\n }\n \n static void rose_heartbeat_expiry(struct timer_list *t)\n@@ -130,6 +130,7 @@ static void rose_heartbeat_expiry(struct timer_list *t)\n \t\t    (sk->sk_state == TCP_LISTEN && sock_flag(sk, SOCK_DEAD))) {\n \t\t\tbh_unlock_sock(sk);\n \t\t\trose_destroy_socket(sk);\n+\t\t\tsock_put(sk);\n \t\t\treturn;\n \t\t}\n \t\tbreak;\n@@ -152,6 +153,7 @@ static void rose_heartbeat_expiry(struct timer_list *t)\n \n \trose_start_heartbeat(sk);\n \tbh_unlock_sock(sk);\n+\tsock_put(sk);\n }\n \n static void rose_timer_expiry(struct timer_list *t)\n@@ -181,6 +183,7 @@ static void rose_timer_expiry(struct timer_list *t)\n \t\tbreak;\n \t}\n \tbh_unlock_sock(sk);\n+\tsock_put(sk);\n }\n \n static void rose_idletimer_expiry(struct timer_list *t)\n@@ -205,4 +208,5 @@ static void rose_idletimer_expiry(struct timer_list *t)\n \t\tsock_set_flag(sk, SOCK_DEAD);\n \t}\n \tbh_unlock_sock(sk);\n+\tsock_put(sk);\n }", "neovim_committer_date": "2022-07-07 15:54:47+0000", "original_unified_id": "CVE-2022-2318_Linux Kernel_Linux Kernel_aad32af3", "original_cve_id": "CVE-2022-2318", "original_patch_type": "same_repo"}, {"commit_id_source": "8dad9a67100245295373523375610be850999b37", "file_path_source": "drivers/block/xen-blkfront.c", "func_before_source": "", "func_after_source": "", "diff_source": "@@ -144,6 +144,10 @@ static unsigned int xen_blkif_max_ring_order;\n module_param_named(max_ring_page_order, xen_blkif_max_ring_order, int, S_IRUGO);\n MODULE_PARM_DESC(max_ring_page_order, \"Maximum order of pages to be used for the shared ring\");\n \n+static bool __read_mostly xen_blkif_trusted = true;\n+module_param_named(trusted, xen_blkif_trusted, bool, 0644);\n+MODULE_PARM_DESC(trusted, \"Is the backend trusted\");\n+\n #define BLK_RING_SIZE(info)\t\\\n \t__CONST_RING_SIZE(blkif, XEN_PAGE_SIZE * (info)->nr_ring_pages)\n \n@@ -206,6 +210,7 @@ struct blkfront_info\n \tunsigned int discard_granularity;\n \tunsigned int discard_alignment;\n \tunsigned int feature_persistent:1;\n+\tunsigned int bounce:1;\n \t/* Number of 4KB segments handled */\n \tunsigned int max_indirect_segments;\n \tint is_ready;\n@@ -296,7 +301,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \t\tif (!gnt_list_entry)\n \t\t\tgoto out_of_memory;\n \n-\t\tif (info->feature_persistent) {\n+\t\tif (info->bounce) {\n \t\t\tgranted_page = alloc_page(GFP_NOIO | __GFP_ZERO);\n \t\t\tif (!granted_page) {\n \t\t\t\tkfree(gnt_list_entry);\n@@ -316,7 +321,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \tlist_for_each_entry_safe(gnt_list_entry, n,\n \t                         &rinfo->grants, node) {\n \t\tlist_del(&gnt_list_entry->node);\n-\t\tif (info->feature_persistent)\n+\t\tif (info->bounce)\n \t\t\t__free_page(gnt_list_entry->page);\n \t\tkfree(gnt_list_entry);\n \t\ti--;\n@@ -362,7 +367,7 @@ static struct grant *get_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (info->feature_persistent)\n+\tif (info->bounce)\n \t\tgrant_foreign_access(gnt_list_entry, info);\n \telse {\n \t\t/* Grant access to the GFN passed by the caller */\n@@ -386,7 +391,7 @@ static struct grant *get_indirect_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (!info->feature_persistent) {\n+\tif (!info->bounce) {\n \t\tstruct page *indirect_page;\n \n \t\t/* Fetch a pre-allocated page to use for indirect grefs */\n@@ -701,7 +706,7 @@ static int blkif_queue_rw_req(struct request *req, struct blkfront_ring_info *ri\n \t\t.grant_idx = 0,\n \t\t.segments = NULL,\n \t\t.rinfo = rinfo,\n-\t\t.need_copy = rq_data_dir(req) && info->feature_persistent,\n+\t\t.need_copy = rq_data_dir(req) && info->bounce,\n \t};\n \n \t/*\n@@ -1015,11 +1020,12 @@ static void xlvbd_flush(struct blkfront_info *info)\n {\n \tblk_queue_write_cache(info->rq, info->feature_flush ? true : false,\n \t\t\t      info->feature_fua ? true : false);\n-\tpr_info(\"blkfront: %s: %s %s %s %s %s\\n\",\n+\tpr_info(\"blkfront: %s: %s %s %s %s %s %s %s\\n\",\n \t\tinfo->gd->disk_name, flush_info(info),\n \t\t\"persistent grants:\", info->feature_persistent ?\n \t\t\"enabled;\" : \"disabled;\", \"indirect descriptors:\",\n-\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\");\n+\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\",\n+\t\t\"bounce buffer:\", info->bounce ? \"enabled\" : \"disabled;\");\n }\n \n static int xen_translate_vdev(int vdevice, int *minor, unsigned int *offset)\n@@ -1254,7 +1260,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \tif (!list_empty(&rinfo->indirect_pages)) {\n \t\tstruct page *indirect_page, *n;\n \n-\t\tBUG_ON(info->feature_persistent);\n+\t\tBUG_ON(info->bounce);\n \t\tlist_for_each_entry_safe(indirect_page, n, &rinfo->indirect_pages, lru) {\n \t\t\tlist_del(&indirect_page->lru);\n \t\t\t__free_page(indirect_page);\n@@ -1271,7 +1277,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\t\t\tcontinue;\n \n \t\t\trinfo->persistent_gnts_c--;\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1291,7 +1297,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\tfor (j = 0; j < segs; j++) {\n \t\t\tpersistent_gnt = rinfo->shadow[i].grants_used[j];\n \t\t\tgnttab_end_foreign_access(persistent_gnt->gref, 0, 0UL);\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1481,7 +1487,7 @@ static int blkif_completion(unsigned long *id,\n \tdata.s = s;\n \tnum_sg = s->num_sg;\n \n-\tif (bret->operation == BLKIF_OP_READ && info->feature_persistent) {\n+\tif (bret->operation == BLKIF_OP_READ && info->bounce) {\n \t\tfor_each_sg(s->sg, sg, num_sg, i) {\n \t\t\tBUG_ON(sg->offset + sg->length > PAGE_SIZE);\n \n@@ -1540,7 +1546,7 @@ static int blkif_completion(unsigned long *id,\n \t\t\t\t * Add the used indirect page back to the list of\n \t\t\t\t * available pages for indirect grefs.\n \t\t\t\t */\n-\t\t\t\tif (!info->feature_persistent) {\n+\t\t\t\tif (!info->bounce) {\n \t\t\t\t\tindirect_page = s->indirect_grants[i]->page;\n \t\t\t\t\tlist_add(&indirect_page->lru, &rinfo->indirect_pages);\n \t\t\t\t}\n@@ -1822,6 +1828,13 @@ static int talk_to_blkback(struct xenbus_device *dev,\n \tint err;\n \tunsigned int i, max_page_order = 0;\n \tunsigned int ring_page_order = 0;\n+\tunsigned int trusted;\n+\n+\t/* Check if backend is trusted. */\n+\terr = xenbus_scanf(XBT_NIL, dev->nodename, \"trusted\", \"%u\", &trusted);\n+\tif (err < 0)\n+\t\ttrusted = 1;\n+\tinfo->bounce = !xen_blkif_trusted || !trusted;\n \n \terr = xenbus_scanf(XBT_NIL, info->xbdev->otherend,\n \t\t\t   \"max-ring-page-order\", \"%u\", &max_page_order);\n@@ -2301,10 +2314,10 @@ static int blkfront_setup_indirect(struct blkfront_ring_info *rinfo)\n \tif (err)\n \t\tgoto out_of_memory;\n \n-\tif (!info->feature_persistent && info->max_indirect_segments) {\n+\tif (!info->bounce && info->max_indirect_segments) {\n \t\t/*\n-\t\t * We are using indirect descriptors but not persistent\n-\t\t * grants, we need to allocate a set of pages that can be\n+\t\t * We are using indirect descriptors but don't have a bounce\n+\t\t * buffer, we need to allocate a set of pages that can be\n \t\t * used for mapping indirect grefs\n \t\t */\n \t\tint num = INDIRECT_GREFS(grants) * BLK_RING_SIZE(info);\n@@ -2410,6 +2423,8 @@ static void blkfront_gather_backend_features(struct blkfront_info *info)\n \t\tinfo->feature_persistent = 0;\n \telse\n \t\tinfo->feature_persistent = persistent;\n+\tif (info->feature_persistent)\n+\t\tinfo->bounce = true;\n \n \terr = xenbus_scanf(XBT_NIL, info->xbdev->otherend,\n \t\t\t   \"feature-max-indirect-segments\", \"%u\",", "commit_id_target": "3ebaa2c13f680889c4fb9f090b243499d25017d0", "file_path_target": "drivers/block/xen-blkfront.c", "func_before_target": "", "func_after_target": "", "diff_target": "@@ -152,6 +152,10 @@ static unsigned int xen_blkif_max_ring_order;\n module_param_named(max_ring_page_order, xen_blkif_max_ring_order, int, 0444);\n MODULE_PARM_DESC(max_ring_page_order, \"Maximum order of pages to be used for the shared ring\");\n \n+static bool __read_mostly xen_blkif_trusted = true;\n+module_param_named(trusted, xen_blkif_trusted, bool, 0644);\n+MODULE_PARM_DESC(trusted, \"Is the backend trusted\");\n+\n #define BLK_RING_SIZE(info)\t\\\n \t__CONST_RING_SIZE(blkif, XEN_PAGE_SIZE * (info)->nr_ring_pages)\n \n@@ -210,6 +214,7 @@ struct blkfront_info\n \tunsigned int feature_discard:1;\n \tunsigned int feature_secdiscard:1;\n \tunsigned int feature_persistent:1;\n+\tunsigned int bounce:1;\n \tunsigned int discard_granularity;\n \tunsigned int discard_alignment;\n \t/* Number of 4KB segments handled */\n@@ -312,7 +317,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \t\tif (!gnt_list_entry)\n \t\t\tgoto out_of_memory;\n \n-\t\tif (info->feature_persistent) {\n+\t\tif (info->bounce) {\n \t\t\tgranted_page = alloc_page(GFP_NOIO | __GFP_ZERO);\n \t\t\tif (!granted_page) {\n \t\t\t\tkfree(gnt_list_entry);\n@@ -332,7 +337,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \tlist_for_each_entry_safe(gnt_list_entry, n,\n \t                         &rinfo->grants, node) {\n \t\tlist_del(&gnt_list_entry->node);\n-\t\tif (info->feature_persistent)\n+\t\tif (info->bounce)\n \t\t\t__free_page(gnt_list_entry->page);\n \t\tkfree(gnt_list_entry);\n \t\ti--;\n@@ -378,7 +383,7 @@ static struct grant *get_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (info->feature_persistent)\n+\tif (info->bounce)\n \t\tgrant_foreign_access(gnt_list_entry, info);\n \telse {\n \t\t/* Grant access to the GFN passed by the caller */\n@@ -402,7 +407,7 @@ static struct grant *get_indirect_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (!info->feature_persistent) {\n+\tif (!info->bounce) {\n \t\tstruct page *indirect_page;\n \n \t\t/* Fetch a pre-allocated page to use for indirect grefs */\n@@ -705,7 +710,7 @@ static int blkif_queue_rw_req(struct request *req, struct blkfront_ring_info *ri\n \t\t.grant_idx = 0,\n \t\t.segments = NULL,\n \t\t.rinfo = rinfo,\n-\t\t.need_copy = rq_data_dir(req) && info->feature_persistent,\n+\t\t.need_copy = rq_data_dir(req) && info->bounce,\n \t};\n \n \t/*\n@@ -983,11 +988,12 @@ static void xlvbd_flush(struct blkfront_info *info)\n {\n \tblk_queue_write_cache(info->rq, info->feature_flush ? true : false,\n \t\t\t      info->feature_fua ? true : false);\n-\tpr_info(\"blkfront: %s: %s %s %s %s %s\\n\",\n+\tpr_info(\"blkfront: %s: %s %s %s %s %s %s %s\\n\",\n \t\tinfo->gd->disk_name, flush_info(info),\n \t\t\"persistent grants:\", info->feature_persistent ?\n \t\t\"enabled;\" : \"disabled;\", \"indirect descriptors:\",\n-\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\");\n+\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\",\n+\t\t\"bounce buffer:\", info->bounce ? \"enabled\" : \"disabled;\");\n }\n \n static int xen_translate_vdev(int vdevice, int *minor, unsigned int *offset)\n@@ -1209,7 +1215,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \tif (!list_empty(&rinfo->indirect_pages)) {\n \t\tstruct page *indirect_page, *n;\n \n-\t\tBUG_ON(info->feature_persistent);\n+\t\tBUG_ON(info->bounce);\n \t\tlist_for_each_entry_safe(indirect_page, n, &rinfo->indirect_pages, lru) {\n \t\t\tlist_del(&indirect_page->lru);\n \t\t\t__free_page(indirect_page);\n@@ -1226,7 +1232,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\t\t\t\t\t\t  0UL);\n \t\t\t\trinfo->persistent_gnts_c--;\n \t\t\t}\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1247,7 +1253,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\tfor (j = 0; j < segs; j++) {\n \t\t\tpersistent_gnt = rinfo->shadow[i].grants_used[j];\n \t\t\tgnttab_end_foreign_access(persistent_gnt->gref, 0UL);\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1437,7 +1443,7 @@ static int blkif_completion(unsigned long *id,\n \tdata.s = s;\n \tnum_sg = s->num_sg;\n \n-\tif (bret->operation == BLKIF_OP_READ && info->feature_persistent) {\n+\tif (bret->operation == BLKIF_OP_READ && info->bounce) {\n \t\tfor_each_sg(s->sg, sg, num_sg, i) {\n \t\t\tBUG_ON(sg->offset + sg->length > PAGE_SIZE);\n \n@@ -1496,7 +1502,7 @@ static int blkif_completion(unsigned long *id,\n \t\t\t\t * Add the used indirect page back to the list of\n \t\t\t\t * available pages for indirect grefs.\n \t\t\t\t */\n-\t\t\t\tif (!info->feature_persistent) {\n+\t\t\t\tif (!info->bounce) {\n \t\t\t\t\tindirect_page = s->indirect_grants[i]->page;\n \t\t\t\t\tlist_add(&indirect_page->lru, &rinfo->indirect_pages);\n \t\t\t\t}\n@@ -1787,6 +1793,10 @@ static int talk_to_blkback(struct xenbus_device *dev,\n \tif (!info)\n \t\treturn -ENODEV;\n \n+\t/* Check if backend is trusted. */\n+\tinfo->bounce = !xen_blkif_trusted ||\n+\t\t       !xenbus_read_unsigned(dev->nodename, \"trusted\", 1);\n+\n \tmax_page_order = xenbus_read_unsigned(info->xbdev->otherend,\n \t\t\t\t\t      \"max-ring-page-order\", 0);\n \tring_page_order = min(xen_blkif_max_ring_order, max_page_order);\n@@ -2196,10 +2206,10 @@ static int blkfront_setup_indirect(struct blkfront_ring_info *rinfo)\n \tif (err)\n \t\tgoto out_of_memory;\n \n-\tif (!info->feature_persistent && info->max_indirect_segments) {\n+\tif (!info->bounce && info->max_indirect_segments) {\n \t\t/*\n-\t\t * We are using indirect descriptors but not persistent\n-\t\t * grants, we need to allocate a set of pages that can be\n+\t\t * We are using indirect descriptors but don't have a bounce\n+\t\t * buffer, we need to allocate a set of pages that can be\n \t\t * used for mapping indirect grefs\n \t\t */\n \t\tint num = INDIRECT_GREFS(grants) * BLK_RING_SIZE(info);\n@@ -2300,6 +2310,8 @@ static void blkfront_gather_backend_features(struct blkfront_info *info)\n \t\tinfo->feature_persistent =\n \t\t\t!!xenbus_read_unsigned(info->xbdev->otherend,\n \t\t\t\t\t       \"feature-persistent\", 0);\n+\tif (info->feature_persistent)\n+\t\tinfo->bounce = true;\n \n \tindirect_segments = xenbus_read_unsigned(info->xbdev->otherend,\n \t\t\t\t\t\"feature-max-indirect-segments\", 0);\n@@ -2571,6 +2583,13 @@ static void blkfront_delay_work(struct work_struct *work)\n \tstruct blkfront_info *info;\n \tbool need_schedule_work = false;\n \n+\t/*\n+\t * Note that when using bounce buffers but not persistent grants\n+\t * there's no need to run blkfront_delay_work because grants are\n+\t * revoked in blkif_completion or else an error is reported and the\n+\t * connection is closed.\n+\t */\n+\n \tmutex_lock(&blkfront_mutex);\n \n \tlist_for_each_entry(info, &info_list, info_list) {", "neovim_committer_date": "2022-07-07 15:55:00+0000", "original_unified_id": "CVE-2022-33742_Linux Kernel_Linux Kernel_a6be3101", "original_cve_id": "CVE-2022-33742", "original_patch_type": "same_repo"}, {"commit_id_source": "856d1b8e6e826b5087f1ea3fdbabda3557d73599", "file_path_source": "arch/arm/xen/p2m.c", "func_before_source": "#include <linux/bootmem.h>\n#include <linux/gfp.h>\n#include <linux/export.h>\n#include <linux/rwlock.h>\n#include <linux/slab.h>\n#include <linux/types.h>\n#include <linux/dma-mapping.h>\n#include <linux/vmalloc.h>\n#include <linux/swiotlb.h>\n\n#include <xen/xen.h>\n#include <xen/interface/memory.h>\n#include <xen/page.h>\n#include <xen/swiotlb-xen.h>\n\n#include <asm/cacheflush.h>\n#include <asm/xen/hypercall.h>\n#include <asm/xen/interface.h>\n\nstruct xen_p2m_entry {\n\tunsigned long pfn;\n\tunsigned long mfn;\n\tunsigned long nr_pages;\n\tstruct rb_node rbnode_phys;\n};\n\nstatic rwlock_t p2m_lock;\nstruct rb_root phys_to_mach = RB_ROOT;\nEXPORT_SYMBOL_GPL(phys_to_mach);\n\nstatic int xen_add_phys_to_mach_entry(struct xen_p2m_entry *new)\n{\n\tstruct rb_node **link = &phys_to_mach.rb_node;\n\tstruct rb_node *parent = NULL;\n\tstruct xen_p2m_entry *entry;\n\tint rc = 0;\n\n\twhile (*link) {\n\t\tparent = *link;\n\t\tentry = rb_entry(parent, struct xen_p2m_entry, rbnode_phys);\n\n\t\tif (new->pfn == entry->pfn)\n\t\t\tgoto err_out;\n\n\t\tif (new->pfn < entry->pfn)\n\t\t\tlink = &(*link)->rb_left;\n\t\telse\n\t\t\tlink = &(*link)->rb_right;\n\t}\n\trb_link_node(&new->rbnode_phys, parent, link);\n\trb_insert_color(&new->rbnode_phys, &phys_to_mach);\n\tgoto out;\n\nerr_out:\n\trc = -EINVAL;\n\tpr_warn(\"%s: cannot add pfn=%pa -> mfn=%pa: pfn=%pa -> mfn=%pa already exists\\n\",\n\t\t\t__func__, &new->pfn, &new->mfn, &entry->pfn, &entry->mfn);\nout:\n\treturn rc;\n}\n\nunsigned long __pfn_to_mfn(unsigned long pfn)\n{\n\tstruct rb_node *n = phys_to_mach.rb_node;\n\tstruct xen_p2m_entry *entry;\n\tunsigned long irqflags;\n\n\tread_lock_irqsave(&p2m_lock, irqflags);\n\twhile (n) {\n\t\tentry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\tif (entry->pfn <= pfn &&\n\t\t\t\tentry->pfn + entry->nr_pages > pfn) {\n\t\t\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\treturn entry->mfn + (pfn - entry->pfn);\n\t\t}\n\t\tif (pfn < entry->pfn)\n\t\t\tn = n->rb_left;\n\t\telse\n\t\t\tn = n->rb_right;\n\t}\n\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\n\treturn INVALID_P2M_ENTRY;\n}\nEXPORT_SYMBOL_GPL(__pfn_to_mfn);\n\nint set_foreign_p2m_mapping(struct gnttab_map_grant_ref *map_ops,\n\t\t\t    struct gnttab_map_grant_ref *kmap_ops,\n\t\t\t    struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tstruct gnttab_unmap_grant_ref unmap;\n\t\tint rc;\n\n\t\tif (map_ops[i].status)\n\t\t\tcontinue;\n\t\tif (likely(set_phys_to_machine(map_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    map_ops[i].dev_bus_addr >> XEN_PAGE_SHIFT)))\n\t\t\tcontinue;\n\n\t\t/*\n\t\t * Signal an error for this slot. This in turn requires\n\t\t * immediate unmapping.\n\t\t */\n\t\tmap_ops[i].status = GNTST_general_error;\n\t\tunmap.host_addr = map_ops[i].host_addr,\n\t\tunmap.handle = map_ops[i].handle;\n\t\tmap_ops[i].handle = ~0;\n\t\tif (map_ops[i].flags & GNTMAP_device_map)\n\t\t\tunmap.dev_bus_addr = map_ops[i].dev_bus_addr;\n\t\telse\n\t\t\tunmap.dev_bus_addr = 0;\n\n\t\t/*\n\t\t * Pre-populate the status field, to be recognizable in\n\t\t * the log message below.\n\t\t */\n\t\tunmap.status = 1;\n\n\t\trc = HYPERVISOR_grant_table_op(GNTTABOP_unmap_grant_ref,\n\t\t\t\t\t       &unmap, 1);\n\t\tif (rc || unmap.status != GNTST_okay)\n\t\t\tpr_err_once(\"gnttab unmap failed: rc=%d st=%d\\n\",\n\t\t\t\t    rc, unmap.status);\n\t}\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(set_foreign_p2m_mapping);\n\nint clear_foreign_p2m_mapping(struct gnttab_unmap_grant_ref *unmap_ops,\n\t\t\t      struct gnttab_unmap_grant_ref *kunmap_ops,\n\t\t\t      struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tset_phys_to_machine(unmap_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    INVALID_P2M_ENTRY);\n\t}\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(clear_foreign_p2m_mapping);\n\nbool __set_phys_to_machine_multi(unsigned long pfn,\n\t\tunsigned long mfn, unsigned long nr_pages)\n{\n\tint rc;\n\tunsigned long irqflags;\n\tstruct xen_p2m_entry *p2m_entry;\n\tstruct rb_node *n = phys_to_mach.rb_node;\n\n\tif (mfn == INVALID_P2M_ENTRY) {\n\t\twrite_lock_irqsave(&p2m_lock, irqflags);\n\t\twhile (n) {\n\t\t\tp2m_entry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\t\tif (p2m_entry->pfn <= pfn &&\n\t\t\t\t\tp2m_entry->pfn + p2m_entry->nr_pages > pfn) {\n\t\t\t\trb_erase(&p2m_entry->rbnode_phys, &phys_to_mach);\n\t\t\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\t\tkfree(p2m_entry);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (pfn < p2m_entry->pfn)\n\t\t\t\tn = n->rb_left;\n\t\t\telse\n\t\t\t\tn = n->rb_right;\n\t\t}\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\treturn true;\n\t}\n\n\tp2m_entry = kzalloc(sizeof(struct xen_p2m_entry), GFP_NOWAIT);\n\tif (!p2m_entry) {\n\t\tpr_warn(\"cannot allocate xen_p2m_entry\\n\");\n\t\treturn false;\n\t}\n\tp2m_entry->pfn = pfn;\n\tp2m_entry->nr_pages = nr_pages;\n\tp2m_entry->mfn = mfn;\n\n\twrite_lock_irqsave(&p2m_lock, irqflags);\n\tif ((rc = xen_add_phys_to_mach_entry(p2m_entry)) < 0) {\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\treturn false;\n\t}\n\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\treturn true;\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine_multi);\n\nbool __set_phys_to_machine(unsigned long pfn, unsigned long mfn)\n{\n\treturn __set_phys_to_machine_multi(pfn, mfn, 1);\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine);\n\nstatic int p2m_init(void)\n{\n\trwlock_init(&p2m_lock);\n\treturn 0;\n}\narch_initcall(p2m_init);\n", "func_after_source": "#include <linux/bootmem.h>\n#include <linux/gfp.h>\n#include <linux/export.h>\n#include <linux/rwlock.h>\n#include <linux/slab.h>\n#include <linux/types.h>\n#include <linux/dma-mapping.h>\n#include <linux/vmalloc.h>\n#include <linux/swiotlb.h>\n\n#include <xen/xen.h>\n#include <xen/interface/memory.h>\n#include <xen/page.h>\n#include <xen/swiotlb-xen.h>\n\n#include <asm/cacheflush.h>\n#include <asm/xen/hypercall.h>\n#include <asm/xen/interface.h>\n\nstruct xen_p2m_entry {\n\tunsigned long pfn;\n\tunsigned long mfn;\n\tunsigned long nr_pages;\n\tstruct rb_node rbnode_phys;\n};\n\nstatic rwlock_t p2m_lock;\nstruct rb_root phys_to_mach = RB_ROOT;\nEXPORT_SYMBOL_GPL(phys_to_mach);\n\nstatic int xen_add_phys_to_mach_entry(struct xen_p2m_entry *new)\n{\n\tstruct rb_node **link = &phys_to_mach.rb_node;\n\tstruct rb_node *parent = NULL;\n\tstruct xen_p2m_entry *entry;\n\tint rc = 0;\n\n\twhile (*link) {\n\t\tparent = *link;\n\t\tentry = rb_entry(parent, struct xen_p2m_entry, rbnode_phys);\n\n\t\tif (new->pfn == entry->pfn)\n\t\t\tgoto err_out;\n\n\t\tif (new->pfn < entry->pfn)\n\t\t\tlink = &(*link)->rb_left;\n\t\telse\n\t\t\tlink = &(*link)->rb_right;\n\t}\n\trb_link_node(&new->rbnode_phys, parent, link);\n\trb_insert_color(&new->rbnode_phys, &phys_to_mach);\n\tgoto out;\n\nerr_out:\n\trc = -EINVAL;\n\tpr_warn(\"%s: cannot add pfn=%pa -> mfn=%pa: pfn=%pa -> mfn=%pa already exists\\n\",\n\t\t\t__func__, &new->pfn, &new->mfn, &entry->pfn, &entry->mfn);\nout:\n\treturn rc;\n}\n\nunsigned long __pfn_to_mfn(unsigned long pfn)\n{\n\tstruct rb_node *n;\n\tstruct xen_p2m_entry *entry;\n\tunsigned long irqflags;\n\n\tread_lock_irqsave(&p2m_lock, irqflags);\n\tn = phys_to_mach.rb_node;\n\twhile (n) {\n\t\tentry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\tif (entry->pfn <= pfn &&\n\t\t\t\tentry->pfn + entry->nr_pages > pfn) {\n\t\t\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\treturn entry->mfn + (pfn - entry->pfn);\n\t\t}\n\t\tif (pfn < entry->pfn)\n\t\t\tn = n->rb_left;\n\t\telse\n\t\t\tn = n->rb_right;\n\t}\n\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\n\treturn INVALID_P2M_ENTRY;\n}\nEXPORT_SYMBOL_GPL(__pfn_to_mfn);\n\nint set_foreign_p2m_mapping(struct gnttab_map_grant_ref *map_ops,\n\t\t\t    struct gnttab_map_grant_ref *kmap_ops,\n\t\t\t    struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tstruct gnttab_unmap_grant_ref unmap;\n\t\tint rc;\n\n\t\tif (map_ops[i].status)\n\t\t\tcontinue;\n\t\tif (likely(set_phys_to_machine(map_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    map_ops[i].dev_bus_addr >> XEN_PAGE_SHIFT)))\n\t\t\tcontinue;\n\n\t\t/*\n\t\t * Signal an error for this slot. This in turn requires\n\t\t * immediate unmapping.\n\t\t */\n\t\tmap_ops[i].status = GNTST_general_error;\n\t\tunmap.host_addr = map_ops[i].host_addr,\n\t\tunmap.handle = map_ops[i].handle;\n\t\tmap_ops[i].handle = ~0;\n\t\tif (map_ops[i].flags & GNTMAP_device_map)\n\t\t\tunmap.dev_bus_addr = map_ops[i].dev_bus_addr;\n\t\telse\n\t\t\tunmap.dev_bus_addr = 0;\n\n\t\t/*\n\t\t * Pre-populate the status field, to be recognizable in\n\t\t * the log message below.\n\t\t */\n\t\tunmap.status = 1;\n\n\t\trc = HYPERVISOR_grant_table_op(GNTTABOP_unmap_grant_ref,\n\t\t\t\t\t       &unmap, 1);\n\t\tif (rc || unmap.status != GNTST_okay)\n\t\t\tpr_err_once(\"gnttab unmap failed: rc=%d st=%d\\n\",\n\t\t\t\t    rc, unmap.status);\n\t}\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(set_foreign_p2m_mapping);\n\nint clear_foreign_p2m_mapping(struct gnttab_unmap_grant_ref *unmap_ops,\n\t\t\t      struct gnttab_unmap_grant_ref *kunmap_ops,\n\t\t\t      struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tset_phys_to_machine(unmap_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    INVALID_P2M_ENTRY);\n\t}\n\n\treturn 0;\n}\nEXPORT_SYMBOL_GPL(clear_foreign_p2m_mapping);\n\nbool __set_phys_to_machine_multi(unsigned long pfn,\n\t\tunsigned long mfn, unsigned long nr_pages)\n{\n\tint rc;\n\tunsigned long irqflags;\n\tstruct xen_p2m_entry *p2m_entry;\n\tstruct rb_node *n;\n\n\tif (mfn == INVALID_P2M_ENTRY) {\n\t\twrite_lock_irqsave(&p2m_lock, irqflags);\n\t\tn = phys_to_mach.rb_node;\n\t\twhile (n) {\n\t\t\tp2m_entry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\t\tif (p2m_entry->pfn <= pfn &&\n\t\t\t\t\tp2m_entry->pfn + p2m_entry->nr_pages > pfn) {\n\t\t\t\trb_erase(&p2m_entry->rbnode_phys, &phys_to_mach);\n\t\t\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\t\tkfree(p2m_entry);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (pfn < p2m_entry->pfn)\n\t\t\t\tn = n->rb_left;\n\t\t\telse\n\t\t\t\tn = n->rb_right;\n\t\t}\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\treturn true;\n\t}\n\n\tp2m_entry = kzalloc(sizeof(struct xen_p2m_entry), GFP_NOWAIT);\n\tif (!p2m_entry) {\n\t\tpr_warn(\"cannot allocate xen_p2m_entry\\n\");\n\t\treturn false;\n\t}\n\tp2m_entry->pfn = pfn;\n\tp2m_entry->nr_pages = nr_pages;\n\tp2m_entry->mfn = mfn;\n\n\twrite_lock_irqsave(&p2m_lock, irqflags);\n\tif ((rc = xen_add_phys_to_mach_entry(p2m_entry)) < 0) {\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\treturn false;\n\t}\n\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\treturn true;\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine_multi);\n\nbool __set_phys_to_machine(unsigned long pfn, unsigned long mfn)\n{\n\treturn __set_phys_to_machine_multi(pfn, mfn, 1);\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine);\n\nstatic int p2m_init(void)\n{\n\trwlock_init(&p2m_lock);\n\treturn 0;\n}\narch_initcall(p2m_init);\n", "diff_source": "@@ -61,11 +61,12 @@ static int xen_add_phys_to_mach_entry(struct xen_p2m_entry *new)\n \n unsigned long __pfn_to_mfn(unsigned long pfn)\n {\n-\tstruct rb_node *n = phys_to_mach.rb_node;\n+\tstruct rb_node *n;\n \tstruct xen_p2m_entry *entry;\n \tunsigned long irqflags;\n \n \tread_lock_irqsave(&p2m_lock, irqflags);\n+\tn = phys_to_mach.rb_node;\n \twhile (n) {\n \t\tentry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n \t\tif (entry->pfn <= pfn &&\n@@ -151,10 +152,11 @@ bool __set_phys_to_machine_multi(unsigned long pfn,\n \tint rc;\n \tunsigned long irqflags;\n \tstruct xen_p2m_entry *p2m_entry;\n-\tstruct rb_node *n = phys_to_mach.rb_node;\n+\tstruct rb_node *n;\n \n \tif (mfn == INVALID_P2M_ENTRY) {\n \t\twrite_lock_irqsave(&p2m_lock, irqflags);\n+\t\tn = phys_to_mach.rb_node;\n \t\twhile (n) {\n \t\t\tp2m_entry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n \t\t\tif (p2m_entry->pfn <= pfn &&", "commit_id_target": "efd9826d4c08abac7e8840757e3e1bfcf2876f70", "file_path_target": "arch/arm/xen/p2m.c", "func_before_target": "// SPDX-License-Identifier: GPL-2.0-only\n#include <linux/memblock.h>\n#include <linux/gfp.h>\n#include <linux/export.h>\n#include <linux/spinlock.h>\n#include <linux/slab.h>\n#include <linux/types.h>\n#include <linux/dma-mapping.h>\n#include <linux/vmalloc.h>\n#include <linux/swiotlb.h>\n\n#include <xen/xen.h>\n#include <xen/interface/memory.h>\n#include <xen/grant_table.h>\n#include <xen/page.h>\n#include <xen/swiotlb-xen.h>\n\n#include <asm/cacheflush.h>\n#include <asm/xen/hypercall.h>\n#include <asm/xen/interface.h>\n\nstruct xen_p2m_entry {\n\tunsigned long pfn;\n\tunsigned long mfn;\n\tunsigned long nr_pages;\n\tstruct rb_node rbnode_phys;\n};\n\nstatic rwlock_t p2m_lock;\nstruct rb_root phys_to_mach = RB_ROOT;\nEXPORT_SYMBOL_GPL(phys_to_mach);\n\nstatic int xen_add_phys_to_mach_entry(struct xen_p2m_entry *new)\n{\n\tstruct rb_node **link = &phys_to_mach.rb_node;\n\tstruct rb_node *parent = NULL;\n\tstruct xen_p2m_entry *entry;\n\tint rc = 0;\n\n\twhile (*link) {\n\t\tparent = *link;\n\t\tentry = rb_entry(parent, struct xen_p2m_entry, rbnode_phys);\n\n\t\tif (new->pfn == entry->pfn)\n\t\t\tgoto err_out;\n\n\t\tif (new->pfn < entry->pfn)\n\t\t\tlink = &(*link)->rb_left;\n\t\telse\n\t\t\tlink = &(*link)->rb_right;\n\t}\n\trb_link_node(&new->rbnode_phys, parent, link);\n\trb_insert_color(&new->rbnode_phys, &phys_to_mach);\n\tgoto out;\n\nerr_out:\n\trc = -EINVAL;\n\tpr_warn(\"%s: cannot add pfn=%pa -> mfn=%pa: pfn=%pa -> mfn=%pa already exists\\n\",\n\t\t\t__func__, &new->pfn, &new->mfn, &entry->pfn, &entry->mfn);\nout:\n\treturn rc;\n}\n\nunsigned long __pfn_to_mfn(unsigned long pfn)\n{\n\tstruct rb_node *n = phys_to_mach.rb_node;\n\tstruct xen_p2m_entry *entry;\n\tunsigned long irqflags;\n\n\tread_lock_irqsave(&p2m_lock, irqflags);\n\twhile (n) {\n\t\tentry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\tif (entry->pfn <= pfn &&\n\t\t\t\tentry->pfn + entry->nr_pages > pfn) {\n\t\t\tunsigned long mfn = entry->mfn + (pfn - entry->pfn);\n\t\t\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\treturn mfn;\n\t\t}\n\t\tif (pfn < entry->pfn)\n\t\t\tn = n->rb_left;\n\t\telse\n\t\t\tn = n->rb_right;\n\t}\n\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\n\treturn INVALID_P2M_ENTRY;\n}\nEXPORT_SYMBOL_GPL(__pfn_to_mfn);\n\nint set_foreign_p2m_mapping(struct gnttab_map_grant_ref *map_ops,\n\t\t\t    struct gnttab_map_grant_ref *kmap_ops,\n\t\t\t    struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tstruct gnttab_unmap_grant_ref unmap;\n\t\tint rc;\n\n\t\tif (map_ops[i].status)\n\t\t\tcontinue;\n\t\tif (likely(set_phys_to_machine(map_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    map_ops[i].dev_bus_addr >> XEN_PAGE_SHIFT)))\n\t\t\tcontinue;\n\n\t\t/*\n\t\t * Signal an error for this slot. This in turn requires\n\t\t * immediate unmapping.\n\t\t */\n\t\tmap_ops[i].status = GNTST_general_error;\n\t\tunmap.host_addr = map_ops[i].host_addr,\n\t\tunmap.handle = map_ops[i].handle;\n\t\tmap_ops[i].handle = INVALID_GRANT_HANDLE;\n\t\tif (map_ops[i].flags & GNTMAP_device_map)\n\t\t\tunmap.dev_bus_addr = map_ops[i].dev_bus_addr;\n\t\telse\n\t\t\tunmap.dev_bus_addr = 0;\n\n\t\t/*\n\t\t * Pre-populate the status field, to be recognizable in\n\t\t * the log message below.\n\t\t */\n\t\tunmap.status = 1;\n\n\t\trc = HYPERVISOR_grant_table_op(GNTTABOP_unmap_grant_ref,\n\t\t\t\t\t       &unmap, 1);\n\t\tif (rc || unmap.status != GNTST_okay)\n\t\t\tpr_err_once(\"gnttab unmap failed: rc=%d st=%d\\n\",\n\t\t\t\t    rc, unmap.status);\n\t}\n\n\treturn 0;\n}\n\nint clear_foreign_p2m_mapping(struct gnttab_unmap_grant_ref *unmap_ops,\n\t\t\t      struct gnttab_unmap_grant_ref *kunmap_ops,\n\t\t\t      struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tset_phys_to_machine(unmap_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    INVALID_P2M_ENTRY);\n\t}\n\n\treturn 0;\n}\n\nbool __set_phys_to_machine_multi(unsigned long pfn,\n\t\tunsigned long mfn, unsigned long nr_pages)\n{\n\tint rc;\n\tunsigned long irqflags;\n\tstruct xen_p2m_entry *p2m_entry;\n\tstruct rb_node *n = phys_to_mach.rb_node;\n\n\tif (mfn == INVALID_P2M_ENTRY) {\n\t\twrite_lock_irqsave(&p2m_lock, irqflags);\n\t\twhile (n) {\n\t\t\tp2m_entry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\t\tif (p2m_entry->pfn <= pfn &&\n\t\t\t\t\tp2m_entry->pfn + p2m_entry->nr_pages > pfn) {\n\t\t\t\trb_erase(&p2m_entry->rbnode_phys, &phys_to_mach);\n\t\t\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\t\tkfree(p2m_entry);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (pfn < p2m_entry->pfn)\n\t\t\t\tn = n->rb_left;\n\t\t\telse\n\t\t\t\tn = n->rb_right;\n\t\t}\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\treturn true;\n\t}\n\n\tp2m_entry = kzalloc(sizeof(*p2m_entry), GFP_NOWAIT);\n\tif (!p2m_entry)\n\t\treturn false;\n\n\tp2m_entry->pfn = pfn;\n\tp2m_entry->nr_pages = nr_pages;\n\tp2m_entry->mfn = mfn;\n\n\twrite_lock_irqsave(&p2m_lock, irqflags);\n\trc = xen_add_phys_to_mach_entry(p2m_entry);\n\tif (rc < 0) {\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\tkfree(p2m_entry);\n\t\treturn false;\n\t}\n\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\treturn true;\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine_multi);\n\nbool __set_phys_to_machine(unsigned long pfn, unsigned long mfn)\n{\n\treturn __set_phys_to_machine_multi(pfn, mfn, 1);\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine);\n\nstatic int p2m_init(void)\n{\n\trwlock_init(&p2m_lock);\n\treturn 0;\n}\narch_initcall(p2m_init);\n", "func_after_target": "// SPDX-License-Identifier: GPL-2.0-only\n#include <linux/memblock.h>\n#include <linux/gfp.h>\n#include <linux/export.h>\n#include <linux/spinlock.h>\n#include <linux/slab.h>\n#include <linux/types.h>\n#include <linux/dma-mapping.h>\n#include <linux/vmalloc.h>\n#include <linux/swiotlb.h>\n\n#include <xen/xen.h>\n#include <xen/interface/memory.h>\n#include <xen/grant_table.h>\n#include <xen/page.h>\n#include <xen/swiotlb-xen.h>\n\n#include <asm/cacheflush.h>\n#include <asm/xen/hypercall.h>\n#include <asm/xen/interface.h>\n\nstruct xen_p2m_entry {\n\tunsigned long pfn;\n\tunsigned long mfn;\n\tunsigned long nr_pages;\n\tstruct rb_node rbnode_phys;\n};\n\nstatic rwlock_t p2m_lock;\nstruct rb_root phys_to_mach = RB_ROOT;\nEXPORT_SYMBOL_GPL(phys_to_mach);\n\nstatic int xen_add_phys_to_mach_entry(struct xen_p2m_entry *new)\n{\n\tstruct rb_node **link = &phys_to_mach.rb_node;\n\tstruct rb_node *parent = NULL;\n\tstruct xen_p2m_entry *entry;\n\tint rc = 0;\n\n\twhile (*link) {\n\t\tparent = *link;\n\t\tentry = rb_entry(parent, struct xen_p2m_entry, rbnode_phys);\n\n\t\tif (new->pfn == entry->pfn)\n\t\t\tgoto err_out;\n\n\t\tif (new->pfn < entry->pfn)\n\t\t\tlink = &(*link)->rb_left;\n\t\telse\n\t\t\tlink = &(*link)->rb_right;\n\t}\n\trb_link_node(&new->rbnode_phys, parent, link);\n\trb_insert_color(&new->rbnode_phys, &phys_to_mach);\n\tgoto out;\n\nerr_out:\n\trc = -EINVAL;\n\tpr_warn(\"%s: cannot add pfn=%pa -> mfn=%pa: pfn=%pa -> mfn=%pa already exists\\n\",\n\t\t\t__func__, &new->pfn, &new->mfn, &entry->pfn, &entry->mfn);\nout:\n\treturn rc;\n}\n\nunsigned long __pfn_to_mfn(unsigned long pfn)\n{\n\tstruct rb_node *n;\n\tstruct xen_p2m_entry *entry;\n\tunsigned long irqflags;\n\n\tread_lock_irqsave(&p2m_lock, irqflags);\n\tn = phys_to_mach.rb_node;\n\twhile (n) {\n\t\tentry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\tif (entry->pfn <= pfn &&\n\t\t\t\tentry->pfn + entry->nr_pages > pfn) {\n\t\t\tunsigned long mfn = entry->mfn + (pfn - entry->pfn);\n\t\t\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\treturn mfn;\n\t\t}\n\t\tif (pfn < entry->pfn)\n\t\t\tn = n->rb_left;\n\t\telse\n\t\t\tn = n->rb_right;\n\t}\n\tread_unlock_irqrestore(&p2m_lock, irqflags);\n\n\treturn INVALID_P2M_ENTRY;\n}\nEXPORT_SYMBOL_GPL(__pfn_to_mfn);\n\nint set_foreign_p2m_mapping(struct gnttab_map_grant_ref *map_ops,\n\t\t\t    struct gnttab_map_grant_ref *kmap_ops,\n\t\t\t    struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tstruct gnttab_unmap_grant_ref unmap;\n\t\tint rc;\n\n\t\tif (map_ops[i].status)\n\t\t\tcontinue;\n\t\tif (likely(set_phys_to_machine(map_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    map_ops[i].dev_bus_addr >> XEN_PAGE_SHIFT)))\n\t\t\tcontinue;\n\n\t\t/*\n\t\t * Signal an error for this slot. This in turn requires\n\t\t * immediate unmapping.\n\t\t */\n\t\tmap_ops[i].status = GNTST_general_error;\n\t\tunmap.host_addr = map_ops[i].host_addr,\n\t\tunmap.handle = map_ops[i].handle;\n\t\tmap_ops[i].handle = INVALID_GRANT_HANDLE;\n\t\tif (map_ops[i].flags & GNTMAP_device_map)\n\t\t\tunmap.dev_bus_addr = map_ops[i].dev_bus_addr;\n\t\telse\n\t\t\tunmap.dev_bus_addr = 0;\n\n\t\t/*\n\t\t * Pre-populate the status field, to be recognizable in\n\t\t * the log message below.\n\t\t */\n\t\tunmap.status = 1;\n\n\t\trc = HYPERVISOR_grant_table_op(GNTTABOP_unmap_grant_ref,\n\t\t\t\t\t       &unmap, 1);\n\t\tif (rc || unmap.status != GNTST_okay)\n\t\t\tpr_err_once(\"gnttab unmap failed: rc=%d st=%d\\n\",\n\t\t\t\t    rc, unmap.status);\n\t}\n\n\treturn 0;\n}\n\nint clear_foreign_p2m_mapping(struct gnttab_unmap_grant_ref *unmap_ops,\n\t\t\t      struct gnttab_unmap_grant_ref *kunmap_ops,\n\t\t\t      struct page **pages, unsigned int count)\n{\n\tint i;\n\n\tfor (i = 0; i < count; i++) {\n\t\tset_phys_to_machine(unmap_ops[i].host_addr >> XEN_PAGE_SHIFT,\n\t\t\t\t    INVALID_P2M_ENTRY);\n\t}\n\n\treturn 0;\n}\n\nbool __set_phys_to_machine_multi(unsigned long pfn,\n\t\tunsigned long mfn, unsigned long nr_pages)\n{\n\tint rc;\n\tunsigned long irqflags;\n\tstruct xen_p2m_entry *p2m_entry;\n\tstruct rb_node *n;\n\n\tif (mfn == INVALID_P2M_ENTRY) {\n\t\twrite_lock_irqsave(&p2m_lock, irqflags);\n\t\tn = phys_to_mach.rb_node;\n\t\twhile (n) {\n\t\t\tp2m_entry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n\t\t\tif (p2m_entry->pfn <= pfn &&\n\t\t\t\t\tp2m_entry->pfn + p2m_entry->nr_pages > pfn) {\n\t\t\t\trb_erase(&p2m_entry->rbnode_phys, &phys_to_mach);\n\t\t\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\t\t\tkfree(p2m_entry);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (pfn < p2m_entry->pfn)\n\t\t\t\tn = n->rb_left;\n\t\t\telse\n\t\t\t\tn = n->rb_right;\n\t\t}\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\treturn true;\n\t}\n\n\tp2m_entry = kzalloc(sizeof(*p2m_entry), GFP_NOWAIT);\n\tif (!p2m_entry)\n\t\treturn false;\n\n\tp2m_entry->pfn = pfn;\n\tp2m_entry->nr_pages = nr_pages;\n\tp2m_entry->mfn = mfn;\n\n\twrite_lock_irqsave(&p2m_lock, irqflags);\n\trc = xen_add_phys_to_mach_entry(p2m_entry);\n\tif (rc < 0) {\n\t\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\t\tkfree(p2m_entry);\n\t\treturn false;\n\t}\n\twrite_unlock_irqrestore(&p2m_lock, irqflags);\n\treturn true;\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine_multi);\n\nbool __set_phys_to_machine(unsigned long pfn, unsigned long mfn)\n{\n\treturn __set_phys_to_machine_multi(pfn, mfn, 1);\n}\nEXPORT_SYMBOL_GPL(__set_phys_to_machine);\n\nstatic int p2m_init(void)\n{\n\trwlock_init(&p2m_lock);\n\treturn 0;\n}\narch_initcall(p2m_init);\n", "diff_target": "@@ -63,11 +63,12 @@ static int xen_add_phys_to_mach_entry(struct xen_p2m_entry *new)\n \n unsigned long __pfn_to_mfn(unsigned long pfn)\n {\n-\tstruct rb_node *n = phys_to_mach.rb_node;\n+\tstruct rb_node *n;\n \tstruct xen_p2m_entry *entry;\n \tunsigned long irqflags;\n \n \tread_lock_irqsave(&p2m_lock, irqflags);\n+\tn = phys_to_mach.rb_node;\n \twhile (n) {\n \t\tentry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n \t\tif (entry->pfn <= pfn &&\n@@ -152,10 +153,11 @@ bool __set_phys_to_machine_multi(unsigned long pfn,\n \tint rc;\n \tunsigned long irqflags;\n \tstruct xen_p2m_entry *p2m_entry;\n-\tstruct rb_node *n = phys_to_mach.rb_node;\n+\tstruct rb_node *n;\n \n \tif (mfn == INVALID_P2M_ENTRY) {\n \t\twrite_lock_irqsave(&p2m_lock, irqflags);\n+\t\tn = phys_to_mach.rb_node;\n \t\twhile (n) {\n \t\t\tp2m_entry = rb_entry(n, struct xen_p2m_entry, rbnode_phys);\n \t\t\tif (p2m_entry->pfn <= pfn &&", "neovim_committer_date": "2022-07-07 15:55:01+0000", "original_unified_id": "CVE-2022-33744_Linux Kernel_Linux Kernel_6ef5a99c", "original_cve_id": "CVE-2022-33744", "original_patch_type": "same_repo"}, {"commit_id_source": "181d8d2066c000ba0a0e6940a7ad80f1a0e68e9d", "file_path_source": "net/sctp/associola.c", "func_before_source": "", "func_after_source": "", "diff_source": "@@ -229,9 +229,8 @@ static struct sctp_association *sctp_association_init(\n \tif (!sctp_ulpq_init(&asoc->ulpq, asoc))\n \t\tgoto fail_init;\n \n-\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams,\n-\t\t\t     0, gfp))\n-\t\tgoto fail_init;\n+\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams, 0, gfp))\n+\t\tgoto stream_free;\n \n \t/* Initialize default path MTU. */\n \tasoc->pathmtu = sp->pathmtu;", "commit_id_target": "d99f144acc3b4b27ab91f78fd6d7085385ccd654", "file_path_target": "net/sctp/associola.c", "func_before_target": "", "func_after_target": "", "diff_target": "@@ -229,9 +229,8 @@ static struct sctp_association *sctp_association_init(\n \tif (!sctp_ulpq_init(&asoc->ulpq, asoc))\n \t\tgoto fail_init;\n \n-\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams,\n-\t\t\t     0, gfp))\n-\t\tgoto fail_init;\n+\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams, 0, gfp))\n+\t\tgoto stream_free;\n \n \t/* Initialize default path MTU. */\n \tasoc->pathmtu = sp->pathmtu;", "neovim_committer_date": "2022-08-03 10:05:28+0000", "original_unified_id": "CVE-2023-2177_Linux Kernel_Linux Kernel_8d3eba62", "original_cve_id": "CVE-2023-2177", "original_patch_type": "same_repo"}]