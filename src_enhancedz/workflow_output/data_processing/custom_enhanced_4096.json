[{"commit_id_source": "8dad9a67100245295373523375610be850999b37", "file_path_source": "drivers/block/xen-blkfront.c", "func_before_source": "", "func_after_source": "", "diff_source": "@@ -144,6 +144,10 @@ static unsigned int xen_blkif_max_ring_order;\n module_param_named(max_ring_page_order, xen_blkif_max_ring_order, int, S_IRUGO);\n MODULE_PARM_DESC(max_ring_page_order, \"Maximum order of pages to be used for the shared ring\");\n \n+static bool __read_mostly xen_blkif_trusted = true;\n+module_param_named(trusted, xen_blkif_trusted, bool, 0644);\n+MODULE_PARM_DESC(trusted, \"Is the backend trusted\");\n+\n #define BLK_RING_SIZE(info)\t\\\n \t__CONST_RING_SIZE(blkif, XEN_PAGE_SIZE * (info)->nr_ring_pages)\n \n@@ -206,6 +210,7 @@ struct blkfront_info\n \tunsigned int discard_granularity;\n \tunsigned int discard_alignment;\n \tunsigned int feature_persistent:1;\n+\tunsigned int bounce:1;\n \t/* Number of 4KB segments handled */\n \tunsigned int max_indirect_segments;\n \tint is_ready;\n@@ -296,7 +301,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \t\tif (!gnt_list_entry)\n \t\t\tgoto out_of_memory;\n \n-\t\tif (info->feature_persistent) {\n+\t\tif (info->bounce) {\n \t\t\tgranted_page = alloc_page(GFP_NOIO | __GFP_ZERO);\n \t\t\tif (!granted_page) {\n \t\t\t\tkfree(gnt_list_entry);\n@@ -316,7 +321,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \tlist_for_each_entry_safe(gnt_list_entry, n,\n \t                         &rinfo->grants, node) {\n \t\tlist_del(&gnt_list_entry->node);\n-\t\tif (info->feature_persistent)\n+\t\tif (info->bounce)\n \t\t\t__free_page(gnt_list_entry->page);\n \t\tkfree(gnt_list_entry);\n \t\ti--;\n@@ -362,7 +367,7 @@ static struct grant *get_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (info->feature_persistent)\n+\tif (info->bounce)\n \t\tgrant_foreign_access(gnt_list_entry, info);\n \telse {\n \t\t/* Grant access to the GFN passed by the caller */\n@@ -386,7 +391,7 @@ static struct grant *get_indirect_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (!info->feature_persistent) {\n+\tif (!info->bounce) {\n \t\tstruct page *indirect_page;\n \n \t\t/* Fetch a pre-allocated page to use for indirect grefs */\n@@ -701,7 +706,7 @@ static int blkif_queue_rw_req(struct request *req, struct blkfront_ring_info *ri\n \t\t.grant_idx = 0,\n \t\t.segments = NULL,\n \t\t.rinfo = rinfo,\n-\t\t.need_copy = rq_data_dir(req) && info->feature_persistent,\n+\t\t.need_copy = rq_data_dir(req) && info->bounce,\n \t};\n \n \t/*\n@@ -1015,11 +1020,12 @@ static void xlvbd_flush(struct blkfront_info *info)\n {\n \tblk_queue_write_cache(info->rq, info->feature_flush ? true : false,\n \t\t\t      info->feature_fua ? true : false);\n-\tpr_info(\"blkfront: %s: %s %s %s %s %s\\n\",\n+\tpr_info(\"blkfront: %s: %s %s %s %s %s %s %s\\n\",\n \t\tinfo->gd->disk_name, flush_info(info),\n \t\t\"persistent grants:\", info->feature_persistent ?\n \t\t\"enabled;\" : \"disabled;\", \"indirect descriptors:\",\n-\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\");\n+\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\",\n+\t\t\"bounce buffer:\", info->bounce ? \"enabled\" : \"disabled;\");\n }\n \n static int xen_translate_vdev(int vdevice, int *minor, unsigned int *offset)\n@@ -1254,7 +1260,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \tif (!list_empty(&rinfo->indirect_pages)) {\n \t\tstruct page *indirect_page, *n;\n \n-\t\tBUG_ON(info->feature_persistent);\n+\t\tBUG_ON(info->bounce);\n \t\tlist_for_each_entry_safe(indirect_page, n, &rinfo->indirect_pages, lru) {\n \t\t\tlist_del(&indirect_page->lru);\n \t\t\t__free_page(indirect_page);\n@@ -1271,7 +1277,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\t\t\tcontinue;\n \n \t\t\trinfo->persistent_gnts_c--;\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1291,7 +1297,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\tfor (j = 0; j < segs; j++) {\n \t\t\tpersistent_gnt = rinfo->shadow[i].grants_used[j];\n \t\t\tgnttab_end_foreign_access(persistent_gnt->gref, 0, 0UL);\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1481,7 +1487,7 @@ static int blkif_completion(unsigned long *id,\n \tdata.s = s;\n \tnum_sg = s->num_sg;\n \n-\tif (bret->operation == BLKIF_OP_READ && info->feature_persistent) {\n+\tif (bret->operation == BLKIF_OP_READ && info->bounce) {\n \t\tfor_each_sg(s->sg, sg, num_sg, i) {\n \t\t\tBUG_ON(sg->offset + sg->length > PAGE_SIZE);\n \n@@ -1540,7 +1546,7 @@ static int blkif_completion(unsigned long *id,\n \t\t\t\t * Add the used indirect page back to the list of\n \t\t\t\t * available pages for indirect grefs.\n \t\t\t\t */\n-\t\t\t\tif (!info->feature_persistent) {\n+\t\t\t\tif (!info->bounce) {\n \t\t\t\t\tindirect_page = s->indirect_grants[i]->page;\n \t\t\t\t\tlist_add(&indirect_page->lru, &rinfo->indirect_pages);\n \t\t\t\t}\n@@ -1822,6 +1828,13 @@ static int talk_to_blkback(struct xenbus_device *dev,\n \tint err;\n \tunsigned int i, max_page_order = 0;\n \tunsigned int ring_page_order = 0;\n+\tunsigned int trusted;\n+\n+\t/* Check if backend is trusted. */\n+\terr = xenbus_scanf(XBT_NIL, dev->nodename, \"trusted\", \"%u\", &trusted);\n+\tif (err < 0)\n+\t\ttrusted = 1;\n+\tinfo->bounce = !xen_blkif_trusted || !trusted;\n \n \terr = xenbus_scanf(XBT_NIL, info->xbdev->otherend,\n \t\t\t   \"max-ring-page-order\", \"%u\", &max_page_order);\n@@ -2301,10 +2314,10 @@ static int blkfront_setup_indirect(struct blkfront_ring_info *rinfo)\n \tif (err)\n \t\tgoto out_of_memory;\n \n-\tif (!info->feature_persistent && info->max_indirect_segments) {\n+\tif (!info->bounce && info->max_indirect_segments) {\n \t\t/*\n-\t\t * We are using indirect descriptors but not persistent\n-\t\t * grants, we need to allocate a set of pages that can be\n+\t\t * We are using indirect descriptors but don't have a bounce\n+\t\t * buffer, we need to allocate a set of pages that can be\n \t\t * used for mapping indirect grefs\n \t\t */\n \t\tint num = INDIRECT_GREFS(grants) * BLK_RING_SIZE(info);\n@@ -2410,6 +2423,8 @@ static void blkfront_gather_backend_features(struct blkfront_info *info)\n \t\tinfo->feature_persistent = 0;\n \telse\n \t\tinfo->feature_persistent = persistent;\n+\tif (info->feature_persistent)\n+\t\tinfo->bounce = true;\n \n \terr = xenbus_scanf(XBT_NIL, info->xbdev->otherend,\n \t\t\t   \"feature-max-indirect-segments\", \"%u\",", "commit_id_target": "3ebaa2c13f680889c4fb9f090b243499d25017d0", "file_path_target": "drivers/block/xen-blkfront.c", "func_before_target": "", "func_after_target": "", "diff_target": "@@ -152,6 +152,10 @@ static unsigned int xen_blkif_max_ring_order;\n module_param_named(max_ring_page_order, xen_blkif_max_ring_order, int, 0444);\n MODULE_PARM_DESC(max_ring_page_order, \"Maximum order of pages to be used for the shared ring\");\n \n+static bool __read_mostly xen_blkif_trusted = true;\n+module_param_named(trusted, xen_blkif_trusted, bool, 0644);\n+MODULE_PARM_DESC(trusted, \"Is the backend trusted\");\n+\n #define BLK_RING_SIZE(info)\t\\\n \t__CONST_RING_SIZE(blkif, XEN_PAGE_SIZE * (info)->nr_ring_pages)\n \n@@ -210,6 +214,7 @@ struct blkfront_info\n \tunsigned int feature_discard:1;\n \tunsigned int feature_secdiscard:1;\n \tunsigned int feature_persistent:1;\n+\tunsigned int bounce:1;\n \tunsigned int discard_granularity;\n \tunsigned int discard_alignment;\n \t/* Number of 4KB segments handled */\n@@ -312,7 +317,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \t\tif (!gnt_list_entry)\n \t\t\tgoto out_of_memory;\n \n-\t\tif (info->feature_persistent) {\n+\t\tif (info->bounce) {\n \t\t\tgranted_page = alloc_page(GFP_NOIO | __GFP_ZERO);\n \t\t\tif (!granted_page) {\n \t\t\t\tkfree(gnt_list_entry);\n@@ -332,7 +337,7 @@ static int fill_grant_buffer(struct blkfront_ring_info *rinfo, int num)\n \tlist_for_each_entry_safe(gnt_list_entry, n,\n \t                         &rinfo->grants, node) {\n \t\tlist_del(&gnt_list_entry->node);\n-\t\tif (info->feature_persistent)\n+\t\tif (info->bounce)\n \t\t\t__free_page(gnt_list_entry->page);\n \t\tkfree(gnt_list_entry);\n \t\ti--;\n@@ -378,7 +383,7 @@ static struct grant *get_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (info->feature_persistent)\n+\tif (info->bounce)\n \t\tgrant_foreign_access(gnt_list_entry, info);\n \telse {\n \t\t/* Grant access to the GFN passed by the caller */\n@@ -402,7 +407,7 @@ static struct grant *get_indirect_grant(grant_ref_t *gref_head,\n \t/* Assign a gref to this page */\n \tgnt_list_entry->gref = gnttab_claim_grant_reference(gref_head);\n \tBUG_ON(gnt_list_entry->gref == -ENOSPC);\n-\tif (!info->feature_persistent) {\n+\tif (!info->bounce) {\n \t\tstruct page *indirect_page;\n \n \t\t/* Fetch a pre-allocated page to use for indirect grefs */\n@@ -705,7 +710,7 @@ static int blkif_queue_rw_req(struct request *req, struct blkfront_ring_info *ri\n \t\t.grant_idx = 0,\n \t\t.segments = NULL,\n \t\t.rinfo = rinfo,\n-\t\t.need_copy = rq_data_dir(req) && info->feature_persistent,\n+\t\t.need_copy = rq_data_dir(req) && info->bounce,\n \t};\n \n \t/*\n@@ -983,11 +988,12 @@ static void xlvbd_flush(struct blkfront_info *info)\n {\n \tblk_queue_write_cache(info->rq, info->feature_flush ? true : false,\n \t\t\t      info->feature_fua ? true : false);\n-\tpr_info(\"blkfront: %s: %s %s %s %s %s\\n\",\n+\tpr_info(\"blkfront: %s: %s %s %s %s %s %s %s\\n\",\n \t\tinfo->gd->disk_name, flush_info(info),\n \t\t\"persistent grants:\", info->feature_persistent ?\n \t\t\"enabled;\" : \"disabled;\", \"indirect descriptors:\",\n-\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\");\n+\t\tinfo->max_indirect_segments ? \"enabled;\" : \"disabled;\",\n+\t\t\"bounce buffer:\", info->bounce ? \"enabled\" : \"disabled;\");\n }\n \n static int xen_translate_vdev(int vdevice, int *minor, unsigned int *offset)\n@@ -1209,7 +1215,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \tif (!list_empty(&rinfo->indirect_pages)) {\n \t\tstruct page *indirect_page, *n;\n \n-\t\tBUG_ON(info->feature_persistent);\n+\t\tBUG_ON(info->bounce);\n \t\tlist_for_each_entry_safe(indirect_page, n, &rinfo->indirect_pages, lru) {\n \t\t\tlist_del(&indirect_page->lru);\n \t\t\t__free_page(indirect_page);\n@@ -1226,7 +1232,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\t\t\t\t\t\t  0UL);\n \t\t\t\trinfo->persistent_gnts_c--;\n \t\t\t}\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1247,7 +1253,7 @@ static void blkif_free_ring(struct blkfront_ring_info *rinfo)\n \t\tfor (j = 0; j < segs; j++) {\n \t\t\tpersistent_gnt = rinfo->shadow[i].grants_used[j];\n \t\t\tgnttab_end_foreign_access(persistent_gnt->gref, 0UL);\n-\t\t\tif (info->feature_persistent)\n+\t\t\tif (info->bounce)\n \t\t\t\t__free_page(persistent_gnt->page);\n \t\t\tkfree(persistent_gnt);\n \t\t}\n@@ -1437,7 +1443,7 @@ static int blkif_completion(unsigned long *id,\n \tdata.s = s;\n \tnum_sg = s->num_sg;\n \n-\tif (bret->operation == BLKIF_OP_READ && info->feature_persistent) {\n+\tif (bret->operation == BLKIF_OP_READ && info->bounce) {\n \t\tfor_each_sg(s->sg, sg, num_sg, i) {\n \t\t\tBUG_ON(sg->offset + sg->length > PAGE_SIZE);\n \n@@ -1496,7 +1502,7 @@ static int blkif_completion(unsigned long *id,\n \t\t\t\t * Add the used indirect page back to the list of\n \t\t\t\t * available pages for indirect grefs.\n \t\t\t\t */\n-\t\t\t\tif (!info->feature_persistent) {\n+\t\t\t\tif (!info->bounce) {\n \t\t\t\t\tindirect_page = s->indirect_grants[i]->page;\n \t\t\t\t\tlist_add(&indirect_page->lru, &rinfo->indirect_pages);\n \t\t\t\t}\n@@ -1787,6 +1793,10 @@ static int talk_to_blkback(struct xenbus_device *dev,\n \tif (!info)\n \t\treturn -ENODEV;\n \n+\t/* Check if backend is trusted. */\n+\tinfo->bounce = !xen_blkif_trusted ||\n+\t\t       !xenbus_read_unsigned(dev->nodename, \"trusted\", 1);\n+\n \tmax_page_order = xenbus_read_unsigned(info->xbdev->otherend,\n \t\t\t\t\t      \"max-ring-page-order\", 0);\n \tring_page_order = min(xen_blkif_max_ring_order, max_page_order);\n@@ -2196,10 +2206,10 @@ static int blkfront_setup_indirect(struct blkfront_ring_info *rinfo)\n \tif (err)\n \t\tgoto out_of_memory;\n \n-\tif (!info->feature_persistent && info->max_indirect_segments) {\n+\tif (!info->bounce && info->max_indirect_segments) {\n \t\t/*\n-\t\t * We are using indirect descriptors but not persistent\n-\t\t * grants, we need to allocate a set of pages that can be\n+\t\t * We are using indirect descriptors but don't have a bounce\n+\t\t * buffer, we need to allocate a set of pages that can be\n \t\t * used for mapping indirect grefs\n \t\t */\n \t\tint num = INDIRECT_GREFS(grants) * BLK_RING_SIZE(info);\n@@ -2300,6 +2310,8 @@ static void blkfront_gather_backend_features(struct blkfront_info *info)\n \t\tinfo->feature_persistent =\n \t\t\t!!xenbus_read_unsigned(info->xbdev->otherend,\n \t\t\t\t\t       \"feature-persistent\", 0);\n+\tif (info->feature_persistent)\n+\t\tinfo->bounce = true;\n \n \tindirect_segments = xenbus_read_unsigned(info->xbdev->otherend,\n \t\t\t\t\t\"feature-max-indirect-segments\", 0);\n@@ -2571,6 +2583,13 @@ static void blkfront_delay_work(struct work_struct *work)\n \tstruct blkfront_info *info;\n \tbool need_schedule_work = false;\n \n+\t/*\n+\t * Note that when using bounce buffers but not persistent grants\n+\t * there's no need to run blkfront_delay_work because grants are\n+\t * revoked in blkif_completion or else an error is reported and the\n+\t * connection is closed.\n+\t */\n+\n \tmutex_lock(&blkfront_mutex);\n \n \tlist_for_each_entry(info, &info_list, info_list) {", "neovim_committer_date": "2022-07-07 15:55:00+0000", "original_unified_id": "CVE-2022-33742_Linux Kernel_Linux Kernel_a6be3101", "original_cve_id": "CVE-2022-33742", "original_patch_type": "same_repo"}, {"commit_id_source": "181d8d2066c000ba0a0e6940a7ad80f1a0e68e9d", "file_path_source": "net/sctp/associola.c", "func_before_source": "", "func_after_source": "", "diff_source": "@@ -229,9 +229,8 @@ static struct sctp_association *sctp_association_init(\n \tif (!sctp_ulpq_init(&asoc->ulpq, asoc))\n \t\tgoto fail_init;\n \n-\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams,\n-\t\t\t     0, gfp))\n-\t\tgoto fail_init;\n+\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams, 0, gfp))\n+\t\tgoto stream_free;\n \n \t/* Initialize default path MTU. */\n \tasoc->pathmtu = sp->pathmtu;", "commit_id_target": "d99f144acc3b4b27ab91f78fd6d7085385ccd654", "file_path_target": "net/sctp/associola.c", "func_before_target": "", "func_after_target": "", "diff_target": "@@ -229,9 +229,8 @@ static struct sctp_association *sctp_association_init(\n \tif (!sctp_ulpq_init(&asoc->ulpq, asoc))\n \t\tgoto fail_init;\n \n-\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams,\n-\t\t\t     0, gfp))\n-\t\tgoto fail_init;\n+\tif (sctp_stream_init(&asoc->stream, asoc->c.sinit_num_ostreams, 0, gfp))\n+\t\tgoto stream_free;\n \n \t/* Initialize default path MTU. */\n \tasoc->pathmtu = sp->pathmtu;", "neovim_committer_date": "2022-08-03 10:05:28+0000", "original_unified_id": "CVE-2023-2177_Linux Kernel_Linux Kernel_8d3eba62", "original_cve_id": "CVE-2023-2177", "original_patch_type": "same_repo"}]