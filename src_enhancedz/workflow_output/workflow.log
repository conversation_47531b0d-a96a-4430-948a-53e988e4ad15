2025-07-10 12:43:58,803 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 12:43:58,803 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 12:43:58,803 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 12:43:58,805 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 12:43:58,805 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='../reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 12:43:58,805 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 12:43:58,805 - WorkflowOrchestrator - INFO - 🔄 开始步骤: preprocessing
2025-07-10 12:44:05,265 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:44:05,267 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: [Errno 2] No such file or directory: ''
2025-07-10 12:44:05,267 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:44:05,269 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: [Errno 2] No such file or directory: ''
2025-07-10 12:44:05,269 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 500, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 275, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 182, in apply_original_tool_preprocessing
    with open(adapted_data_file, 'r', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: ''

2025-07-10 12:44:05,271 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:48:18,977 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: preprocessing
2025-07-10 12:48:18,978 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 12:48:18,978 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 12:48:18,978 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 12:48:18,979 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 12:48:18,979 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='../reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 12:48:18,979 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 12:48:18,979 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 12:48:18,979 - WorkflowOrchestrator - INFO - 🔄 开始步骤: preprocessing
2025-07-10 12:48:23,407 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:48:23,409 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:23,409 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:48:23,411 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:23,411 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 510, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 285, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 182, in apply_original_tool_preprocessing
    with open(adapted_data_file, 'r', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/ppathf/../reproduction_work/data/custom/enhanced_ppathf_adapted.json'

2025-07-10 12:48:23,413 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:48:56,559 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: preprocessing
2025-07-10 12:48:56,559 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 12:48:56,559 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 12:48:56,559 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 12:48:56,561 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 12:48:56,561 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='../reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 12:48:56,561 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 12:48:56,561 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 12:48:56,561 - WorkflowOrchestrator - INFO - 🔄 开始步骤: preprocessing
2025-07-10 12:48:58,874 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:48:58,875 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:58,875 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:48:58,877 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json'
2025-07-10 12:48:58,877 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 182, in apply_original_tool_preprocessing
    with open(adapted_data_file, 'r', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/nas-files/Re-paper/reproduction_work/data/custom/enhanced_ppathf_adapted.json'

2025-07-10 12:48:58,879 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:50:22,460 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: preprocessing
2025-07-10 12:50:22,461 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 12:50:22,461 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 12:50:22,461 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 12:50:22,463 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 12:50:22,463 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 12:50:22,463 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 12:50:22,464 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 12:50:22,464 - WorkflowOrchestrator - INFO - 🔄 开始步骤: preprocessing
2025-07-10 12:50:37,471 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:50:37,473 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: time data '2018-06-26T00:08:08Z' does not match format '%Y-%m-%d %H:%M:%S%z'
2025-07-10 12:50:37,473 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:50:37,475 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: time data '2018-06-26T00:08:08Z' does not match format '%Y-%m-%d %H:%M:%S%z'
2025-07-10 12:50:37,477 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 192, in apply_original_tool_preprocessing
    _, dataset = filter_by_date(dataset, split_date, "neovim_committer_date")
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 50, in filter_by_date
    dataset.sort(key=lambda x: datetime.strptime(x[date_key], DATE_FORMAT))
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 50, in <lambda>
    dataset.sort(key=lambda x: datetime.strptime(x[date_key], DATE_FORMAT))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/_strptime.py", line 568, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/_strptime.py", line 349, in _strptime
    raise ValueError("time data %r does not match format %r" %
ValueError: time data '2018-06-26T00:08:08Z' does not match format '%Y-%m-%d %H:%M:%S%z'

2025-07-10 12:50:37,478 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:51:40,354 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: preprocessing
2025-07-10 12:51:40,354 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 12:51:40,354 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 12:51:40,354 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 12:51:40,356 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 12:51:40,356 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 12:51:40,356 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 12:51:40,357 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 12:51:40,357 - WorkflowOrchestrator - INFO - 🔄 开始步骤: preprocessing
2025-07-10 12:51:49,348 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:51:49,349 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:51:49,349 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:51:49,351 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:51:49,355 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 216, in apply_original_tool_preprocessing
    filtered_dataset = filter_by_max_length(
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 28, in filter_by_max_length
    tokenizer = AutoTokenizer.from_pretrained(model_path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 643, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 487, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/utils/hub.py", line 417, in cached_file
    resolved_file = hf_hub_download(
                    ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.

2025-07-10 12:51:49,356 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:58:17,514 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: preprocessing
2025-07-10 12:58:17,514 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 12:58:17,514 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 12:58:17,514 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 12:58:17,516 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 12:58:17,516 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 12:58:17,516 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 12:58:17,517 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 12:58:17,517 - WorkflowOrchestrator - INFO - 🔄 开始步骤: preprocessing
2025-07-10 12:58:28,154 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: preprocessing
2025-07-10 12:58:28,157 - WorkflowOrchestrator - ERROR - ❌ 数据预处理失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:58:28,157 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 12:58:28,159 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.
2025-07-10 12:58:28,163 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 511, in run_full_workflow
    preprocessed_files = await self.step_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 286, in step_preprocessing
    preprocessed_files = processor.apply_original_tool_preprocessing(adapted_file)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 216, in apply_original_tool_preprocessing
    filtered_dataset = filter_by_max_length(
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/utils.py", line 28, in filter_by_max_length
    tokenizer = AutoTokenizer.from_pretrained(model_path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 643, in from_pretrained
    tokenizer_config = get_tokenizer_config(pretrained_model_name_or_path, **kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/models/auto/tokenization_auto.py", line 487, in get_tokenizer_config
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/transformers/utils/hub.py", line 417, in cached_file
    resolved_file = hf_hub_download(
                    ^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/nas-files/conda/miniconda3/miniconda3/lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/root/Star-Coder-Model/starcoder'. Use `repo_type` argument if needed.

2025-07-10 12:58:28,165 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 12:59:52,975 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: preprocessing
2025-07-10 12:59:52,975 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 12:59:52,975 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 12:59:52,975 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 12:59:52,977 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 12:59:52,977 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 12:59:52,977 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 12:59:52,978 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 12:59:52,979 - WorkflowOrchestrator - INFO - 🔄 开始步骤: preprocessing
2025-07-10 13:00:03,480 - WorkflowOrchestrator - INFO - ✅ 完成步骤: preprocessing
2025-07-10 13:00:03,482 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:00:03,489 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:00:03,491 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: Incompatible Language version 15. Must be between 13 and 14
2025-07-10 13:00:03,491 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:00:03,519 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: Incompatible Language version 15. Must be between 13 and 14
2025-07-10 13:00:03,520 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 29, in __new__
    cls._instance.parser.set_language(cls._instance.C_LANGUAGE)
ValueError: Incompatible Language version 15. Must be between 13 and 14

2025-07-10 13:00:03,523 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:00:23,286 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: reduction
2025-07-10 13:00:23,286 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 13:00:23,286 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 13:00:23,286 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 13:00:23,289 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 13:00:23,290 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=True, skip_reduction=True, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 13:00:23,290 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 13:00:23,290 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 13:00:23,290 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据预处理步骤
2025-07-10 13:00:23,290 - WorkflowOrchestrator - INFO - ⏭️ 跳过Reduction处理步骤
2025-07-10 13:00:23,290 - WorkflowOrchestrator - INFO - 🔄 开始步骤: inference
2025-07-10 13:00:23,604 - WorkflowOrchestrator - INFO - ✅ 完成步骤: inference
2025-07-10 13:00:23,605 - WorkflowOrchestrator - INFO - 🔄 开始步骤: evaluation
2025-07-10 13:00:24,027 - WorkflowOrchestrator - INFO - ✅ 完成步骤: evaluation
2025-07-10 13:00:24,027 - WorkflowOrchestrator - ERROR - ❌ 保存工作流状态失败: cannot pickle 'coroutine' object
2025-07-10 13:00:24,029 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:00:24,029 - WorkflowOrchestrator - INFO - ✅ 完成步骤: workflow_completed
2025-07-10 13:00:24,029 - WorkflowOrchestrator - ERROR - ❌ 保存工作流状态失败: cannot pickle 'coroutine' object
2025-07-10 13:00:24,029 - WorkflowOrchestrator - INFO - 🎉 完整工作流执行完成！
2025-07-10 13:14:07,815 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: evaluation
2025-07-10 13:14:07,815 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 13:14:07,815 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 13:14:07,815 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 13:14:07,817 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 13:14:07,817 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 13:14:07,817 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 13:14:07,818 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 13:14:07,818 - WorkflowOrchestrator - INFO - ✅ 数据预处理步骤已完成，跳过
2025-07-10 13:14:07,819 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:14:08,015 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:14:08,017 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: type object 'tree_sitter.Language' has no attribute 'build_library'
2025-07-10 13:14:08,017 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:14:08,019 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: type object 'tree_sitter.Language' has no attribute 'build_library'
2025-07-10 13:14:08,019 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 21, in __new__
    Language.build_library(
    ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'tree_sitter.Language' has no attribute 'build_library'

2025-07-10 13:14:08,021 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:15:16,927 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: reduction
2025-07-10 13:15:16,927 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 13:15:16,927 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 13:15:16,927 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 13:15:16,929 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 13:15:16,929 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 13:15:16,929 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 13:15:16,930 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 13:15:16,930 - WorkflowOrchestrator - INFO - ✅ 数据预处理步骤已完成，跳过
2025-07-10 13:15:16,930 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:15:17,072 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:15:17,074 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: type object 'tree_sitter.Language' has no attribute 'find'
2025-07-10 13:15:17,074 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:15:17,075 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: type object 'tree_sitter.Language' has no attribute 'find'
2025-07-10 13:15:17,076 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 21, in __new__
    cls._instance.C_LANGUAGE = Language.find("c")
                               ^^^^^^^^^^^^^
AttributeError: type object 'tree_sitter.Language' has no attribute 'find'

2025-07-10 13:15:17,078 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:21:00,285 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: reduction
2025-07-10 13:21:00,285 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 13:21:00,285 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 13:21:00,285 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 13:21:00,287 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 13:21:00,287 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 13:21:00,287 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 13:21:00,288 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 13:21:00,288 - WorkflowOrchestrator - INFO - ✅ 数据预处理步骤已完成，跳过
2025-07-10 13:21:00,288 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:21:00,481 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:21:00,483 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:21:00,483 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:21:00,484 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:21:00,485 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:21:00,487 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:22:58,643 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: reduction
2025-07-10 13:22:58,643 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 13:22:58,643 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 13:22:58,643 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 13:22:58,645 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 13:22:58,645 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 13:22:58,645 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 13:22:58,646 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 13:22:58,646 - WorkflowOrchestrator - INFO - ✅ 数据预处理步骤已完成，跳过
2025-07-10 13:22:58,646 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:22:58,852 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:22:58,854 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:22:58,855 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:22:58,856 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:22:58,857 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.parser = get_parser('c')
                           ^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 19, in tree_sitter_languages.core.get_parser
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:22:58,859 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:24:36,044 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: reduction
2025-07-10 13:24:36,045 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 13:24:36,045 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 13:24:36,045 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 13:24:36,047 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 13:24:36,047 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 13:24:36,047 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 13:24:36,048 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 13:24:36,048 - WorkflowOrchestrator - INFO - ✅ 数据预处理步骤已完成，跳过
2025-07-10 13:24:36,048 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:24:36,241 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:24:36,242 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:24:36,243 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:24:36,244 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:24:36,245 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:24:36,247 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
2025-07-10 13:27:21,247 - WorkflowOrchestrator - INFO - 📋 已加载工作流状态: reduction
2025-07-10 13:27:21,247 - WorkflowOrchestrator - INFO - 🎭 工作流编排器初始化完成
2025-07-10 13:27:21,247 - WorkflowOrchestrator - INFO - 📁 输出目录: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output
2025-07-10 13:27:21,247 - WorkflowOrchestrator - INFO - 🔄 开始步骤: workflow_started
2025-07-10 13:27:21,249 - WorkflowOrchestrator - INFO - 🚀 开始执行完整工作流...
2025-07-10 13:27:21,249 - WorkflowOrchestrator - INFO - 📋 配置: WorkflowConfig(workspace_root='/home/<USER>/nas-files/Re-paper/ppathf', custom_data_path='/home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json', output_dir=None, max_records=20, token_limits=[2048, 4096, 8192], use_api_service=True, api_base_url='http://************:5002', api_model_name='starcoder-lora', skip_data_adaptation=True, skip_preprocessing=False, skip_reduction=False, skip_inference=False, skip_evaluation=False, enable_slicing=True, enable_recovery=True, tolerant_mode=True)
2025-07-10 13:27:21,249 - WorkflowOrchestrator - INFO - ⏭️ 跳过数据适配步骤
2025-07-10 13:27:21,250 - WorkflowOrchestrator - INFO - 📄 使用现有数据文件: /home/<USER>/nas-files/Re-paper/ppathf/reproduction_work/data/custom/enhanced_ppathf_adapted.json
2025-07-10 13:27:21,250 - WorkflowOrchestrator - INFO - ✅ 数据预处理步骤已完成，跳过
2025-07-10 13:27:21,250 - WorkflowOrchestrator - INFO - 🔄 开始步骤: reduction
2025-07-10 13:27:21,448 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: reduction
2025-07-10 13:27:21,450 - WorkflowOrchestrator - ERROR - ❌ Reduction处理失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:27:21,451 - WorkflowOrchestrator - ERROR - ❌ 步骤失败: workflow_failed
2025-07-10 13:27:21,672 - WorkflowOrchestrator - ERROR - ❌ 工作流执行失败: __init__() takes exactly 1 argument (2 given)
2025-07-10 13:27:21,674 - WorkflowOrchestrator - ERROR - 错误详情: Traceback (most recent call last):
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 514, in run_full_workflow
    reduced_files = await self.step_reduction(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_orchestrator.py", line 329, in step_reduction
    reduced_files = processor.apply_reduction_module(preprocessed_files)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/enhanced_data_processor.py", line 329, in apply_reduction_module
    fcu = FunctionCompareUtilities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/nas-files/Re-paper/ppathf/src/PPatHF-main/PPatHF-main/reduction/fcu.py", line 22, in __new__
    cls._instance.C_LANGUAGE = get_language('c')
                               ^^^^^^^^^^^^^^^^^
  File "tree_sitter_languages/core.pyx", line 14, in tree_sitter_languages.core.get_language
TypeError: __init__() takes exactly 1 argument (2 given)

2025-07-10 13:27:21,676 - WorkflowOrchestrator - INFO - 📋 最终工作流报告已生成: /home/<USER>/nas-files/Re-paper/ppathf/src_enhancedz/workflow_output/final_workflow_report.json
